{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# fMRI Processing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Preparations\n", "\n", "initial setups"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["import os\n", "import datetime\n", "import numpy as np\n", "import nibabel as nib\n", "import scipy\n", "import scipy.signal as ss\n", "import scipy.io as io\n", "from nilearn import image\n", "from ipywidgets import interact\n", "from matplotlib import pyplot as plt\n", "%matplotlib inline\n", "\n", "from glob import glob\n", "from functions import find_files_with_string\n", "import pandas as pd\n", "from nilearn.image import resample_to_img\n", "from nilearn.glm import threshold_stats_img\n", "from nilearn.glm.first_level import make_first_level_design_matrix, FirstLevelModel\n", "from nilearn.plotting import plot_design_matrix, plot_roi, plot_contrast_matrix, plot_stat_map\n", "\n", "\n", "# 添加 FSL bin 路径到环境变量，便于调用fsl命令\n", "os.environ[\"FSLDIR\"] = \"/usr/local/fsl\"\n", "os.environ[\"PATH\"] += os.pathsep + \"/usr/local/fsl/bin\"\n"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [], "source": ["## initiate data id number\n", "base_dir = '/opt/data/private/lq/data/NSD/nsddata_rawdata'\n", "freesurfer_sub_id = 'sub-01'\n", "ses_id = 'ses-nsd01'\n", "run_id_fmap = 'run-03'\n", "run_id_func = 'run-01'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Move all mricron output files to ./nii_output before run next cell.\n", "Do not use to run the next cell if mricron does dicom2nii.gz"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["## set up input folders\n", "anat_input_dir = os.path.join(base_dir,freesurfer_sub_id, 'anat')\n", "field_input_dir = os.path.join(base_dir, freesurfer_sub_id, ses_id, 'fmap')\n", "func_input_dir = os.path.join(base_dir,freesurfer_sub_id, ses_id, 'func')\n", "\n", "## set up output folders\n", "field_output_dir = os.path.join(field_input_dir, 'field_output')\n", "anat_output_dir = os.path.join(anat_input_dir, 'anat_output')\n", "reg_output_dir = os.path.join(anat_input_dir, 'reg_output')"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [], "source": ["## check and create folders\n", "folders = [anat_input_dir, \n", "           field_input_dir, \n", "           field_output_dir, \n", "           anat_output_dir,\n", "           reg_output_dir\n", "           ]\n", "for i in folders:\n", "    path_tmp = os.path.join(os.getcwd(), i)\n", "    if not os.path.exists(path_tmp):\n", "        os.makedirs(path_tmp, exist_ok=True)"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/plain": ["['/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-04_magnitude1.nii.gz',\n", " '/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-02_magnitude1.nii.gz',\n", " '/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-01_magnitude1.nii.gz',\n", " '/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-03_magnitude1.nii.gz']"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["find_files_with_string(field_input_dir, 'magnitude1.nii.gz')"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-01_magnitude1.nii.gz\n", "/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-01_phasediff.nii.gz\n", "/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-02_magnitude1.nii.gz\n", "/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-02_phasediff.nii.gz\n", "/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-03_magnitude1.nii.gz\n", "/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-03_phasediff.nii.gz\n", "/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-04_magnitude1.nii.gz\n", "/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-04_phasediff.nii.gz\n", "/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-01_bold.nii.gz\n"]}], "source": ["## set up input filenames\n", "anat_T1_dir = os.path.join(anat_input_dir, 'T1.nii.gz')\n", "field_mag_dir = {}\n", "field_pha_dir = {}  \n", "for run_id_fmap_tmp in ['run-01', 'run-02', 'run-03', 'run-04']:\n", "    field_mag_dir[run_id_fmap_tmp] = os.path.join(field_input_dir, f\"{freesurfer_sub_id}_{ses_id}_{run_id_fmap_tmp}_magnitude1.nii.gz\")\n", "    field_pha_dir[run_id_fmap_tmp] = os.path.join(field_input_dir, f\"{freesurfer_sub_id}_{ses_id}_{run_id_fmap_tmp}_phasediff.nii.gz\")\n", "    print(field_mag_dir[run_id_fmap_tmp])\n", "    print(field_pha_dir[run_id_fmap_tmp])\n", "\n", "func_2d_dir = os.path.join(func_input_dir, f\"{freesurfer_sub_id}_{ses_id}_task-nsdcore_{run_id_func}_bold.nii.gz\")\n", "print(func_2d_dir)\n", "func_stc_mc_fm_1vol_dir = os.path.join(func_input_dir, 'task1', 'f_skip_stc_mc_fm_1vol.nii.gz')\n", "# ref downsampled_registered_func filename\n", "func_simptask_dir = os.path.join(func_input_dir, 'output_dir_simptask', 'f_simptask.nii.gz')"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [], "source": ["## set up output filenames\n", "field_brain1_dir = {}\n", "field_brain_dir =  {}\n", "field_fmap_dir = {}\n", "for run_id_fmap_tmp in ['run-01', 'run-02', 'run-03', 'run-04']:\n", "    field_brain1_dir[run_id_fmap_tmp] = os.path.join(field_input_dir, 'field_output', f'{run_id_fmap_tmp}_fmap_mag_brain1.nii.gz')\n", "    field_brain_dir[run_id_fmap_tmp] = os.path.join(field_input_dir, 'field_output',  f'{run_id_fmap_tmp}_fmap_mag_brain.nii.gz')\n", "    field_fmap_dir[run_id_fmap_tmp] = os.path.join(field_input_dir, 'field_output',  f'{run_id_fmap_tmp}_fmap_rads.nii.gz')\n", "\n", "\n", "anat_brain_dir = os.path.join(anat_output_dir,  'T1_brain.nii.gz')\n", "anat_wmseg_dir = os.path.join(anat_output_dir,  'T1_wmseg.nii.gz')\n", "anat_pve0_dir = os.path.join(anat_output_dir, 'T1_brain_pve_0.nii.gz')\n", "anat_pve1_dir = os.path.join(anat_output_dir,  'T1_brain_pve_1.nii.gz')\n", "anat_pve2_dir = os.path.join(anat_output_dir,  'T1_brain_pve_2.nii.gz')\n", "\n", "singlevol_dir = os.path.join(func_input_dir, f\"{freesurfer_sub_id}_{ses_id}_task-nsdcore_{run_id_func}_bold_singlevol.nii.gz\")\n", "tcustom_dir = os.path.join(base_dir, 'tcustom_epi.txt')\n", "mask_global_dir = os.path.join(anat_output_dir, 'global_mask.nii.gz')\n", "anat_gm_mask_dir = os.path.join(anat_output_dir, \"T1_gm_mask.nii.gz\")\n", "register_dir = os.path.join(reg_output_dir, 'register.dof6.dat')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# USE this to check the alignment between func_2d and fieldmap, with different parameters though.\n", "# cmd = f\"fslroi {func_2d_dir} {singlevol_dir} 0 1\"\n", "# os.system(cmd)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Get tcustom.txt when preparing for the first time of a run\n", "The tcustom.txt can be reused for data with the same epi sequence."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/opt/data/private/lq/data/NSD/nsddata_rawdata/tcustom_epi.txt\n", "/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func\n"]}], "source": ["print(tcustom_dir)\n", "print(func_input_dir)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "run:  0 ---------\n", "Effective_echo_spacing:  0.000329994\n", "tr:  1.6\n", "slicetiming in json:  [0, 0.7475, 1.4925, 0.6325, 1.3775, 0.5175, 1.2625, 0.4025, 1.1475, 0.2875, 1.0325, 0.1725, 0.9175, 0.0575, 0.8025, 1.55, 0.69, 1.435, 0.575, 1.32, 0.46, 1.205, 0.345, 1.09, 0.23, 0.975, 0.115, 0.86, 0, 0.7475, 1.4925, 0.6325, 1.3775, 0.5175, 1.2625, 0.4025, 1.1475, 0.2875, 1.0325, 0.1725, 0.9175, 0.0575, 0.8025, 1.55, 0.69, 1.435, 0.575, 1.32, 0.46, 1.205, 0.345, 1.09, 0.23, 0.975, 0.115, 0.86, 0, 0.7475, 1.4925, 0.6325, 1.3775, 0.5175, 1.2625, 0.4025, 1.1475, 0.2875, 1.0325, 0.1725, 0.9175, 0.0575, 0.8025, 1.55, 0.69, 1.435, 0.575, 1.32, 0.46, 1.205, 0.345, 1.09, 0.23, 0.975, 0.115, 0.86] \n", "\n", "scaled: \n", " [0.        0.4671875 0.9328125 0.3953125 0.8609375 0.3234375 0.7890625\n", " 0.2515625 0.7171875 0.1796875 0.6453125 0.1078125 0.5734375 0.0359375\n", " 0.5015625 0.96875   0.43125   0.896875  0.359375  0.825     0.2875\n", " 0.753125  0.215625  0.68125   0.14375   0.609375  0.071875  0.5375\n", " 0.        0.4671875 0.9328125 0.3953125 0.8609375 0.3234375 0.7890625\n", " 0.2515625 0.7171875 0.1796875 0.6453125 0.1078125 0.5734375 0.0359375\n", " 0.5015625 0.96875   0.43125   0.896875  0.359375  0.825     0.2875\n", " 0.753125  0.215625  0.68125   0.14375   0.609375  0.071875  0.5375\n", " 0.        0.4671875 0.9328125 0.3953125 0.8609375 0.3234375 0.7890625\n", " 0.2515625 0.7171875 0.1796875 0.6453125 0.1078125 0.5734375 0.0359375\n", " 0.5015625 0.96875   0.43125   0.896875  0.359375  0.825     0.2875\n", " 0.753125  0.215625  0.68125   0.14375   0.609375  0.071875  0.5375   ]\n", "\n", "run:  1 ---------\n", "Effective_echo_spacing:  0.000329994\n", "tr:  1.6\n", "slicetiming in json:  [0, 0.745, 1.49, 0.63, 1.3775, 0.515, 1.2625, 0.4, 1.1475, 0.2875, 1.0325, 0.1725, 0.9175, 0.0575, 0.8025, 1.5475, 0.6875, 1.4325, 0.5725, 1.32, 0.4575, 1.205, 0.345, 1.09, 0.23, 0.975, 0.115, 0.86, 0, 0.745, 1.49, 0.63, 1.3775, 0.515, 1.2625, 0.4, 1.1475, 0.2875, 1.0325, 0.1725, 0.9175, 0.0575, 0.8025, 1.5475, 0.6875, 1.4325, 0.5725, 1.32, 0.4575, 1.205, 0.345, 1.09, 0.23, 0.975, 0.115, 0.86, 0, 0.745, 1.49, 0.63, 1.3775, 0.515, 1.2625, 0.4, 1.1475, 0.2875, 1.0325, 0.1725, 0.9175, 0.0575, 0.8025, 1.5475, 0.6875, 1.4325, 0.5725, 1.32, 0.4575, 1.205, 0.345, 1.09, 0.23, 0.975, 0.115, 0.86] \n", "\n", "scaled: \n", " [0.        0.465625  0.93125   0.39375   0.8609375 0.321875  0.7890625\n", " 0.25      0.7171875 0.1796875 0.6453125 0.1078125 0.5734375 0.0359375\n", " 0.5015625 0.9671875 0.4296875 0.8953125 0.3578125 0.825     0.2859375\n", " 0.753125  0.215625  0.68125   0.14375   0.609375  0.071875  0.5375\n", " 0.        0.465625  0.93125   0.39375   0.8609375 0.321875  0.7890625\n", " 0.25      0.7171875 0.1796875 0.6453125 0.1078125 0.5734375 0.0359375\n", " 0.5015625 0.9671875 0.4296875 0.8953125 0.3578125 0.825     0.2859375\n", " 0.753125  0.215625  0.68125   0.14375   0.609375  0.071875  0.5375\n", " 0.        0.465625  0.93125   0.39375   0.8609375 0.321875  0.7890625\n", " 0.25      0.7171875 0.1796875 0.6453125 0.1078125 0.5734375 0.0359375\n", " 0.5015625 0.9671875 0.4296875 0.8953125 0.3578125 0.825     0.2859375\n", " 0.753125  0.215625  0.68125   0.14375   0.609375  0.071875  0.5375   ]\n", "\n", "run:  2 ---------\n", "Effective_echo_spacing:  0.000329994\n", "tr:  1.6\n", "slicetiming in json:  [0, 0.745, 1.49, 0.63, 1.375, 0.515, 1.26, 0.4, 1.145, 0.285, 1.0325, 0.17, 0.9175, 0.055, 0.8025, 1.5475, 0.6875, 1.4325, 0.5725, 1.3175, 0.4575, 1.2025, 0.3425, 1.0875, 0.2275, 0.975, 0.1125, 0.86, 0, 0.745, 1.49, 0.63, 1.375, 0.515, 1.26, 0.4, 1.145, 0.285, 1.0325, 0.17, 0.9175, 0.055, 0.8025, 1.5475, 0.6875, 1.4325, 0.5725, 1.3175, 0.4575, 1.2025, 0.3425, 1.0875, 0.2275, 0.975, 0.1125, 0.86, 0, 0.745, 1.49, 0.63, 1.375, 0.515, 1.26, 0.4, 1.145, 0.285, 1.0325, 0.17, 0.9175, 0.055, 0.8025, 1.5475, 0.6875, 1.4325, 0.5725, 1.3175, 0.4575, 1.2025, 0.3425, 1.0875, 0.2275, 0.975, 0.1125, 0.86] \n", "\n", "scaled: \n", " [0.        0.465625  0.93125   0.39375   0.859375  0.321875  0.7875\n", " 0.25      0.715625  0.178125  0.6453125 0.10625   0.5734375 0.034375\n", " 0.5015625 0.9671875 0.4296875 0.8953125 0.3578125 0.8234375 0.2859375\n", " 0.7515625 0.2140625 0.6796875 0.1421875 0.609375  0.0703125 0.5375\n", " 0.        0.465625  0.93125   0.39375   0.859375  0.321875  0.7875\n", " 0.25      0.715625  0.178125  0.6453125 0.10625   0.5734375 0.034375\n", " 0.5015625 0.9671875 0.4296875 0.8953125 0.3578125 0.8234375 0.2859375\n", " 0.7515625 0.2140625 0.6796875 0.1421875 0.609375  0.0703125 0.5375\n", " 0.        0.465625  0.93125   0.39375   0.859375  0.321875  0.7875\n", " 0.25      0.715625  0.178125  0.6453125 0.10625   0.5734375 0.034375\n", " 0.5015625 0.9671875 0.4296875 0.8953125 0.3578125 0.8234375 0.2859375\n", " 0.7515625 0.2140625 0.6796875 0.1421875 0.609375  0.0703125 0.5375   ]\n", "\n", "run:  3 ---------\n", "Effective_echo_spacing:  0.000329994\n", "tr:  1.6\n", "slicetiming in json:  [0, 0.745, 1.49, 0.63, 1.375, 0.515, 1.2625, 0.4, 1.1475, 0.2875, 1.0325, 0.1725, 0.9175, 0.0575, 0.8025, 1.5475, 0.6875, 1.4325, 0.5725, 1.32, 0.4575, 1.205, 0.3425, 1.09, 0.23, 0.975, 0.115, 0.86, 0, 0.745, 1.49, 0.63, 1.375, 0.515, 1.2625, 0.4, 1.1475, 0.2875, 1.0325, 0.1725, 0.9175, 0.0575, 0.8025, 1.5475, 0.6875, 1.4325, 0.5725, 1.32, 0.4575, 1.205, 0.3425, 1.09, 0.23, 0.975, 0.115, 0.86, 0, 0.745, 1.49, 0.63, 1.375, 0.515, 1.2625, 0.4, 1.1475, 0.2875, 1.0325, 0.1725, 0.9175, 0.0575, 0.8025, 1.5475, 0.6875, 1.4325, 0.5725, 1.32, 0.4575, 1.205, 0.3425, 1.09, 0.23, 0.975, 0.115, 0.86] \n", "\n", "scaled: \n", " [0.        0.465625  0.93125   0.39375   0.859375  0.321875  0.7890625\n", " 0.25      0.7171875 0.1796875 0.6453125 0.1078125 0.5734375 0.0359375\n", " 0.5015625 0.9671875 0.4296875 0.8953125 0.3578125 0.825     0.2859375\n", " 0.753125  0.2140625 0.68125   0.14375   0.609375  0.071875  0.5375\n", " 0.        0.465625  0.93125   0.39375   0.859375  0.321875  0.7890625\n", " 0.25      0.7171875 0.1796875 0.6453125 0.1078125 0.5734375 0.0359375\n", " 0.5015625 0.9671875 0.4296875 0.8953125 0.3578125 0.825     0.2859375\n", " 0.753125  0.2140625 0.68125   0.14375   0.609375  0.071875  0.5375\n", " 0.        0.465625  0.93125   0.39375   0.859375  0.321875  0.7890625\n", " 0.25      0.7171875 0.1796875 0.6453125 0.1078125 0.5734375 0.0359375\n", " 0.5015625 0.9671875 0.4296875 0.8953125 0.3578125 0.825     0.2859375\n", " 0.753125  0.2140625 0.68125   0.14375   0.609375  0.071875  0.5375   ]\n", "\n", "run:  4 ---------\n", "Effective_echo_spacing:  0.000329994\n", "tr:  1.6\n", "slicetiming in json:  [0, 0.745, 1.49, 0.63, 1.375, 0.515, 1.2625, 0.4, 1.1475, 0.285, 1.0325, 0.1725, 0.9175, 0.0575, 0.8025, 1.5475, 0.6875, 1.4325, 0.5725, 1.3175, 0.4575, 1.205, 0.3425, 1.09, 0.23, 0.975, 0.115, 0.86, 0, 0.745, 1.49, 0.63, 1.375, 0.515, 1.2625, 0.4, 1.1475, 0.285, 1.0325, 0.1725, 0.9175, 0.0575, 0.8025, 1.5475, 0.6875, 1.4325, 0.5725, 1.3175, 0.4575, 1.205, 0.3425, 1.09, 0.23, 0.975, 0.115, 0.86, 0, 0.745, 1.49, 0.63, 1.375, 0.515, 1.2625, 0.4, 1.1475, 0.285, 1.0325, 0.1725, 0.9175, 0.0575, 0.8025, 1.5475, 0.6875, 1.4325, 0.5725, 1.3175, 0.4575, 1.205, 0.3425, 1.09, 0.23, 0.975, 0.115, 0.86] \n", "\n", "scaled: \n", " [0.        0.465625  0.93125   0.39375   0.859375  0.321875  0.7890625\n", " 0.25      0.7171875 0.178125  0.6453125 0.1078125 0.5734375 0.0359375\n", " 0.5015625 0.9671875 0.4296875 0.8953125 0.3578125 0.8234375 0.2859375\n", " 0.753125  0.2140625 0.68125   0.14375   0.609375  0.071875  0.5375\n", " 0.        0.465625  0.93125   0.39375   0.859375  0.321875  0.7890625\n", " 0.25      0.7171875 0.178125  0.6453125 0.1078125 0.5734375 0.0359375\n", " 0.5015625 0.9671875 0.4296875 0.8953125 0.3578125 0.8234375 0.2859375\n", " 0.753125  0.2140625 0.68125   0.14375   0.609375  0.071875  0.5375\n", " 0.        0.465625  0.93125   0.39375   0.859375  0.321875  0.7890625\n", " 0.25      0.7171875 0.178125  0.6453125 0.1078125 0.5734375 0.0359375\n", " 0.5015625 0.9671875 0.4296875 0.8953125 0.3578125 0.8234375 0.2859375\n", " 0.753125  0.2140625 0.68125   0.14375   0.609375  0.071875  0.5375   ]\n", "\n", "run:  5 ---------\n", "Effective_echo_spacing:  0.000329994\n", "tr:  1.6\n", "slicetiming in json:  [0, 0.745, 1.49, 0.63, 1.375, 0.515, 1.26, 0.4, 1.1475, 0.285, 1.0325, 0.1725, 0.9175, 0.0575, 0.8025, 1.5475, 0.6875, 1.4325, 0.5725, 1.3175, 0.4575, 1.205, 0.3425, 1.09, 0.2275, 0.975, 0.115, 0.86, 0, 0.745, 1.49, 0.63, 1.375, 0.515, 1.26, 0.4, 1.1475, 0.285, 1.0325, 0.1725, 0.9175, 0.0575, 0.8025, 1.5475, 0.6875, 1.4325, 0.5725, 1.3175, 0.4575, 1.205, 0.3425, 1.09, 0.2275, 0.975, 0.115, 0.86, 0, 0.745, 1.49, 0.63, 1.375, 0.515, 1.26, 0.4, 1.1475, 0.285, 1.0325, 0.1725, 0.9175, 0.0575, 0.8025, 1.5475, 0.6875, 1.4325, 0.5725, 1.3175, 0.4575, 1.205, 0.3425, 1.09, 0.2275, 0.975, 0.115, 0.86] \n", "\n", "scaled: \n", " [0.        0.465625  0.93125   0.39375   0.859375  0.321875  0.7875\n", " 0.25      0.7171875 0.178125  0.6453125 0.1078125 0.5734375 0.0359375\n", " 0.5015625 0.9671875 0.4296875 0.8953125 0.3578125 0.8234375 0.2859375\n", " 0.753125  0.2140625 0.68125   0.1421875 0.609375  0.071875  0.5375\n", " 0.        0.465625  0.93125   0.39375   0.859375  0.321875  0.7875\n", " 0.25      0.7171875 0.178125  0.6453125 0.1078125 0.5734375 0.0359375\n", " 0.5015625 0.9671875 0.4296875 0.8953125 0.3578125 0.8234375 0.2859375\n", " 0.753125  0.2140625 0.68125   0.1421875 0.609375  0.071875  0.5375\n", " 0.        0.465625  0.93125   0.39375   0.859375  0.321875  0.7875\n", " 0.25      0.7171875 0.178125  0.6453125 0.1078125 0.5734375 0.0359375\n", " 0.5015625 0.9671875 0.4296875 0.8953125 0.3578125 0.8234375 0.2859375\n", " 0.753125  0.2140625 0.68125   0.1421875 0.609375  0.071875  0.5375   ]\n"]}], "source": ["import json\n", "for run_id in range(6):\n", "    print('\\nrun: ', run_id, '---------')\n", "    json_file_path = os.path.join(func_input_dir, f\"sub-01_ses-nsd01_task-nsdcore_run-0{run_id+1}_bold.json\")\n", "\n", "    with open(json_file_path, 'r') as json_file:\n", "        data = json.load(json_file)\n", "        \n", "    Effective_echo_spacing = data['EffectiveEchoSpacing']\n", "    print('Effective_echo_spacing: ', Effective_echo_spacing)\n", "    tr = data['RepetitionTime']\n", "    print('tr: ', tr)\n", "\n", "    tcustom = data['SliceTiming']\n", "    print('slicetiming in json: ', tcustom, '\\n')\n", "\n", "    tcustom_scaled = np.divide(tcustom, tr)\n", "    print('scaled: \\n',tcustom_scaled)\n", "\n", "\n", "    if not os.path.exists(tcustom_dir):    \n", "        np.savetxt(X=tcustom_scaled, delimiter='\\n', fname=tcustom_dir)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["# Visualize Images as a slider\n", "def show_slice_0(i):\n", "    plt.subplot(131)\n", "    plt.imshow(array[i,:,:], cmap='gray')\n", "    plt.show()\n", "def show_slice_1(j):\n", "    plt.subplot(132)\n", "    plt.imshow(array[:,j,:], cmap='gray')\n", "    plt.show()\n", "def show_slice_2(k):\n", "    plt.subplot(133)\n", "    plt.imshow(array[:,:,k], cmap='gray')\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Remove dummy volumes\n", "\n", "Don't have to do this in Siemens Prisma data. Already removed before exporting from the machine. Check if other machines' data is used here."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Skull-stripping and fieldmap preparations\n", "\n", "Stop and check after each command. Slightly adjust the cmd params and re-run the cmd if temporary images are not appropriately displayed."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Preparations:\n", "\n", "1. 3 field map images: 2 magnitude images and 1 phase images\n", "\n", "2. Generate fmap_rads.nii.gz: "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["## Skull-strip the magnitude nifti image\n", "cmd = f\"bet2 {field_mag_dir[run_id_fmap]} {field_brain1_dir[run_id_fmap]} -f 0.5\"\n", "os.system(cmd)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Figure size 640x480 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 660x350 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plot_roi(roi_img=singlevol_dir, bg_img=nib.load(field_brain1_dir[run_id_fmap]), alpha=0.2)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c9b99a54569741298cdfbe14b5657d90", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=49, description='i', max=99), Output()), _dom_classes=('widget-interact'…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1ea6c85fe5974ca987b5301173dbf721", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=49, description='j', max=99), Output()), _dom_classes=('widget-interact'…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "def2c90a1d11470ea3325f92e6d0dc03", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=20, description='k', max=41), Output()), _dom_classes=('widget-interact'…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.show_slice_2(k)>"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the Nifti File\n", "res_img = nib.load(field_brain1_dir[run_id_fmap])\n", "array = res_img.get_fdata()\n", "\n", "interact(show_slice_0, i=(0, array.shape[0]-1))\n", "interact(show_slice_1, j=(0, array.shape[1]-1))\n", "\n", "interact(show_slice_2, k=(0, array.shape[2]-1))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["## Erode (or dilate) the brain image a little \n", "## if the previous 'bet2 -f' cannot give you a better result\n", "cmd = f\"fslmaths {field_brain1_dir[run_id_fmap]} -ero {field_brain_dir[run_id_fmap]}\"\n", "os.system(cmd)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8f757c1632804a39b152aced42fe8f4e", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=49, description='i', max=99), Output()), _dom_classes=('widget-interact'…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "19a292dfa067474bb1babd58964c953e", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=49, description='j', max=99), Output()), _dom_classes=('widget-interact'…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "813e13a36159410bbc4208082b1d3111", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=20, description='k', max=41), Output()), _dom_classes=('widget-interact'…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.show_slice_2(k)>"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the Nifti File\n", "res_img = nib.load(field_brain_dir[run_id_fmap])\n", "array = res_img.get_fdata()\n", "\n", "interact(show_slice_0, i=(0, array.shape[0]-1))\n", "interact(show_slice_1, j=(0, array.shape[1]-1))\n", "interact(show_slice_2, k=(0, array.shape[2]-1))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading volumes\n", "Phase loaded\n", "Magnitude loaded\n", "Mask loaded\n", "Rewrapping phase range to [-pi,pi]\n", "Number of phase splits = 8\n", "Calculating starting matrices (471 by 471)\n", "Finished connection_matrices\n", "1253 constraints left\n", "Did while loop 470 times\n", "Done. Created /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/field_output/run-03_fmap_rads for use with FEAT.\n"]}, {"data": {"text/plain": ["0"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["## Create fmap_rads.nii.gz file\n", "## This is a necessity for the fieldmap correction step.\n", "## This should be done after you get a good skull-stripping result, at least in the prefrontal area.\n", "# Usage: fsl_prepare_fieldmap <scanner> <phase_image> <magnitude_image> <out_image> <deltaTE (in ms)> [--nocheck]\n", "# <deltaTE> is the echo time difference of the fieldmap sequence - find this out from the operator\n", "# (defaults are *usually* 2.46ms=long echo time of mag.nii.gz-short echo time of mag.nii.gz on SIEMENS)\n", "cmd = f\"fsl_prepare_fieldmap SIEMENS {field_pha_dir[run_id_fmap]} {field_brain_dir[run_id_fmap]} {field_fmap_dir[run_id_fmap]} 2.46\"\n", "os.system(cmd)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ca1912d92afe4231a5c300f57a49b9ad", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=49, description='i', max=99), Output()), _dom_classes=('widget-interact'…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1bd0a54c12514114b300240547734fc7", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=49, description='j', max=99), Output()), _dom_classes=('widget-interact'…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8af5f63b5ac14f358a654187327bc7d8", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=20, description='k', max=41), Output()), _dom_classes=('widget-interact'…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.show_slice_2(k)>"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the Nifti File\n", "res_img = nib.load(field_fmap_dir[run_id_fmap])\n", "\n", "# Convert the image data as a NumPy array\n", "array = res_img.get_fdata()\n", "\n", "interact(show_slice_0, i=(0, array.shape[0]-1))\n", "interact(show_slice_1, j=(0, array.shape[1]-1))\n", "interact(show_slice_2, k=(0, array.shape[2]-1))"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [{"data": {"text/plain": ["'/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/field_output/run-02_fmap_rads.nii.gz'"]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["field_fmap_dir['run-01']\n", "field_fmap_dir['run-02']"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Figure size 640x480 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 730x350 with 5 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plot_roi(roi_img=field_fmap_dir['run-01'], bg_img=nib.load(field_fmap_dir['run-02']), alpha=0.2, colorbar=True)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Skull-strip and segment the anatomical T1 image\n", "\n", "This step provide gray matter, white matter, and csf maps."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["## Skull-strip the T1 image\n", "cmd = f\"bet2 {anat_T1_dir} {anat_brain_dir}\"\n", "os.system(cmd)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/anat_output/T1_brain.nii.gz\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "58154a83c4344364a75ec86f3a479439", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=159, description='i', max=319), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ad45b862b4d247d896cb95a2102540e1", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=159, description='j', max=319), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "07e900d101044684b5ab69fe485ebaf7", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=159, description='k', max=319), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.show_slice_2(k)>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the Nifti File\n", "print(anat_brain_dir)\n", "res_img = nib.load(anat_brain_dir)\n", "array = res_img.get_fdata()\n", "\n", "interact(show_slice_0, i=(0, array.shape[0]-1))\n", "interact(show_slice_1, j=(0, array.shape[1]-1))\n", "interact(show_slice_2, k=(0, array.shape[2]-1))"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["## Segment the T1 brain into gm, wm, and csf\n", "cmd = f\"fast -B -I 10 -l 10 {anat_brain_dir}\"\n", "os.system(cmd)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3b9923d566204b5fb37e520bc8bcc55d", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=159, description='i', max=319), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3b3a3d19c0ca4f7eb33be470105a200c", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=159, description='j', max=319), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "98c3bbc391214ec08cbac70452e7bf80", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=159, description='k', max=319), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.show_slice_2(k)>"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the white-matter map Nifti File\n", "res_img = nib.load(anat_pve2_dir)\n", "array = res_img.get_fdata()\n", "\n", "interact(show_slice_0, i=(0, array.shape[0]-1))\n", "interact(show_slice_1, j=(0, array.shape[1]-1))\n", "interact(show_slice_2, k=(0, array.shape[2]-1))"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["## Create a binary white-matter map\n", "cmd = f\"fslmaths {anat_pve2_dir} -thr 0.5 -bin {anat_wmseg_dir}\"\n", "os.system(cmd)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d23e5f3bf7ec43c2a946c63557645399", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=159, description='i', max=319), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "95bd4cf8ea5349f6870674c3bb292112", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=159, description='j', max=319), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cbd1700cf5004f5ea42273e9f96ea153", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=159, description='k', max=319), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.show_slice_2(k)>"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the Nifti File\n", "res_img = nib.load(anat_wmseg_dir)\n", "array = res_img.get_fdata()\n", "\n", "interact(show_slice_0, i=(0, array.shape[0]-1))\n", "interact(show_slice_1, j=(0, array.shape[1]-1))\n", "interact(show_slice_2, k=(0, array.shape[2]-1))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["## Create a binary gray-matter map\n", "cmd = f\"fslmaths {anat_pve1_dir} -thr 0.4 -bin {anat_gm_mask_dir}\"\n", "os.system(cmd)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c80a97dd19da4ccfbc3a912d9db07b90", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=159, description='i', max=319), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f2db07e145944426a132ecd70d29b53a", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=159, description='j', max=319), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "535d381e813541bcab28b1c47834ef1b", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=159, description='k', max=319), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.show_slice_2(k)>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the Nifti File\n", "res_img = nib.load(anat_gm_mask_dir)\n", "array = res_img.get_fdata()\n", "\n", "interact(show_slice_0, i=(0, array.shape[0]-1))\n", "interact(show_slice_1, j=(0, array.shape[1]-1))\n", "interact(show_slice_2, k=(0, array.shape[2]-1))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. recon"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. run preprocessing of func nii"]}], "metadata": {"kernelspec": {"display_name": "fmri", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}