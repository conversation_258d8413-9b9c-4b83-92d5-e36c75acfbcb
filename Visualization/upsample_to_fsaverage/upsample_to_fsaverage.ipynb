{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["\n# Upsample data from a lower resolution fsaverage template to fsaverage for visualization\n\nThis example shows how data in a lower resolution fsaverage template \n(e.g., fsaverage5 or fsaverage6) can be upsampled to the high resolution fsaverage \ntemplate for visualization.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["import matplotlib.pyplot as plt\nimport numpy as np\n\nimport cortex\n\nsubject = \"fsaverage\"\n\n# First we check if the fsaverage template is already in the pycortex filestore. If not,\n# we download the template from the web and add it to the filestore.\nif subject not in cortex.db.subjects:\n    cortex.download_subject(subject)\n\n# Next we create some data on fsaverage5. Each hemisphere has 10242 vertices.\nn_vertices_fsaverage5 = 10242\ndata_fs5 = np.arange(1, n_vertices_fsaverage5 + 1)\n# We concatenate the data to itself to create a vector of length 20484, corresponding to\n# the two hemispheres together.\ndata_fs5 = np.concatenate((data_fs5, data_fs5))\n# Finally, we upsample the data to fsaverage.\ndata_fs7 = cortex.freesurfer.upsample_to_fsaverage(data_fs5, \"fsaverage5\")\n\n# Now that the data is in the fsaverage template, we can visualize it in PyCortex as any\n# other vertex dataset.\nvtx = cortex.Vertex(data_fs7, subject, vmin=0, vmax=n_vertices_fsaverage5, cmap=\"turbo\")\ncortex.quickshow(vtx, with_curvature=False, with_colorbar=False)\nplt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 0}