%%%%%%%%%%%%%%%%%%%%%%%%%% aparc

% 36 entries. note that the first entry is called 'unknown'.

fslabels = {
    'unknown'
    'bankssts'
    'caudalanteriorcingulate'
    'caudalmiddlefrontal'  % #4
    'corpuscallosum'
    'cuneus'
    'entorhinal'
    'fusiform'
    'inferiorparietal'
    'inferiortemporal'
    'isthmuscingulate'
    'lateraloccipital'
    'lateralorbitofrontal'
    'lingual'
    'medialorbitofrontal'
    'middletemporal'
    'parahippocampal'
    'paracentral'
    'parsopercularis'
    'parsorbitalis'
    'parstriangularis'
    'pericalcarine'
    'postcentral'
    'posteriorcingulate'
    'precentral'  % #25
    'precuneus'
    'rostralanteriorcingulate'
    'rostralmiddlefrontal'
    'superiorfrontal'
    'superiorparietal'
    'superiortemporal'
    'supramarginal'
    'frontalpole'
    'temporalpole'
    'transversetemporal'
    'insula'
}';

fscolortable = [
          25           5          25           0     1639705
          25         100          40           0     2647065
         125         100         160           0    ********
         100          25           0           0        6500
         120          70          50           0     3294840
         220          20         100           0     6558940
         220          20          10           0      660700
         180         220         140           0     9231540
         220          60         220           0    14433500
         180          40         120           0     7874740
         140          20         140           0     9180300
          20          30         140           0     9182740
          35          75          50           0     3296035
         225         140         140           0     9211105
         200          35          75           0     4924360
         160         100          50           0     3302560
          20         220          60           0     3988500
          60         220          60           0     3988540
         220         180         140           0     9221340
          20         100          50           0     3302420
         220          60          20           0     1326300
         120         100          60           0     3957880
         220          20          20           0     1316060
         220         180         220           0    14464220
          60          20         220           0    14423100
         160         140         180           0    11832480
          80          20         140           0     9180240
          75          50         125           0     8204875
          20         220         160           0    10542100
          20         180         140           0     9221140
         140         220         220           0    14474380
          80         160          20           0     1351760
         100           0         100           0     6553700
          70          20         170           0    11146310
         150         150         200           0    13145750
         255         192          32           0     2146559
];

%%%%%%%%%%%%%%%%%%%%%%%%%% aparc.a2009s

% 76 entries. note that the first entry is called 'unknown'.

fslabels2009 = {
    'Unknown'
    'G&S_frontomargin'
    'G&S_occipital_inf'
    'G&S_paracentral'
    'G&S_subcentral'
    'G&S_transv_frontopol'
    'G&S_cingul-Ant'
    'G&S_cingul-Mid-Ant'
    'G&S_cingul-Mid-Post'
    'G_cingul-Post-dorsal'
    'G_cingul-Post-ventral'
    'G_cuneus'
    'G_front_inf-Opercular'
    'G_front_inf-Orbital'
    'G_front_inf-Triangul'
    'G_front_middle'
    'G_front_sup'
    'G_Ins_lg&S_cent_ins'
    'G_insular_short'
    'G_occipital_middle'
    'G_occipital_sup'
    'G_oc-temp_lat-fusifor'
    'G_oc-temp_med-Lingual'
    'G_oc-temp_med-Parahip'
    'G_orbital'
    'G_pariet_inf-Angular'
    'G_pariet_inf-Supramar'
    'G_parietal_sup'
    'G_postcentral'
    'G_precentral'
    'G_precuneus'
    'G_rectus'
    'G_subcallosal'
    'G_temp_sup-G_T_transv'
    'G_temp_sup-Lateral'
    'G_temp_sup-Plan_polar'
    'G_temp_sup-Plan_tempo'
    'G_temporal_inf'
    'G_temporal_middle'
    'Lat_Fis-ant-Horizont'
    'Lat_Fis-ant-Vertical'
    'Lat_Fis-post'
    'Medial_wall'
    'Pole_occipital'
    'Pole_temporal'
    'S_calcarine'
    'S_central'
    'S_cingul-Marginalis'
    'S_circular_insula_ant'
    'S_circular_insula_inf'
    'S_circular_insula_sup'
    'S_collat_transv_ant'
    'S_collat_transv_post'
    'S_front_inf'
    'S_front_middle'
    'S_front_sup'
    'S_interm_prim-Jensen'
    'S_intrapariet&P_trans'
    'S_oc_middle&Lunatus'
    'S_oc_sup&transversal'
    'S_occipital_ant'
    'S_oc-temp_lat'
    'S_oc-temp_med&Lingual'
    'S_orbital_lateral'
    'S_orbital_med-olfact'
    'S_orbital-H_Shaped'
    'S_parieto_occipital'
    'S_pericallosal'
    'S_postcentral'
    'S_precentral-inf-part'
    'S_precentral-sup-part'
    'S_suborbital'
    'S_subparietal'
    'S_temporal_inf'
    'S_temporal_sup'
    'S_temporal_transverse'
}';

fscolortable2009 = [
           0           0           0           0           0
          23         220          60           0     3988503
          23          60         180           0    11811863
          63         100          60           0     3957823
          63          20         220           0    14423103
          13           0         250           0    16384013
          26          60           0           0       15386
          26          60          75           0     4930586
          26          60         150           0     9845786
          25          60         250           0    16399385
          60          25          25           0     1644860
         180          20          20           0     1316020
         220          20         100           0     6558940
         140          60          60           0     3947660
         180         220         140           0     9231540
         140         100         180           0    11822220
         180          20         140           0     9180340
          23          10          10           0      657943
         225         140         140           0     9211105
         180          60         180           0    11812020
          20         220          60           0     3988500
          60          20         140           0     9180220
         220         180         140           0     9221340
          65         100          20           0     1336385
         220          60          20           0     1326300
          20          60         220           0    14433300
         100         100          60           0     3957860
         220         180         220           0    14464220
          20         180         140           0     9221140
          60         140         180           0    11832380
          25          20         140           0     9180185
          20          60         100           0     6568980
          60         220          20           0     1367100
          60          60         220           0    14433340
         220          60         220           0    14433500
          65         220          60           0     3988545
          25         140          20           0     1346585
         220         220         100           0     6610140
         180          60          60           0     3947700
          61          20         220           0    14423101
          61          20          60           0     3937341
          61          60         100           0     6569021
          25          25          25           0     1644825
         140          20          60           0     3937420
         220         180          20           0     1357020
          63         180         180           0    11842623
         221          20          10           0      660701
         221          20         100           0     6558941
         221          60         140           0     9190621
         221          20         220           0    14423261
          61         220         220           0    14474301
         100         200         200           0    13158500
          10         200         200           0    13158410
         221         220          20           0     1367261
         141          20         100           0     6558861
          61         220         100           0     6609981
         141          60          20           0     1326221
         143          20         220           0    14423183
         101          60         220           0    14433381
          21          20         140           0     9180181
          61          20         180           0    11801661
         221         140          20           0     1346781
         141         100         220           0    14443661
         221         100          20           0     1336541
         181         200          20           0     1362101
         101          20          20           0     1315941
         101         100         180           0    11822181
         181         220          20           0     1367221
          21         140         200           0    13143061
          21          20         240           0    15733781
          21          20         200           0    13112341
          21          20          60           0     3937301
         101          60          60           0     3947621
          21         180         180           0    11842581
         223         220          60           0     3988703
         221          60          60           0     3947741
];
