function N=facenormals(V, F)
%
%  N=facenormals(V, F)
%

% Get all edge vectors
e1=V(F(:,1),:)-V(F(:,2),:);
e2=V(F(:,2),:)-V(F(:,3),:);
e3=V(F(:,3),:)-V(F(:,1),:);

% Normalize edge vectors
e1_norm=e1./repmat(sqrt(e1(:,1).^2+e1(:,2).^2+e1(:,3).^2),1,3); 
e2_norm=e2./repmat(sqrt(e2(:,1).^2+e2(:,2).^2+e2(:,3).^2),1,3); 
e3_norm=e3./repmat(sqrt(e3(:,1).^2+e3(:,2).^2+e3(:,3).^2),1,3);

% Calculate Angle of face seen from vertices
%Angle =  [acos(dot(e1_norm',-e3_norm'));acos(dot(e2_norm',-e1_norm'));acos(dot(e3_norm',-e2_norm'))]';

% Calculate normal of face
N=cross(e1,e3);

