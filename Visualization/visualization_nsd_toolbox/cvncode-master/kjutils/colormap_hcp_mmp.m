function cmap = colormap_hcp_mmp
%cmap = colormap_hcp_mmp()
% 
%RGB values for HCP MMP v1.0 parcels, as defined in Glasser 2016
%
%Use clim [-.5 180.5]

cmap=[    0 0 0
    63     5   255
    54   103   129
    62    78   178
    23    50   233
    15    40   226
    14    28   214
    26    47   201
    33   178    20
    35   205    21
   134   143   137
   141   166   152
    62    88    47
     7    57   246
   153    64   133
   186    86   188
    18    70   195
    57   114   160
    57    89   117
    25    47   206
     0    49   184
    18    33   181
    34    43   171
    31    86   104
   235    19    47
   122    79    56
    31    49    22
   138   128   153
   142    87    76
   149   181   188
    42    31    44
    81    54    98
   127    56    91
    29     0    14
    63    17    47
    54    11    42
    48   163    48
   153   172   136
   205   190   194
    64   154    64
    69   161    44
   119   159    66
   115   194   134
   110   146    86
   144   179   135
   184   243   224
   169   246   237
    68   177   108
    63   113   158
    50   129   137
   136   206   204
    20   191    38
    48   176    62
    47   216    22
    26   158    32
    64   159    38
    71   168    50
   146   136   107
   129   106   103
   176   133   130
   163   142   121
    67    12    25
    90    41    50
   105    83    81
    95    32    52
    25    12     1
    96    75    66
    53    54    43
    63    67    48
    43    35    18
    34    39    20
    38    43    28
    38    35    24
   101    85    84
    88    88    62
    60    63    42
    51    49    31
    77    76    66
   178   214   151
   126   119    95
   137   160   132
    97    98    87
   188   201   178
   138   149   134
   207   217   200
   145   112   128
   140   117   131
    55    52    45
    14    35    14
    98    75    89
    55    62    58
   129   106   118
    82    60    69
    40    35    31
    66    35    33
   155   180   202
   121   192   136
    90    98    83
    88    92    73
   145   138    64
   114   157    49
    99   158    35
   145   127    52
   208    72    66
   178    61    23
   190   144    89
   131   124    83
   130    42    27
   182   148   129
   157   116   106
    90    66    65
   141    72    83
    85    35    44
   160   153    88
   175   137    99
    99   156    51
   129   222   134
   136   220   166
    49    48    35
    86    57    83
    60    57    40
    87    59   176
    61    62    51
    58    42    26
   195    30     5
    99    39     6
    69    50    75
    96   102   113
    77    51    34
    73    57    39
    71    59    57
    35    34    23
    11    14     0
    96   102    94
    67    64    58
    64    72    56
   105   128   117
   217   252   223
    88   131   163
   125    90    64
   156   162   138
   121   148   145
   120   112   190
   155   190   228
   176   166   174
   118   121   128
   112   160   207
   187   205   145
   255   255   226
   133   105   114
    53    62    46
    62    67    57
    41    89   166
    62    61   155
    49    53   159
    88    90    89
    14    79   128
    92   157   153
    15    46   182
    54    94   160
    67    62   161
    56    22    45
   143   105   133
    53    61   124
    36    24    14
    46    35    21
    76    48    53
   164   104    97
   110   123    36
   163   102   105
    89    64    76
   126   123   112
    46    51    42
   187    30    24
   233    18    27
   152    39     4
    40    33    22
    59    45    43
   122    50    50
   130    63    87
   123    35    74]/255;