%%%%% FIRST, DEFINE V1-hV4

% define
zz = 1;
subjid = sprintf('subj%02d',zz);   % which subject
cmap   = jet(256);   % colormap for ROIs
rng    = [0 7];      % should be [0 N] where N is the max ROI index
roilabels = {'V1v' 'V1d' 'V2v' 'V2d' 'V3v' 'V3d' 'hV4'};  % 1 x N cell vector of strings
prfang = {mod(180-cvnloadmgz(sprintf('%s/%s/label/lh.prfangle.mgz',cvnpath('freesurfer'),subjid)),360) ...  % one colormap for both hemis
          cvnloadmgz(sprintf('%s/%s/label/rh.prfangle.mgz',cvnpath('freesurfer'),subjid))};
mgznames = { ...
  'Kastner2015' ...
  prfang ...
  'prfeccentricity' ...
  'prfmeanvol' ...
  'prfR2' ...
  'corticalsulc' ...
  'prf-eccrois' ...
};           % quantities of interest (1 x Q)
crngs = { ...
  [0 25] ...
  [0 360] ...
  [0 12] ...
  [0 2000] ...
  [0 50] ...
  [0 28] ...
  [0 5] ...
};          % ranges for the quantities (1 x Q)
cmaps = { ...
  jet(256) ...
  cmfanglecmapRH ...
  cmfecccmap(4) ...
  gray(256) ...
  hot(256) ...
  jet(256) ...
  jet(256) ...
};   % colormaps for the quantities (1 x Q)
threshs = { ...
  0.5 ...
  [] ...
  [] ...
  [] ...
  [] ...
  0.5 ...
  0.5 ...
};   % thresholds
roivals = [];                                                                    % start with a blank slate?
roivals = cvnloadmgz(sprintf('%s/%s/label/*.prf-visualrois.mgz',cvnpath('freesurfer'),subjid));  % load in an existing file?

% do it
cvndefinerois;

% save as [lh,rh].prf-visualrois.mgz

%%%%% NEXT, DEFINE ECC ROIs

% define
zz = 1;
subjid = sprintf('subj%02d',zz);   % which subject
cmap   = jet(256);   % colormap for ROIs
rng    = [0 5];      % should be [0 N] where N is the max ROI index
roilabels = {'ecc0pt5' 'ecc1' 'ecc2' 'ecc4' 'ecc4+'};  % 1 x N cell vector of strings
mgznames = { ...
  'prf-visualrois' ...
  'prfeccentricity' ...
  'prfeccentricity' ...
  'prfeccentricity' ...
  'prfeccentricity' ...
  'prfeccentricity' ...
};           % quantities of interest (1 x Q)
delta = [-.01 .01];
crngs = { ...
  [0 7] ...
  [0 12] ...
  .5+delta ...
  1+delta ...
  2+delta ...
  4+delta ...
};          % ranges for the quantities (1 x Q)
cmaps = { ...
  jet(256) ...
  cmfecccmap(4) ...
  gray ...
  gray ...
  gray ...
  gray ...
};   % colormaps for the quantities (1 x Q)
threshs = { ...
  0.5 ...
  [] ...
  [] ...
  [] ...
  [] ...
  [] ...
};   % thresholds
roivals = [];                                                                    % start with a blank slate?
roivals = cvnloadmgz(sprintf('%s/%s/label/*.prf-eccrois.mgz',cvnpath('freesurfer'),subjid));  % load in an existing file?

% do it
cvndefinerois;

% save as [lh,rh].prf-eccrois.mgz

%%%%% TRIM

% trim ecc ROIs to live within v1-v4
hemis = {'lh' 'rh'};
for subjix=1:8
  for hemi=1:2
    fsdir0 = sprintf('~/nsd/nsddata/freesurfer/subj%02d',subjix);
    file1 = sprintf('%s/label/%s.prf-eccrois.mgz',fsdir0,hemis{hemi});
    a1 = cvnloadmgz(file1);
    a2 = cvnloadmgz(sprintf('%s/label/%s.prf-visualrois.mgz',fsdir0,hemis{hemi}));
    a1(a2==0) = 0;
    nsd_savemgz(a1,file1,fsdir0);
  end
end

%%%%%%%%% CONTINUE

% create .ctab file with final names!

% copy
for subjix=1:8
  copyfile(sprintf('~/Dropbox/KKTEMP/%s.mgz.ctab','prf-visualrois'), ...
           sprintf('~/nsd/nsddata/freesurfer/subj%02d/label/',subjix));
  copyfile(sprintf('~/Dropbox/KKTEMP/%s.mgz.ctab','prf-eccrois'), ...
           sprintf('~/nsd/nsddata/freesurfer/subj%02d/label/',subjix));
end

%%%%% proceed to analysis_drawrois_prf_part03.m
