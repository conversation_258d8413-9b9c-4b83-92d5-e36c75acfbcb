a1 = load('/research/papers/2022 nsd/figures/INSPECTIONS/ROTATINGBRAIN/b3nc.mat');


allvals = [];
for p=1:8
  a = cvnloadmgz(sprintf('~/Dropbox/nsddata/freesurfer/subj%02d/label/lh.prf-visualrois.mgz',p));
  allvals(:,p,1) = nsd_mapdata(p,'lh.white','fsaverage',a(:),'nearest');
  a = cvnloadmgz(sprintf('~/Dropbox/nsddata/freesurfer/subj%02d/label/rh.prf-visualrois.mgz',p));
  allvals(:,p,2) = nsd_mapdata(p,'rh.white','fsaverage',a(:),'nearest');
end

% union dorsal and ventral; >=.5 of the subjects; wta;
v1avg = mean(ismember(allvals,[1 2]),2);
v2avg = mean(ismember(allvals,[3 4]),2);
v3avg = mean(ismember(allvals,[5 6]),2);
isok = v1avg >= .5 | v2avg >= .5 | v3avg >= .5;
[~,final] = max(cat(2,v1avg,v2avg,v3avg),[],2);
final(~isok) = 0;



% define
zz = 8;
subjid = sprintf('subj%02d',zz);   % which subject
subjid = 'fsaverage';
cmap   = jet(256);   % colormap for ROIs
rng    = [0 6];      % should be [0 N] where N is the max ROI index
rng = [0 5];
rng = [0 1];
roilabels = {'V1v' 'V1d' 'V2v' 'V2d' 'V3v' 'V3d'};  % 1 x N cell vector of strings
roilabels = {'posterior' 'anterior' 'V1' 'V2' 'V3'};
roilabels = {'fVTC'};
%prfang = {mod(180-cvnloadmgz(sprintf('%s/%s/label/lh.prfangle.mgz',cvnpath('freesurfer'),subjid)),360) ...  % one colormap for both hemis
%          cvnloadmgz(sprintf('%s/%s/label/rh.prfangle.mgz',cvnpath('freesurfer'),subjid))};
mgznames = { ...
  'Kastner2015' ...
  'corticalsulc' ...
  {a1.vals(1:163842) a1.vals(163842+1:end)} ...
  {final(1:163842)' final(163842+1:end)'} ...
};           % quantities of interest (1 x Q)
crngs = { ...
   [0 25] ...
   [0 28] ...
   [0 75] ...
   [0 3]
};
   
%   [0 360] ...
%   [0 12] ...
%   [0 2000] ...
%   [0 50] ...
% };          % ranges for the quantities (1 x Q)
cmaps = { ...
   jet(256) ...
   jet(256) ...
   jet(256) ...
   jet(256) ...
};
%   cmfanglecmapRH ...
%   cmfecccmap(4) ...
%   gray(256) ...
%   hot(256) ...
% };   % colormaps for the quantities (1 x Q)
threshs = { ...
   0.5 ...
   0.5 ...
   [] ...
   0.5 ...
};
%   [] ...
%   [] ...
%   [] ...
%   [] ...
% };   % thresholds
roivals = [];                                                                    % start with a blank slate?
%roivals = cvnloadmgz(sprintf('%s/%s/label/*.highlevelvisual.mgz',cvnpath('freesurfer'),subjid));  % load in an existing file?
cd('/research/papers/2022 nsd');
roivals = cvnloadmgz('figures/SCIENCE.RSA/*.highlevelvisual.mgz');
roivals = cvnloadmgz('~/nsd/nsddata/freesurfer/fsaverage/label/*.fVTC.mgz');

% do it
cvndefinerois;



0 Unknown
1 pVTC
2 aVTC
3 V1
4 V2
5 V3

0 Unknown
1 fVTC


