[metadata]
# replace with your username:
name = nsdcode
version = 1.0.0
author = <PERSON> and <PERSON><PERSON>
author_email = <EMAIL>
maintainer = <PERSON>
maintainer_email = <EMAIL>
description = NSD map data from various spaces
long_description = file: README.md
long_description_content_type = text/markdown
url = https://github.com/kendrickkay/nsdcode
classifiers =
    Programming Language :: Python :: 3
    License :: OSI Approved :: MIT License
    Operating System :: OS Independent
platforms = OS Independent

[options]
setup_requires =
  setuptools_scm
python_requires = >=3.7
install_requires =
    scikit-learn
    setuptools_scm
    numpy
    scipy
    matplotlib
    nibabel
    tqdm
    pandas

zip_safe = False
include_package_data = True
packages = find:    

[options.extras_require]
dev =
    matplotlib
    pytest
    pytest-cov
    pytest-benchmark
    sphinx
    sphinx-gallery
    numpydoc
    sphinx_rtd_theme
    sphinx_copybutton
    sphinxcontrib-matlabdomain
    flake8
