{"cells": [{"cell_type": "code", "execution_count": null, "id": "b6b757fc", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import nibabel as nib\n", "from surfer import Brain, io\n", "from mayavi import mlab\n", "import os\n", "import sys\n", "from os.path import join, exists, split\n", "import warnings\n", "from tqdm import tqdm\n", "from pprint import pprint\n", "warnings.filterwarnings('ignore')\n", "import matplotlib \n", "from glmsingle.glmsingle import GLM_single\n", "from nilearn.plotting import plot_design_matrix, plot_roi\n", "from nilearn import plotting\n", "from ipywidgets import interact\n", "\n", "# 添加 rootfolder 到 sys.path\n", "# os.getcwd()获取当前 Notebook 的工作目录（一般是打开 Notebook 时所在目录）\n", "sys.path.append(os.path.abspath(os.path.join(os.getcwd(), \"..\")))\n", "print(os.path.join(os.path.dirname(os.getcwd())))\n", "print(os.path.abspath(os.path.join(os.getcwd(), \"..\",'preprocess')))\n", "# 然后就可以正常导入\n", "from preprocess.functions import openObject, saveObject\n", "from nilearn.plotting import view_img\n", "\n", "# 添加 FSL bin 路径到环境变量，便于调用fsl命令\n", "os.environ[\"FSLDIR\"] = \"/usr/local/fsl\"\n", "os.environ[\"PATH\"] += os.pathsep + \"/usr/local/fsl/bin\"\n", "# 设置 FreeSurfer 安装目录，便于调用fsl命令\n", "os.environ[\"FREESURFER_HOME\"] = \"/usr/local/freesurfer\"\n", "os.environ[\"PATH\"] += os.pathsep + os.path.join(os.environ[\"FREESURFER_HOME\"], \"bin\")\n", "\n", "datatype = 'our'\n", "if datatype == 'nsd':\n", "    base_dir = '/opt/data/private/lq/data/NSD/nsddata_rawdata'\n", "    preproc_dir = '/opt/data/private/lq/data/NSD/nsddata_preprocdata/'\n", "    designmatrix_dir = '/opt/data/private/lq/data/NSD/nsddata/experiments/nsd/' \n", "    nsd_ref_beta_dir = '/opt/data/private/lq/data/NSD/nsddata_betas/ppdata'\n", "\n", "    os.environ[\"SUBJECTS_DIR\"] = \"/opt/data/private/lq/data/NSD/nsddata/freesurfer\"\n", "    Results_dir = \"/opt/data/private/lq/data/NSD/nsddata_betas/our/\"\n", "    \n", "    TR_s = 1.6 # nsd: 1.6; our data: 1.5\n", "    tr_target = 1.0\n", "    Effective_echo_spacing = 0.000329994 # nsd: 0.000329994; our data: 0.00027500\n", "    origvoxelSize = np.array([1.8, 1.8, 1.8])\n", "    \n", "\n", "elif datatype == 'our':\n", "    base_dir = '/opt/data/private/lq/data/NSD/nsddata_rawdata'\n", "    preproc_dir = '/opt/data/private/lq/data/NSD/nsddata_preprocdata/'\n", "    designmatrix_dir = '/opt/data/private/lq/data/NSD/nsddata/experiments/nsd/' \n", "\n", "    os.environ[\"SUBJECTS_DIR\"] = \"/usr/local/freesurfer/subjects\"\n", "    # Results_dir = \"/opt/data/private/lq/data/NSD/nsddata_betas/our/\"\n", "    \n", "    TR_s = 1.5\n", "    Effective_echo_spacing = 0.00027500\n", "    origvoxelSize = np.array([2.0, 2.0, 2.0])\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "99cdc94e", "metadata": {}, "outputs": [], "source": ["hem = 'lh'\n", "surf = \"inflated\"\n", "subject = 'sub02'\n", "freesurfer_sub_id_reconname = subject.replace(\"sub-\", \"sub\")\n", "################ here use lh data only ######################\n", "\n", "ROI_surf_mask_dir = {}\n", "ROI_surf_mask_dir[hem] = join(os.environ[\"SUBJECTS_DIR\"], freesurfer_sub_id_reconname, \"label\", f\"{hem}.nsdgeneral.mgz\")\n", "\n", "\n", "data_ROI_plot_inflated = nib.load(ROI_surf_mask_dir[hem]).get_fdata()\n", "print(data_ROI_plot_inflated.shape)\n", "data_ROI_plot_inflated = data_ROI_plot_inflated.squeeze()\n", "print(np.unique(data_ROI_plot_inflated))\n", "## lateral plot\n", "brain = Brain(subject, hem, surf, cortex = [np.array((239,239,239)) / 255., np.array((192,194,195)) / 255.], views=['lat'], background=\"white\", alpha = 1)         \n", "brain.add_data(data_ROI_plot_inflated, min=0,  max=1, transparent=True, center=None, colormap=\"RdBu_r\")                \n", "\n", "brain.save_image( \"HCP_Occi_inflated_lateral.png\")\n", "mlab.close()\n", "\n", "# ## medial plot\n", "# brain = Brain(subject, hem, surf, cortex = [np.array((239,239,239)) / 255., np.array((192,194,195)) / 255.], views=['med'], background=\"white\", alpha = 1)               \n", "# brain.add_data(data_ROI_plot_inflated, min=0, max=1, transparent=True, center=None, colormap=\"RdBu_r\")\n", "# # brain.save_image(os.path.join(results_dir, \"HCP_Occi_inflated_medial.png\"))            \n", "# mlab.close()\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}