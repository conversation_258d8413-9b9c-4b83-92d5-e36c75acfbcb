fmri_preprocessing_wrx_taskinput
Begin:	20250705:16:07:27.

 Preprocessing /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-04_bold.nii.gz

Change to /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04.
Command executed: slicetimer -i /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-04_bold.nii.gz -o f_skip_stc.nii.gz -r 1.6 --tcustom=/opt/data/private/lq/data/NSD/nsddata_rawdata/tcustom_epi.txt
Command executed: mcflirt -in f_skip_stc.nii.gz -out f_skip_stc_mc.nii.gz -cost normmi -meanvol -plots
Command executed: epi_reg --echospacing=0.000329994 --wmseg=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/anat_output/T1_wmseg.nii.gz                         --fmap=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/field_output/run-02_fmap_rads.nii.gz                         --fmapmag=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-02_magnitude1.nii.gz                         --fmapmagbrain=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/field_output/run-02_fmap_mag_brain.nii.gz                         --pedir=-y                         --epi=f_skip_stc_mc.nii.gz_mean_reg.nii.gz                         --t1=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/T1.nii.gz                         --t1brain=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/anat_output/T1_brain.nii.gz                         --out=f_skip_stc_mc_fm
Command executed: applywarp -i f_skip_stc_mc.nii.gz                         -r /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/T1.nii.gz                         -w f_skip_stc_mc_fm_warp.nii.gz                         --interp=spline                         -o f_skip_stc_mc_fm.nii.gz

 1-st level preprocessing for task fMRI data finished 

 /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.dof6.datfor /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/f_skip_stc_mc_fm_1vol.nii.gz exists...
Command executed: mri_convert --voxsize                     1.8 1.8 1.8                     f_skip_stc_mc_fm.nii.gz /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/f_downsampled.nii.gz
Func_to_anat register.dat, and global, wm, vcsf masks_func, lh and rh cortex masks exist for sub-01
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/global.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/global.waveform.dat
Command executed: mri_glmfit --y /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/f_downsampled.nii.gz                 --mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/wm_mask_func.nii.gz --qa --glmdir /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/glm_wm                 --pca --no-est-fwhm
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/wm_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/glm_wm/wm.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/glm_wm/wm.waveform.dat
Command executed: cp /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/glm_wm/pca-eres/u.mtx /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/glm_wm/wm.dat
Command executed: mri_glmfit --y /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/f_downsampled.nii.gz                 --mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/vcsf_mask_func.nii.gz --qa --glmdir /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/glm_vcsf                 --pca --no-est-fwhm
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/vcsf_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/glm_vcsf/vcsf.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/glm_vcsf/vcsf.waveform.dat
Command executed: cp /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/glm_vcsf/pca-eres/u.mtx /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/glm_vcsf/vcsf.dat
Get simptask, clean, forFC volume data for /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-04_bold.nii.gz.

 /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.datfor downsampled func:/opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/f_downsampled.nii.gz exists...
Error executing command: 3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/output_dir_simptask/f_simptask.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/output_dir_simptask/f_simptask_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/output_dir_simptask/f_simptask.nii.gz -detrend -bmall
Error message: Command '3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/output_dir_simptask/f_simptask.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/output_dir_simptask/f_simptask_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/output_dir_simptask/f_simptask.nii.gz -detrend -bmall' returned non-zero exit status 127.
Error executing command: 3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/output_dir_clean/f_simptask_clean.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/output_dir_clean/f_simptask_clean_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/output_dir_clean/f_simptask_clean.nii.gz -detrend -bmall
Error message: Command '3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/output_dir_clean/f_simptask_clean.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/output_dir_clean/f_simptask_clean_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/output_dir_clean/f_simptask_clean.nii.gz -detrend -bmall' returned non-zero exit status 127.
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/output_dir_simptask/f_simptask.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi lh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/surf_output_dir_simptask/lh.func.simptask.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/output_dir_clean/f_simptask_clean.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi lh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/surf_output_dir_clean/lh.func.clean.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/output_dir_simptask/f_simptask.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi rh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/surf_output_dir_simptask/rh.func.simptask.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/output_dir_clean/f_simptask_clean.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi rh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/surf_output_dir_clean/rh.func.clean.nii.gz                 --noreshape --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/surf_output_dir_simptask/lh.func.simptask.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi lh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/surf_output_dir_simptask/lh.func.simptask.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi lh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/surf_output_dir_simptask/lh.func.simptask.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/surf_output_dir_simptask/lh.func.simptask.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/surf_output_dir_clean/lh.func.clean.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi lh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/surf_output_dir_clean/lh.func.clean.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi lh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/surf_output_dir_clean/lh.func.clean.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/surf_output_dir_clean/lh.func.clean.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/surf_output_dir_simptask/rh.func.simptask.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi rh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/surf_output_dir_simptask/rh.func.simptask.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi rh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/surf_output_dir_simptask/rh.func.simptask.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/surf_output_dir_simptask/rh.func.simptask.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/surf_output_dir_clean/rh.func.clean.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi rh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/surf_output_dir_clean/rh.func.clean.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi rh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/surf_output_dir_clean/rh.func.clean.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-04/surf_output_dir_clean/rh.func.clean.fs5.sm.nii.gz                 --cortex

 Successfully preprocess the session /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-04_bold.nii.gz 

 Preprocessing /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-05_bold.nii.gz

Change to /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05.
Command executed: slicetimer -i /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-05_bold.nii.gz -o f_skip_stc.nii.gz -r 1.6 --tcustom=/opt/data/private/lq/data/NSD/nsddata_rawdata/tcustom_epi.txt
Command executed: mcflirt -in f_skip_stc.nii.gz -out f_skip_stc_mc.nii.gz -cost normmi -meanvol -plots
Command executed: epi_reg --echospacing=0.000329994 --wmseg=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/anat_output/T1_wmseg.nii.gz                         --fmap=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/field_output/run-02_fmap_rads.nii.gz                         --fmapmag=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-02_magnitude1.nii.gz                         --fmapmagbrain=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/field_output/run-02_fmap_mag_brain.nii.gz                         --pedir=-y                         --epi=f_skip_stc_mc.nii.gz_mean_reg.nii.gz                         --t1=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/T1.nii.gz                         --t1brain=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/anat_output/T1_brain.nii.gz                         --out=f_skip_stc_mc_fm
Command executed: applywarp -i f_skip_stc_mc.nii.gz                         -r /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/T1.nii.gz                         -w f_skip_stc_mc_fm_warp.nii.gz                         --interp=spline                         -o f_skip_stc_mc_fm.nii.gz

 1-st level preprocessing for task fMRI data finished 

 /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.dof6.datfor /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/f_skip_stc_mc_fm_1vol.nii.gz exists...
Command executed: mri_convert --voxsize                     1.8 1.8 1.8                     f_skip_stc_mc_fm.nii.gz /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/f_downsampled.nii.gz
Func_to_anat register.dat, and global, wm, vcsf masks_func, lh and rh cortex masks exist for sub-01
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/global.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/global.waveform.dat
Command executed: mri_glmfit --y /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/f_downsampled.nii.gz                 --mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/wm_mask_func.nii.gz --qa --glmdir /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/glm_wm                 --pca --no-est-fwhm
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/wm_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/glm_wm/wm.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/glm_wm/wm.waveform.dat
Command executed: cp /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/glm_wm/pca-eres/u.mtx /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/glm_wm/wm.dat
Command executed: mri_glmfit --y /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/f_downsampled.nii.gz                 --mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/vcsf_mask_func.nii.gz --qa --glmdir /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/glm_vcsf                 --pca --no-est-fwhm
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/vcsf_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/glm_vcsf/vcsf.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/glm_vcsf/vcsf.waveform.dat
Command executed: cp /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/glm_vcsf/pca-eres/u.mtx /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/glm_vcsf/vcsf.dat
Get simptask, clean, forFC volume data for /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-05_bold.nii.gz.

 /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.datfor downsampled func:/opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/f_downsampled.nii.gz exists...
Error executing command: 3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/output_dir_simptask/f_simptask.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/output_dir_simptask/f_simptask_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/output_dir_simptask/f_simptask.nii.gz -detrend -bmall
Error message: Command '3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/output_dir_simptask/f_simptask.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/output_dir_simptask/f_simptask_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/output_dir_simptask/f_simptask.nii.gz -detrend -bmall' returned non-zero exit status 127.
Error executing command: 3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/output_dir_clean/f_simptask_clean.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/output_dir_clean/f_simptask_clean_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/output_dir_clean/f_simptask_clean.nii.gz -detrend -bmall
Error message: Command '3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/output_dir_clean/f_simptask_clean.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/output_dir_clean/f_simptask_clean_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/output_dir_clean/f_simptask_clean.nii.gz -detrend -bmall' returned non-zero exit status 127.
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/output_dir_simptask/f_simptask.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi lh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/surf_output_dir_simptask/lh.func.simptask.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/output_dir_clean/f_simptask_clean.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi lh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/surf_output_dir_clean/lh.func.clean.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/output_dir_simptask/f_simptask.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi rh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/surf_output_dir_simptask/rh.func.simptask.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/output_dir_clean/f_simptask_clean.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi rh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/surf_output_dir_clean/rh.func.clean.nii.gz                 --noreshape --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/surf_output_dir_simptask/lh.func.simptask.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi lh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/surf_output_dir_simptask/lh.func.simptask.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi lh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/surf_output_dir_simptask/lh.func.simptask.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/surf_output_dir_simptask/lh.func.simptask.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/surf_output_dir_clean/lh.func.clean.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi lh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/surf_output_dir_clean/lh.func.clean.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi lh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/surf_output_dir_clean/lh.func.clean.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/surf_output_dir_clean/lh.func.clean.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/surf_output_dir_simptask/rh.func.simptask.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi rh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/surf_output_dir_simptask/rh.func.simptask.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi rh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/surf_output_dir_simptask/rh.func.simptask.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/surf_output_dir_simptask/rh.func.simptask.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/surf_output_dir_clean/rh.func.clean.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi rh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/surf_output_dir_clean/rh.func.clean.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi rh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/surf_output_dir_clean/rh.func.clean.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-05/surf_output_dir_clean/rh.func.clean.fs5.sm.nii.gz                 --cortex

 Successfully preprocess the session /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-05_bold.nii.gz 

 Preprocessing /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-06_bold.nii.gz

Change to /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06.
Command executed: slicetimer -i /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-06_bold.nii.gz -o f_skip_stc.nii.gz -r 1.6 --tcustom=/opt/data/private/lq/data/NSD/nsddata_rawdata/tcustom_epi.txt
Command executed: mcflirt -in f_skip_stc.nii.gz -out f_skip_stc_mc.nii.gz -cost normmi -meanvol -plots
Command executed: epi_reg --echospacing=0.000329994 --wmseg=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/anat_output/T1_wmseg.nii.gz                         --fmap=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/field_output/run-02_fmap_rads.nii.gz                         --fmapmag=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-02_magnitude1.nii.gz                         --fmapmagbrain=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/field_output/run-02_fmap_mag_brain.nii.gz                         --pedir=-y                         --epi=f_skip_stc_mc.nii.gz_mean_reg.nii.gz                         --t1=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/T1.nii.gz                         --t1brain=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/anat_output/T1_brain.nii.gz                         --out=f_skip_stc_mc_fm
Command executed: applywarp -i f_skip_stc_mc.nii.gz                         -r /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/T1.nii.gz                         -w f_skip_stc_mc_fm_warp.nii.gz                         --interp=spline                         -o f_skip_stc_mc_fm.nii.gz

 1-st level preprocessing for task fMRI data finished 

 /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.dof6.datfor /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/f_skip_stc_mc_fm_1vol.nii.gz exists...
Command executed: mri_convert --voxsize                     1.8 1.8 1.8                     f_skip_stc_mc_fm.nii.gz /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/f_downsampled.nii.gz
Func_to_anat register.dat, and global, wm, vcsf masks_func, lh and rh cortex masks exist for sub-01
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/global.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/global.waveform.dat
Command executed: mri_glmfit --y /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/f_downsampled.nii.gz                 --mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/wm_mask_func.nii.gz --qa --glmdir /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/glm_wm                 --pca --no-est-fwhm
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/wm_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/glm_wm/wm.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/glm_wm/wm.waveform.dat
Command executed: cp /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/glm_wm/pca-eres/u.mtx /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/glm_wm/wm.dat
Command executed: mri_glmfit --y /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/f_downsampled.nii.gz                 --mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/vcsf_mask_func.nii.gz --qa --glmdir /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/glm_vcsf                 --pca --no-est-fwhm
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/vcsf_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/glm_vcsf/vcsf.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/glm_vcsf/vcsf.waveform.dat
Command executed: cp /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/glm_vcsf/pca-eres/u.mtx /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/glm_vcsf/vcsf.dat
Get simptask, clean, forFC volume data for /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-06_bold.nii.gz.

 /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.datfor downsampled func:/opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/f_downsampled.nii.gz exists...
Error executing command: 3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/output_dir_simptask/f_simptask.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/output_dir_simptask/f_simptask_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/output_dir_simptask/f_simptask.nii.gz -detrend -bmall
Error message: Command '3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/output_dir_simptask/f_simptask.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/output_dir_simptask/f_simptask_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/output_dir_simptask/f_simptask.nii.gz -detrend -bmall' returned non-zero exit status 127.
Error executing command: 3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/output_dir_clean/f_simptask_clean.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/output_dir_clean/f_simptask_clean_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/output_dir_clean/f_simptask_clean.nii.gz -detrend -bmall
Error message: Command '3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/output_dir_clean/f_simptask_clean.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/output_dir_clean/f_simptask_clean_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/output_dir_clean/f_simptask_clean.nii.gz -detrend -bmall' returned non-zero exit status 127.
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/output_dir_simptask/f_simptask.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi lh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/surf_output_dir_simptask/lh.func.simptask.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/output_dir_clean/f_simptask_clean.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi lh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/surf_output_dir_clean/lh.func.clean.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/output_dir_simptask/f_simptask.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi rh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/surf_output_dir_simptask/rh.func.simptask.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/output_dir_clean/f_simptask_clean.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi rh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/surf_output_dir_clean/rh.func.clean.nii.gz                 --noreshape --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/surf_output_dir_simptask/lh.func.simptask.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi lh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/surf_output_dir_simptask/lh.func.simptask.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi lh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/surf_output_dir_simptask/lh.func.simptask.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/surf_output_dir_simptask/lh.func.simptask.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/surf_output_dir_clean/lh.func.clean.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi lh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/surf_output_dir_clean/lh.func.clean.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi lh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/surf_output_dir_clean/lh.func.clean.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/surf_output_dir_clean/lh.func.clean.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/surf_output_dir_simptask/rh.func.simptask.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi rh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/surf_output_dir_simptask/rh.func.simptask.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi rh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/surf_output_dir_simptask/rh.func.simptask.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/surf_output_dir_simptask/rh.func.simptask.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/surf_output_dir_clean/rh.func.clean.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi rh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/surf_output_dir_clean/rh.func.clean.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi rh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/surf_output_dir_clean/rh.func.clean.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-06/surf_output_dir_clean/rh.func.clean.fs5.sm.nii.gz                 --cortex

 Successfully preprocess the session /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-06_bold.nii.gz 

 Preprocessing /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-07_bold.nii.gz

Change to /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07.
Command executed: slicetimer -i /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-07_bold.nii.gz -o f_skip_stc.nii.gz -r 1.6 --tcustom=/opt/data/private/lq/data/NSD/nsddata_rawdata/tcustom_epi.txt
Command executed: mcflirt -in f_skip_stc.nii.gz -out f_skip_stc_mc.nii.gz -cost normmi -meanvol -plots
Command executed: epi_reg --echospacing=0.000329994 --wmseg=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/anat_output/T1_wmseg.nii.gz                         --fmap=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/field_output/run-03_fmap_rads.nii.gz                         --fmapmag=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-03_magnitude1.nii.gz                         --fmapmagbrain=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/field_output/run-03_fmap_mag_brain.nii.gz                         --pedir=-y                         --epi=f_skip_stc_mc.nii.gz_mean_reg.nii.gz                         --t1=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/T1.nii.gz                         --t1brain=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/anat_output/T1_brain.nii.gz                         --out=f_skip_stc_mc_fm
Command executed: applywarp -i f_skip_stc_mc.nii.gz                         -r /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/T1.nii.gz                         -w f_skip_stc_mc_fm_warp.nii.gz                         --interp=spline                         -o f_skip_stc_mc_fm.nii.gz

 1-st level preprocessing for task fMRI data finished 

 /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.dof6.datfor /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/f_skip_stc_mc_fm_1vol.nii.gz exists...
Command executed: mri_convert --voxsize                     1.8 1.8 1.8                     f_skip_stc_mc_fm.nii.gz /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/f_downsampled.nii.gz
Func_to_anat register.dat, and global, wm, vcsf masks_func, lh and rh cortex masks exist for sub-01
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/global.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/global.waveform.dat
Command executed: mri_glmfit --y /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/f_downsampled.nii.gz                 --mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/wm_mask_func.nii.gz --qa --glmdir /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/glm_wm                 --pca --no-est-fwhm
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/wm_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/glm_wm/wm.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/glm_wm/wm.waveform.dat
Command executed: cp /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/glm_wm/pca-eres/u.mtx /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/glm_wm/wm.dat
Command executed: mri_glmfit --y /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/f_downsampled.nii.gz                 --mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/vcsf_mask_func.nii.gz --qa --glmdir /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/glm_vcsf                 --pca --no-est-fwhm
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/vcsf_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/glm_vcsf/vcsf.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/glm_vcsf/vcsf.waveform.dat
Command executed: cp /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/glm_vcsf/pca-eres/u.mtx /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/glm_vcsf/vcsf.dat
Get simptask, clean, forFC volume data for /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-07_bold.nii.gz.

 /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.datfor downsampled func:/opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/f_downsampled.nii.gz exists...
Error executing command: 3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/output_dir_simptask/f_simptask.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/output_dir_simptask/f_simptask_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/output_dir_simptask/f_simptask.nii.gz -detrend -bmall
Error message: Command '3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/output_dir_simptask/f_simptask.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/output_dir_simptask/f_simptask_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/output_dir_simptask/f_simptask.nii.gz -detrend -bmall' returned non-zero exit status 127.
Error executing command: 3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/output_dir_clean/f_simptask_clean.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/output_dir_clean/f_simptask_clean_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/output_dir_clean/f_simptask_clean.nii.gz -detrend -bmall
Error message: Command '3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/output_dir_clean/f_simptask_clean.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/output_dir_clean/f_simptask_clean_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/output_dir_clean/f_simptask_clean.nii.gz -detrend -bmall' returned non-zero exit status 127.
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/output_dir_simptask/f_simptask.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi lh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/surf_output_dir_simptask/lh.func.simptask.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/output_dir_clean/f_simptask_clean.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi lh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/surf_output_dir_clean/lh.func.clean.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/output_dir_simptask/f_simptask.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi rh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/surf_output_dir_simptask/rh.func.simptask.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/output_dir_clean/f_simptask_clean.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi rh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/surf_output_dir_clean/rh.func.clean.nii.gz                 --noreshape --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/surf_output_dir_simptask/lh.func.simptask.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi lh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/surf_output_dir_simptask/lh.func.simptask.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi lh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/surf_output_dir_simptask/lh.func.simptask.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/surf_output_dir_simptask/lh.func.simptask.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/surf_output_dir_clean/lh.func.clean.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi lh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/surf_output_dir_clean/lh.func.clean.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi lh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/surf_output_dir_clean/lh.func.clean.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/surf_output_dir_clean/lh.func.clean.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/surf_output_dir_simptask/rh.func.simptask.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi rh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/surf_output_dir_simptask/rh.func.simptask.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi rh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/surf_output_dir_simptask/rh.func.simptask.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/surf_output_dir_simptask/rh.func.simptask.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/surf_output_dir_clean/rh.func.clean.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi rh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/surf_output_dir_clean/rh.func.clean.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi rh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/surf_output_dir_clean/rh.func.clean.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-07/surf_output_dir_clean/rh.func.clean.fs5.sm.nii.gz                 --cortex

 Successfully preprocess the session /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-07_bold.nii.gz 

 Preprocessing /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-08_bold.nii.gz

Change to /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08.
Command executed: slicetimer -i /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-08_bold.nii.gz -o f_skip_stc.nii.gz -r 1.6 --tcustom=/opt/data/private/lq/data/NSD/nsddata_rawdata/tcustom_epi.txt
Command executed: mcflirt -in f_skip_stc.nii.gz -out f_skip_stc_mc.nii.gz -cost normmi -meanvol -plots
Command executed: epi_reg --echospacing=0.000329994 --wmseg=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/anat_output/T1_wmseg.nii.gz                         --fmap=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/field_output/run-03_fmap_rads.nii.gz                         --fmapmag=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-03_magnitude1.nii.gz                         --fmapmagbrain=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/field_output/run-03_fmap_mag_brain.nii.gz                         --pedir=-y                         --epi=f_skip_stc_mc.nii.gz_mean_reg.nii.gz                         --t1=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/T1.nii.gz                         --t1brain=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/anat_output/T1_brain.nii.gz                         --out=f_skip_stc_mc_fm
Command executed: applywarp -i f_skip_stc_mc.nii.gz                         -r /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/T1.nii.gz                         -w f_skip_stc_mc_fm_warp.nii.gz                         --interp=spline                         -o f_skip_stc_mc_fm.nii.gz

 1-st level preprocessing for task fMRI data finished 

 /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.dof6.datfor /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/f_skip_stc_mc_fm_1vol.nii.gz exists...
Command executed: mri_convert --voxsize                     1.8 1.8 1.8                     f_skip_stc_mc_fm.nii.gz /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/f_downsampled.nii.gz
Func_to_anat register.dat, and global, wm, vcsf masks_func, lh and rh cortex masks exist for sub-01
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/global.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/global.waveform.dat
Command executed: mri_glmfit --y /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/f_downsampled.nii.gz                 --mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/wm_mask_func.nii.gz --qa --glmdir /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/glm_wm                 --pca --no-est-fwhm
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/wm_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/glm_wm/wm.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/glm_wm/wm.waveform.dat
Command executed: cp /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/glm_wm/pca-eres/u.mtx /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/glm_wm/wm.dat
Command executed: mri_glmfit --y /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/f_downsampled.nii.gz                 --mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/vcsf_mask_func.nii.gz --qa --glmdir /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/glm_vcsf                 --pca --no-est-fwhm
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/vcsf_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/glm_vcsf/vcsf.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/glm_vcsf/vcsf.waveform.dat
Command executed: cp /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/glm_vcsf/pca-eres/u.mtx /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/glm_vcsf/vcsf.dat
Get simptask, clean, forFC volume data for /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-08_bold.nii.gz.

 /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.datfor downsampled func:/opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/f_downsampled.nii.gz exists...
Error executing command: 3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/output_dir_simptask/f_simptask.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/output_dir_simptask/f_simptask_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/output_dir_simptask/f_simptask.nii.gz -detrend -bmall
Error message: Command '3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/output_dir_simptask/f_simptask.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/output_dir_simptask/f_simptask_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/output_dir_simptask/f_simptask.nii.gz -detrend -bmall' returned non-zero exit status 127.
Error executing command: 3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/output_dir_clean/f_simptask_clean.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/output_dir_clean/f_simptask_clean_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/output_dir_clean/f_simptask_clean.nii.gz -detrend -bmall
Error message: Command '3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/output_dir_clean/f_simptask_clean.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/output_dir_clean/f_simptask_clean_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/output_dir_clean/f_simptask_clean.nii.gz -detrend -bmall' returned non-zero exit status 127.
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/output_dir_simptask/f_simptask.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi lh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/surf_output_dir_simptask/lh.func.simptask.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/output_dir_clean/f_simptask_clean.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi lh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/surf_output_dir_clean/lh.func.clean.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/output_dir_simptask/f_simptask.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi rh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/surf_output_dir_simptask/rh.func.simptask.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/output_dir_clean/f_simptask_clean.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi rh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/surf_output_dir_clean/rh.func.clean.nii.gz                 --noreshape --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/surf_output_dir_simptask/lh.func.simptask.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi lh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/surf_output_dir_simptask/lh.func.simptask.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi lh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/surf_output_dir_simptask/lh.func.simptask.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/surf_output_dir_simptask/lh.func.simptask.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/surf_output_dir_clean/lh.func.clean.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi lh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/surf_output_dir_clean/lh.func.clean.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi lh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/surf_output_dir_clean/lh.func.clean.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/surf_output_dir_clean/lh.func.clean.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/surf_output_dir_simptask/rh.func.simptask.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi rh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/surf_output_dir_simptask/rh.func.simptask.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi rh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/surf_output_dir_simptask/rh.func.simptask.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/surf_output_dir_simptask/rh.func.simptask.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/surf_output_dir_clean/rh.func.clean.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi rh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/surf_output_dir_clean/rh.func.clean.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi rh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/surf_output_dir_clean/rh.func.clean.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-08/surf_output_dir_clean/rh.func.clean.fs5.sm.nii.gz                 --cortex

 Successfully preprocess the session /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-08_bold.nii.gz 

 Preprocessing /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-09_bold.nii.gz

Change to /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09.
Command executed: slicetimer -i /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-09_bold.nii.gz -o f_skip_stc.nii.gz -r 1.6 --tcustom=/opt/data/private/lq/data/NSD/nsddata_rawdata/tcustom_epi.txt
Command executed: mcflirt -in f_skip_stc.nii.gz -out f_skip_stc_mc.nii.gz -cost normmi -meanvol -plots
Command executed: epi_reg --echospacing=0.000329994 --wmseg=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/anat_output/T1_wmseg.nii.gz                         --fmap=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/field_output/run-03_fmap_rads.nii.gz                         --fmapmag=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-03_magnitude1.nii.gz                         --fmapmagbrain=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/field_output/run-03_fmap_mag_brain.nii.gz                         --pedir=-y                         --epi=f_skip_stc_mc.nii.gz_mean_reg.nii.gz                         --t1=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/T1.nii.gz                         --t1brain=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/anat_output/T1_brain.nii.gz                         --out=f_skip_stc_mc_fm
Command executed: applywarp -i f_skip_stc_mc.nii.gz                         -r /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/T1.nii.gz                         -w f_skip_stc_mc_fm_warp.nii.gz                         --interp=spline                         -o f_skip_stc_mc_fm.nii.gz

 1-st level preprocessing for task fMRI data finished 

 /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.dof6.datfor /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/f_skip_stc_mc_fm_1vol.nii.gz exists...
Command executed: mri_convert --voxsize                     1.8 1.8 1.8                     f_skip_stc_mc_fm.nii.gz /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/f_downsampled.nii.gz
Func_to_anat register.dat, and global, wm, vcsf masks_func, lh and rh cortex masks exist for sub-01
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/global.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/global.waveform.dat
Command executed: mri_glmfit --y /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/f_downsampled.nii.gz                 --mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/wm_mask_func.nii.gz --qa --glmdir /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/glm_wm                 --pca --no-est-fwhm
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/wm_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/glm_wm/wm.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/glm_wm/wm.waveform.dat
Command executed: cp /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/glm_wm/pca-eres/u.mtx /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/glm_wm/wm.dat
Command executed: mri_glmfit --y /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/f_downsampled.nii.gz                 --mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/vcsf_mask_func.nii.gz --qa --glmdir /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/glm_vcsf                 --pca --no-est-fwhm
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/vcsf_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/glm_vcsf/vcsf.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/glm_vcsf/vcsf.waveform.dat
Command executed: cp /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/glm_vcsf/pca-eres/u.mtx /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/glm_vcsf/vcsf.dat
Get simptask, clean, forFC volume data for /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-09_bold.nii.gz.

 /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.datfor downsampled func:/opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/f_downsampled.nii.gz exists...
Error executing command: 3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/output_dir_simptask/f_simptask.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/output_dir_simptask/f_simptask_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/output_dir_simptask/f_simptask.nii.gz -detrend -bmall
Error message: Command '3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/output_dir_simptask/f_simptask.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/output_dir_simptask/f_simptask_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/output_dir_simptask/f_simptask.nii.gz -detrend -bmall' returned non-zero exit status 127.
Error executing command: 3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/output_dir_clean/f_simptask_clean.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/output_dir_clean/f_simptask_clean_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/output_dir_clean/f_simptask_clean.nii.gz -detrend -bmall
Error message: Command '3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/output_dir_clean/f_simptask_clean.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/output_dir_clean/f_simptask_clean_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/output_dir_clean/f_simptask_clean.nii.gz -detrend -bmall' returned non-zero exit status 127.
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/output_dir_simptask/f_simptask.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi lh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/surf_output_dir_simptask/lh.func.simptask.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/output_dir_clean/f_simptask_clean.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi lh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/surf_output_dir_clean/lh.func.clean.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/output_dir_simptask/f_simptask.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi rh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/surf_output_dir_simptask/rh.func.simptask.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/output_dir_clean/f_simptask_clean.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi rh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/surf_output_dir_clean/rh.func.clean.nii.gz                 --noreshape --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/surf_output_dir_simptask/lh.func.simptask.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi lh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/surf_output_dir_simptask/lh.func.simptask.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi lh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/surf_output_dir_simptask/lh.func.simptask.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/surf_output_dir_simptask/lh.func.simptask.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/surf_output_dir_clean/lh.func.clean.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi lh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/surf_output_dir_clean/lh.func.clean.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi lh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/surf_output_dir_clean/lh.func.clean.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/surf_output_dir_clean/lh.func.clean.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/surf_output_dir_simptask/rh.func.simptask.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi rh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/surf_output_dir_simptask/rh.func.simptask.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi rh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/surf_output_dir_simptask/rh.func.simptask.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/surf_output_dir_simptask/rh.func.simptask.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/surf_output_dir_clean/rh.func.clean.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi rh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/surf_output_dir_clean/rh.func.clean.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi rh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/surf_output_dir_clean/rh.func.clean.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-09/surf_output_dir_clean/rh.func.clean.fs5.sm.nii.gz                 --cortex

 Successfully preprocess the session /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-09_bold.nii.gz 

 Preprocessing /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-10_bold.nii.gz

Change to /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10.
Command executed: slicetimer -i /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-10_bold.nii.gz -o f_skip_stc.nii.gz -r 1.6 --tcustom=/opt/data/private/lq/data/NSD/nsddata_rawdata/tcustom_epi.txt
Command executed: mcflirt -in f_skip_stc.nii.gz -out f_skip_stc_mc.nii.gz -cost normmi -meanvol -plots
Command executed: epi_reg --echospacing=0.000329994 --wmseg=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/anat_output/T1_wmseg.nii.gz                         --fmap=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/field_output/run-04_fmap_rads.nii.gz                         --fmapmag=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-04_magnitude1.nii.gz                         --fmapmagbrain=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/field_output/run-04_fmap_mag_brain.nii.gz                         --pedir=-y                         --epi=f_skip_stc_mc.nii.gz_mean_reg.nii.gz                         --t1=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/T1.nii.gz                         --t1brain=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/anat_output/T1_brain.nii.gz                         --out=f_skip_stc_mc_fm
Command executed: applywarp -i f_skip_stc_mc.nii.gz                         -r /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/T1.nii.gz                         -w f_skip_stc_mc_fm_warp.nii.gz                         --interp=spline                         -o f_skip_stc_mc_fm.nii.gz

 1-st level preprocessing for task fMRI data finished 

 /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.dof6.datfor /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/f_skip_stc_mc_fm_1vol.nii.gz exists...
Command executed: mri_convert --voxsize                     1.8 1.8 1.8                     f_skip_stc_mc_fm.nii.gz /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/f_downsampled.nii.gz
Func_to_anat register.dat, and global, wm, vcsf masks_func, lh and rh cortex masks exist for sub-01
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/global.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/global.waveform.dat
Command executed: mri_glmfit --y /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/f_downsampled.nii.gz                 --mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/wm_mask_func.nii.gz --qa --glmdir /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/glm_wm                 --pca --no-est-fwhm
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/wm_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/glm_wm/wm.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/glm_wm/wm.waveform.dat
Command executed: cp /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/glm_wm/pca-eres/u.mtx /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/glm_wm/wm.dat
Command executed: mri_glmfit --y /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/f_downsampled.nii.gz                 --mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/vcsf_mask_func.nii.gz --qa --glmdir /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/glm_vcsf                 --pca --no-est-fwhm
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/vcsf_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/glm_vcsf/vcsf.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/glm_vcsf/vcsf.waveform.dat
Command executed: cp /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/glm_vcsf/pca-eres/u.mtx /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/glm_vcsf/vcsf.dat
Get simptask, clean, forFC volume data for /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-10_bold.nii.gz.

 /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.datfor downsampled func:/opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/f_downsampled.nii.gz exists...
Error executing command: 3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/output_dir_simptask/f_simptask.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/output_dir_simptask/f_simptask_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/output_dir_simptask/f_simptask.nii.gz -detrend -bmall
Error message: Command '3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/output_dir_simptask/f_simptask.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/output_dir_simptask/f_simptask_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/output_dir_simptask/f_simptask.nii.gz -detrend -bmall' returned non-zero exit status 127.
Error executing command: 3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/output_dir_clean/f_simptask_clean.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/output_dir_clean/f_simptask_clean_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/output_dir_clean/f_simptask_clean.nii.gz -detrend -bmall
Error message: Command '3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/output_dir_clean/f_simptask_clean.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/output_dir_clean/f_simptask_clean_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/output_dir_clean/f_simptask_clean.nii.gz -detrend -bmall' returned non-zero exit status 127.
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/output_dir_simptask/f_simptask.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi lh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/surf_output_dir_simptask/lh.func.simptask.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/output_dir_clean/f_simptask_clean.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi lh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/surf_output_dir_clean/lh.func.clean.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/output_dir_simptask/f_simptask.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi rh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/surf_output_dir_simptask/rh.func.simptask.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/output_dir_clean/f_simptask_clean.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi rh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/surf_output_dir_clean/rh.func.clean.nii.gz                 --noreshape --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/surf_output_dir_simptask/lh.func.simptask.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi lh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/surf_output_dir_simptask/lh.func.simptask.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi lh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/surf_output_dir_simptask/lh.func.simptask.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/surf_output_dir_simptask/lh.func.simptask.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/surf_output_dir_clean/lh.func.clean.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi lh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/surf_output_dir_clean/lh.func.clean.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi lh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/surf_output_dir_clean/lh.func.clean.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/surf_output_dir_clean/lh.func.clean.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/surf_output_dir_simptask/rh.func.simptask.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi rh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/surf_output_dir_simptask/rh.func.simptask.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi rh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/surf_output_dir_simptask/rh.func.simptask.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/surf_output_dir_simptask/rh.func.simptask.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/surf_output_dir_clean/rh.func.clean.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi rh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/surf_output_dir_clean/rh.func.clean.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi rh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/surf_output_dir_clean/rh.func.clean.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-10/surf_output_dir_clean/rh.func.clean.fs5.sm.nii.gz                 --cortex

 Successfully preprocess the session /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-10_bold.nii.gz 

 Preprocessing /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-11_bold.nii.gz

Change to /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11.
Command executed: slicetimer -i /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-11_bold.nii.gz -o f_skip_stc.nii.gz -r 1.6 --tcustom=/opt/data/private/lq/data/NSD/nsddata_rawdata/tcustom_epi.txt
Command executed: mcflirt -in f_skip_stc.nii.gz -out f_skip_stc_mc.nii.gz -cost normmi -meanvol -plots
Command executed: epi_reg --echospacing=0.000329994 --wmseg=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/anat_output/T1_wmseg.nii.gz                         --fmap=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/field_output/run-04_fmap_rads.nii.gz                         --fmapmag=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-04_magnitude1.nii.gz                         --fmapmagbrain=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/field_output/run-04_fmap_mag_brain.nii.gz                         --pedir=-y                         --epi=f_skip_stc_mc.nii.gz_mean_reg.nii.gz                         --t1=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/T1.nii.gz                         --t1brain=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/anat_output/T1_brain.nii.gz                         --out=f_skip_stc_mc_fm
Command executed: applywarp -i f_skip_stc_mc.nii.gz                         -r /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/T1.nii.gz                         -w f_skip_stc_mc_fm_warp.nii.gz                         --interp=spline                         -o f_skip_stc_mc_fm.nii.gz

 1-st level preprocessing for task fMRI data finished 

 /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.dof6.datfor /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/f_skip_stc_mc_fm_1vol.nii.gz exists...
Command executed: mri_convert --voxsize                     1.8 1.8 1.8                     f_skip_stc_mc_fm.nii.gz /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/f_downsampled.nii.gz
Func_to_anat register.dat, and global, wm, vcsf masks_func, lh and rh cortex masks exist for sub-01
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/global.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/global.waveform.dat
Command executed: mri_glmfit --y /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/f_downsampled.nii.gz                 --mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/wm_mask_func.nii.gz --qa --glmdir /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/glm_wm                 --pca --no-est-fwhm
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/wm_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/glm_wm/wm.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/glm_wm/wm.waveform.dat
Command executed: cp /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/glm_wm/pca-eres/u.mtx /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/glm_wm/wm.dat
Command executed: mri_glmfit --y /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/f_downsampled.nii.gz                 --mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/vcsf_mask_func.nii.gz --qa --glmdir /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/glm_vcsf                 --pca --no-est-fwhm
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/vcsf_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/glm_vcsf/vcsf.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/glm_vcsf/vcsf.waveform.dat
Command executed: cp /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/glm_vcsf/pca-eres/u.mtx /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/glm_vcsf/vcsf.dat
Get simptask, clean, forFC volume data for /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-11_bold.nii.gz.

 /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.datfor downsampled func:/opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/f_downsampled.nii.gz exists...
Error executing command: 3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/output_dir_simptask/f_simptask.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/output_dir_simptask/f_simptask_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/output_dir_simptask/f_simptask.nii.gz -detrend -bmall
Error message: Command '3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/output_dir_simptask/f_simptask.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/output_dir_simptask/f_simptask_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/output_dir_simptask/f_simptask.nii.gz -detrend -bmall' returned non-zero exit status 127.
Error executing command: 3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/output_dir_clean/f_simptask_clean.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/output_dir_clean/f_simptask_clean_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/output_dir_clean/f_simptask_clean.nii.gz -detrend -bmall
Error message: Command '3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/output_dir_clean/f_simptask_clean.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/output_dir_clean/f_simptask_clean_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/output_dir_clean/f_simptask_clean.nii.gz -detrend -bmall' returned non-zero exit status 127.
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/output_dir_simptask/f_simptask.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi lh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/surf_output_dir_simptask/lh.func.simptask.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/output_dir_clean/f_simptask_clean.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi lh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/surf_output_dir_clean/lh.func.clean.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/output_dir_simptask/f_simptask.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi rh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/surf_output_dir_simptask/rh.func.simptask.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/output_dir_clean/f_simptask_clean.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi rh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/surf_output_dir_clean/rh.func.clean.nii.gz                 --noreshape --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/surf_output_dir_simptask/lh.func.simptask.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi lh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/surf_output_dir_simptask/lh.func.simptask.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi lh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/surf_output_dir_simptask/lh.func.simptask.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/surf_output_dir_simptask/lh.func.simptask.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/surf_output_dir_clean/lh.func.clean.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi lh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/surf_output_dir_clean/lh.func.clean.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi lh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/surf_output_dir_clean/lh.func.clean.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/surf_output_dir_clean/lh.func.clean.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/surf_output_dir_simptask/rh.func.simptask.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi rh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/surf_output_dir_simptask/rh.func.simptask.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi rh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/surf_output_dir_simptask/rh.func.simptask.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/surf_output_dir_simptask/rh.func.simptask.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/surf_output_dir_clean/rh.func.clean.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi rh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/surf_output_dir_clean/rh.func.clean.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi rh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/surf_output_dir_clean/rh.func.clean.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-11/surf_output_dir_clean/rh.func.clean.fs5.sm.nii.gz                 --cortex

 Successfully preprocess the session /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-11_bold.nii.gz 

 Preprocessing /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-12_bold.nii.gz

Change to /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12.
Command executed: slicetimer -i /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-12_bold.nii.gz -o f_skip_stc.nii.gz -r 1.6 --tcustom=/opt/data/private/lq/data/NSD/nsddata_rawdata/tcustom_epi.txt
Command executed: mcflirt -in f_skip_stc.nii.gz -out f_skip_stc_mc.nii.gz -cost normmi -meanvol -plots
Command executed: epi_reg --echospacing=0.000329994 --wmseg=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/anat_output/T1_wmseg.nii.gz                         --fmap=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/field_output/run-04_fmap_rads.nii.gz                         --fmapmag=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/sub-01_ses-nsd01_run-04_magnitude1.nii.gz                         --fmapmagbrain=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/fmap/field_output/run-04_fmap_mag_brain.nii.gz                         --pedir=-y                         --epi=f_skip_stc_mc.nii.gz_mean_reg.nii.gz                         --t1=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/T1.nii.gz                         --t1brain=/opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/anat_output/T1_brain.nii.gz                         --out=f_skip_stc_mc_fm
Command executed: applywarp -i f_skip_stc_mc.nii.gz                         -r /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/anat/T1.nii.gz                         -w f_skip_stc_mc_fm_warp.nii.gz                         --interp=spline                         -o f_skip_stc_mc_fm.nii.gz

 1-st level preprocessing for task fMRI data finished 

 /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.dof6.datfor /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/f_skip_stc_mc_fm_1vol.nii.gz exists...
Command executed: mri_convert --voxsize                     1.8 1.8 1.8                     f_skip_stc_mc_fm.nii.gz /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/f_downsampled.nii.gz
Func_to_anat register.dat, and global, wm, vcsf masks_func, lh and rh cortex masks exist for sub-01
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/global.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/global.waveform.dat
Command executed: mri_glmfit --y /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/f_downsampled.nii.gz                 --mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/wm_mask_func.nii.gz --qa --glmdir /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/glm_wm                 --pca --no-est-fwhm
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/wm_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/glm_wm/wm.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/glm_wm/wm.waveform.dat
Command executed: cp /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/glm_wm/pca-eres/u.mtx /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/glm_wm/wm.dat
Command executed: mri_glmfit --y /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/f_downsampled.nii.gz                 --mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/vcsf_mask_func.nii.gz --qa --glmdir /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/glm_vcsf                 --pca --no-est-fwhm
Command executed: meanval --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/f_downsampled.nii.gz                 --m /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/vcsf_mask_func.nii.gz --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/glm_vcsf/vcsf.meanval.dat --avgwf /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/glm_vcsf/vcsf.waveform.dat
Command executed: cp /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/glm_vcsf/pca-eres/u.mtx /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/glm_vcsf/vcsf.dat
Get simptask, clean, forFC volume data for /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-12_bold.nii.gz.

 /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.datfor downsampled func:/opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/f_downsampled.nii.gz exists...
Error executing command: 3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/output_dir_simptask/f_simptask.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/output_dir_simptask/f_simptask_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/output_dir_simptask/f_simptask.nii.gz -detrend -bmall
Error message: Command '3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/output_dir_simptask/f_simptask.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/output_dir_simptask/f_simptask_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/output_dir_simptask/f_simptask.nii.gz -detrend -bmall' returned non-zero exit status 127.
Error executing command: 3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/output_dir_clean/f_simptask_clean.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/output_dir_clean/f_simptask_clean_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/output_dir_clean/f_simptask_clean.nii.gz -detrend -bmall
Error message: Command '3dBlurToFWHM -mask /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ROIs/global_mask_func.nii.gz -FWHM 6                 -input /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/output_dir_clean/f_simptask_clean.nii.gz -prefix /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/output_dir_clean/f_simptask_clean_sm.nii.gz                 -blurmaster /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/output_dir_clean/f_simptask_clean.nii.gz -detrend -bmall' returned non-zero exit status 127.
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/output_dir_simptask/f_simptask.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi lh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/surf_output_dir_simptask/lh.func.simptask.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/output_dir_clean/f_simptask_clean.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi lh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/surf_output_dir_clean/lh.func.clean.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/output_dir_simptask/f_simptask.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi rh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/surf_output_dir_simptask/rh.func.simptask.nii.gz                 --noreshape --cortex
Command executed: mri_vol2surf --mov /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/output_dir_clean/f_simptask_clean.nii.gz                 --reg /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/reg_output/register.downsampledfunc.dof6.dat                 --trgsubject subj01                 --interp trilin --projfrac 0.5                 --hemi rh --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/surf_output_dir_clean/rh.func.clean.nii.gz                 --noreshape --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/surf_output_dir_simptask/lh.func.simptask.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi lh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/surf_output_dir_simptask/lh.func.simptask.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi lh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/surf_output_dir_simptask/lh.func.simptask.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/surf_output_dir_simptask/lh.func.simptask.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/surf_output_dir_clean/lh.func.clean.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi lh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/surf_output_dir_clean/lh.func.clean.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi lh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/surf_output_dir_clean/lh.func.clean.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/surf_output_dir_clean/lh.func.clean.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/surf_output_dir_simptask/rh.func.simptask.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi rh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/surf_output_dir_simptask/rh.func.simptask.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi rh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/surf_output_dir_simptask/rh.func.simptask.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/surf_output_dir_simptask/rh.func.simptask.fs5.sm.nii.gz                 --cortex
Command executed: mri_surf2surf --sval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/surf_output_dir_clean/rh.func.clean.nii.gz                 --srcsubject subj01                 --trgsubject fsaverage5                 --hemi rh --tval /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/surf_output_dir_clean/rh.func.clean.fs5.nii.gz                 --cortex
Command executed: mris_fwhm --s fsaverage5                 --hemi rh --smooth-only                 --i /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/surf_output_dir_clean/rh.func.clean.fs5.nii.gz                 --fwhm 6                 --o /opt/data/private/lq/data/NSD/nsddata_preprocdata/sub-01/ses-nsd01/run-12/surf_output_dir_clean/rh.func.clean.fs5.sm.nii.gz                 --cortex

 Successfully preprocess the session /opt/data/private/lq/data/NSD/nsddata_rawdata/sub-01/ses-nsd01/func/sub-01_ses-nsd01_task-nsdcore_run-12_bold.nii.gz 

 Successfully run preproc-sess of sub-01

End:	20250706:11:53:11.
