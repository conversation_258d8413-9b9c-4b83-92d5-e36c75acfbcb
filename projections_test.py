#!/usr/bin/env python3
"""
测试版本的投影脚本
用于验证session搜索和路径设置是否正确
"""

import numpy as np
import os
import sys
import re
import glob
from os.path import join, exists, split
import h5py
import nibabel as nib

# 添加 rootfolder 到 sys.path
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), "..")))

# 添加 FSL bin 路径到环境变量，便于调用fsl命令
os.environ["FSLDIR"] = "/usr/local/fsl"
os.environ["PATH"] += os.pathsep + "/usr/local/fsl/bin"
# 设置 FreeSurfer 安装目录，便于调用fsl命令
os.environ["FREESURFER_HOME"] = "/usr/local/freesurfer"
os.environ["PATH"] += os.pathsep + os.path.join(os.environ["FREESURFER_HOME"], "bin")
os.environ["SUBJECTS_DIR"] = "/opt/data/private/lq/data/NSD/nsddata/freesurfer"

# 全局配置
base_dir = '/opt/data/private/lq/data/NSD/nsddata_rawdata'
preproc_dir = '/opt/data/private/lq/data/NSD/nsddata_preprocdata/'
designmatrix_dir = '/opt/data/private/lq/data/NSD/nsddata/experiments/nsd/' 
nsd_ref_beta_dir = '/opt/data/private/lq/data/NSD/nsddata_betas/ppdata'
TR_s = 1.6 # nsd: 1.6; our data: 1.5
tr_target = 1.0
datatype = 'nsd'

def find_sessions(nativesurf_inputdir):
    """
    自动搜索指定路径下的session文件
    只搜索左脑lh.betas_session--.hdf5文件，因为左右脑数量一致
    
    Args:
        nativesurf_inputdir: 输入目录路径
        
    Returns:
        list: session编号列表，如['01', '02', '03', ...]
    """
    print(f"Searching for sessions in: {nativesurf_inputdir}")
    
    # 检查目录是否存在
    if not exists(nativesurf_inputdir):
        print(f"Directory does not exist: {nativesurf_inputdir}")
        return []
    
    # 搜索左脑的session文件
    pattern = join(nativesurf_inputdir, "lh.betas_session*.hdf5")
    session_files = glob.glob(pattern)
    
    print(f"Found {len(session_files)} session files:")
    for file_path in session_files:
        print(f"  - {file_path}")
    
    # 提取session编号
    sessions = []
    for file_path in session_files:
        filename = os.path.basename(file_path)
        # 使用正则表达式提取session编号
        match = re.search(r'lh\.betas_session(\d+)\.hdf5', filename)
        if match:
            session_num = match.group(1)
            sessions.append(session_num)
            print(f"  Extracted session: {session_num}")
    
    # 排序session编号
    sessions.sort()
    print(f"Final sorted sessions: {sessions}")
    return sessions

def test_single_subject(subject_id):
    """
    测试单个被试的session搜索
    
    Args:
        subject_id: 被试ID，如'sub-01'
    """
    print(f"\n=== Testing subject: {subject_id} ===")
    
    # 转换被试ID格式
    freesurfer_sub_id_reconname = subject_id.replace("sub-", "subj") if datatype == 'nsd' else subject_id
    print(f"FreeSurfer subject name: {freesurfer_sub_id_reconname}")
    
    # 定义输入和输出目录
    nativesurf_inputdir = join(nsd_ref_beta_dir, freesurfer_sub_id_reconname, 
                              "nativesurface", "betas_fithrf_GLMdenoise_RR")
    fs5_outputdir = join(nsd_ref_beta_dir, freesurfer_sub_id_reconname, 
                        "fsaverage5", "betas_fithrf_GLMdenoise_RR")
    
    print(f"Input directory: {nativesurf_inputdir}")
    print(f"Output directory: {fs5_outputdir}")
    
    # 检查输入目录是否存在
    if not exists(nativesurf_inputdir):
        print(f"❌ Input directory does not exist!")
        return False
    else:
        print(f"✅ Input directory exists")
    
    # 搜索sessions
    sessions = find_sessions(nativesurf_inputdir)
    
    if sessions:
        print(f"✅ Found {len(sessions)} sessions: {sessions}")
        
        # 检查第一个session的文件
        first_session = sessions[0]
        lh_file = join(nativesurf_inputdir, f"lh.betas_session{first_session}.hdf5")
        rh_file = join(nativesurf_inputdir, f"rh.betas_session{first_session}.hdf5")
        
        print(f"\nChecking first session ({first_session}) files:")
        print(f"LH file: {lh_file} - {'✅ exists' if exists(lh_file) else '❌ missing'}")
        print(f"RH file: {rh_file} - {'✅ exists' if exists(rh_file) else '❌ missing'}")
        
        # 尝试读取HDF5文件信息
        if exists(lh_file):
            try:
                with h5py.File(lh_file, "r") as f:
                    print(f"HDF5 keys: {list(f.keys())}")
                    if "betas" in f:
                        betas_shape = f["betas"].shape
                        print(f"Betas shape: {betas_shape}")
                    else:
                        print("❌ 'betas' key not found in HDF5 file")
            except Exception as e:
                print(f"❌ Error reading HDF5 file: {e}")
        
        return True
    else:
        print(f"❌ No sessions found")
        return False

def test_all_subjects():
    """
    测试所有被试（sub-01到sub-08）的session搜索
    """
    print("=== Testing all subjects (sub-01 to sub-08) ===")
    
    # 定义被试列表：sub-01到sub-08
    freeSurfer_subID_list = [f'sub-{i:02d}' for i in range(1, 9)]
    
    results = {}
    total_sessions = 0
    
    for subject_id in freeSurfer_subID_list:
        success = test_single_subject(subject_id)
        
        if success:
            # 获取session数量
            freesurfer_sub_id_reconname = subject_id.replace("sub-", "subj") if datatype == 'nsd' else subject_id
            nativesurf_inputdir = join(nsd_ref_beta_dir, freesurfer_sub_id_reconname, 
                                      "nativesurface", "betas_fithrf_GLMdenoise_RR")
            sessions = find_sessions(nativesurf_inputdir)
            results[subject_id] = len(sessions)
            total_sessions += len(sessions)
        else:
            results[subject_id] = 0
    
    print(f"\n=== Summary ===")
    print(f"Subject\t\tSessions")
    print(f"-------\t\t--------")
    for subject_id, session_count in results.items():
        print(f"{subject_id}\t\t{session_count}")
    
    print(f"\nTotal sessions across all subjects: {total_sessions}")
    
    # 估算处理时间
    if total_sessions > 0:
        estimated_time_per_session = 5  # 假设每个session需要5分钟
        total_estimated_time = total_sessions * estimated_time_per_session
        print(f"Estimated total processing time: {total_estimated_time} minutes ({total_estimated_time/60:.1f} hours)")

def main():
    """
    主函数：运行测试
    """
    print("NSD Beta Projection Test Script")
    print("===============================")
    
    # 检查基本路径
    print(f"Base directory: {nsd_ref_beta_dir}")
    if exists(nsd_ref_beta_dir):
        print("✅ Base directory exists")
    else:
        print("❌ Base directory does not exist!")
        return
    
    # 测试单个被试（sub-01）
    print("\n" + "="*50)
    test_single_subject('sub-01')
    
    # 测试所有被试
    print("\n" + "="*50)
    test_all_subjects()

if __name__ == "__main__":
    main()
