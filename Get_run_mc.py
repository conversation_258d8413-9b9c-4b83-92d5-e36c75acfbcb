import numpy as np
import os
import matplotlib.pyplot as plt
from os.path import join
import pickle
import logging
import sys
from datetime import datetime

def bramila_framewise_displacement(motion_param_input, prepro_suite='fsl-fs', radius=50):
    """
    Computes the framewise displacement metric as described in Power et al. (2012, 2014)

    Parameters:
    -----------
    motion_param_input : str or numpy.ndarray
        Path to the motion parameter file OR numpy array of motion parameters (ntimes x 6 or more)
    prepro_suite : str, optional
        Preprocessing suite used ('fsl-fs' or 'spm'), default 'fsl-fs'
    radius : float, optional
        Radius of sphere in mm to convert degrees to motion, default 50

    Returns:
    --------
    fwd : numpy.ndarray
        Framewise displacement timeseries
    mean_fwd : float
        Mean framewise displacement
    rms : numpy.ndarray
        Root mean square for each column
    """

    print('Computing framewise displacement...', end='')

    # Load motion parameters - handle both file path and array input
    if isinstance(motion_param_input, str):
        # Input is a file path
        if not os.path.exists(motion_param_input):
            raise FileNotFoundError(f"Motion parameter file not found: {motion_param_input}")
        ts = np.loadtxt(motion_param_input)
    elif isinstance(motion_param_input, np.ndarray):
        # Input is already a numpy array
        ts = motion_param_input.copy()
    else:
        raise TypeError(f"motion_param_input must be either a file path (str) or numpy array, got {type(motion_param_input)}")
    
    # Remove first column and columns 7-9 if they exist (to match MATLAB behavior)
    if ts.shape[1] > 6:
        # Remove first column
        ts = ts[:, 1:]
        # Remove columns 7-9 if they exist (now columns 6-8 after removing first column)
        if ts.shape[1] > 6:
            ts = ts[:, :6]
    
    # Ensure we have exactly 6 motion parameters
    if ts.shape[1] != 6:
        raise ValueError(f'The motion time series must have 6 motion parameters in 6 columns; '
                        f'the size of the input given is {ts.shape}')
    
    # Convert to double precision
    ts = ts.astype(np.float64)
    
    # Convert rotations to mm based on preprocessing suite
    if prepro_suite == 'fsl-fs':
        # In FSL the first 3 columns are rotations
        # Convert radians to motion in mm
        temp = ts[:, :3].copy()
        temp = (radius * np.pi / 180) * temp
        ts[:, :3] = temp
    else:  # SPM way
        # Convert degrees to motion in mm
        temp = ts[:, 3:6].copy()
        temp = radius * temp
        ts[:, 3:6] = temp
    
    # Calculate differences between consecutive timepoints
    dts = np.diff(ts, axis=0)
    
    # Add zeros as first row (as per Power et al 2014)
    dts = np.vstack([np.zeros((1, dts.shape[1])), dts])
    
    # Calculate framewise displacement
    fwd = np.sum(np.abs(dts), axis=1)
    
    # Calculate root mean square for each column
    rms = np.sqrt(np.mean(ts**2, axis=0))
    
    # Calculate mean framewise displacement
    mean_fwd = np.mean(fwd)
    
    print(' done')
    
    return fwd, mean_fwd, rms

def saveObject(strName, variable):
    with open(strName, 'wb') as handle:
        pickle.dump(variable, handle, protocol=pickle.HIGHEST_PROTOCOL)

def openObject(strName):
    with open(strName, 'rb') as handle:
        objectOut = pickle.load(handle)
    return objectOut

# Set up logging
def setup_logging(log_dir):
    """Set up logging to both file and console"""
    os.makedirs(log_dir, exist_ok=True)

    # Create log filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = join(log_dir, f'run_motion_QC_{timestamp}.log')

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )

    return log_file

# Replace print with logging
def log_print(message):
    """Print and log message"""
    logging.info(message)

# SET INPUT PARAMETERS
subID_list = [
    'sub-01',                         
]

sesID_list = [
    'ses-01',                           
]

runID_list = [
    'run-04'
]
base_dir = '/cy/data/preprocdata/'

hrf_latency_nTR = 4 # 6s



def calculate_run_meanFD(run_mc_data, subID, output_dir):
    """for single subject: Calculate meanFD for each run, and Create motion plots for a session of this subject"""
    run_meanFD = {}
    run_FD_timeseries = {}
    
    
    log_print(f"Processing {len(runID_list)} stories for subject {subID}")

    # Create separate plots for each run's motion parameters
    for runID in range(len(runID_list)):
        mc_data = np.loadtxt(join(base_dir, subID, 'ses-01', runID, 'f_skip_stc_mc.nii.gz.par'))
        mc_data_currentrun = mc_data[hrf_latency_nTR:]

        try:
            # Use modified bramila_framewise_displacement with array input
            fwd, mean_fwd, _ = bramila_framewise_displacement(mc_data_currentrun, prepro_suite='fsl-fs')
            run_meanFD[runID] = mean_fwd
            run_FD_timeseries[runID] = fwd
            log_print(f"run {runID} meanFD: {mean_fwd:.6f}")

            # Create individual motion parameter plot for this run
            fig, axes = plt.subplots(2, 1, figsize=(12, 8))

            # Plot rotations
            motion_labels_rot = ['rot_x', 'rot_y', 'rot_z']
            for i, label in enumerate(motion_labels_rot):
                axes[0].plot(mc_data_currentrun[:, i], label=label, alpha=0.7)
            axes[0].set_title(f"run {runID} - Rotations (rad) - meanFD: {mean_fwd:.6f}")
            axes[0].set_xlabel('Time (TR)')
            axes[0].set_ylabel('Rotation (rad)')
            axes[0].legend()
            axes[0].grid(True, alpha=0.3)

            # Plot translations
            motion_labels_trans = ['trans_x', 'trans_y', 'trans_z']
            for i, label in enumerate(motion_labels_trans):
                axes[1].plot(mc_data_currentrun[:, i+3], label=label, alpha=0.7)
            axes[1].set_title(f"run {runID} - Translations (mm)")
            axes[1].set_xlabel('Time (TR)')
            axes[1].set_ylabel('Translation (mm)')
            axes[1].legend()
            axes[1].grid(True, alpha=0.3)

            plt.tight_layout()
            plt.savefig(join(output_dir, f'{subID}_motion_parameters_{run_name}_index{runID}.png'),
                       dpi=300, bbox_inches='tight')
            plt.close(fig)

        except Exception as e:
            log_print(f"Error processing run {runID}: {e}")
            run_meanFD[runID] = np.nan
            run_FD_timeseries[runID] = None

    # Create combined FD timeseries plot for all stories
    fig, ax = plt.subplots(figsize=(15, 8))

    for runID in range(len(runID_list)):
        if runID in run_FD_timeseries and run_FD_timeseries[runID] is not None:
            ax.plot(run_FD_timeseries[runID],
                   label=f'run {runID}', alpha=0.7)

    ax.axhline(y=0.2, color='red', linestyle='--', label='FD=0.2 threshold', linewidth=2)
    ax.set_xlabel('Time (TR)')
    ax.set_ylabel('Framewise Displacement (mm)')
    ax.set_title(f'{subID} - Framewise Displacement Across All Stories')
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(join(output_dir, f'{subID}_FD_all_stories.png'),
               dpi=300, bbox_inches='tight')
    plt.close(fig)

    return run_meanFD, run_FD_timeseries

def perform_qc_analysis(meanFD_matrix, subID_name):

    log_print("\n" + "="*50)
    log_print("PERFORMING QC ANALYSIS")
    log_print("="*50)

    nsubjs, nstories = meanFD_matrix.shape

    # [1] meanFD > 0.2 的 run_index
    high_motion_stories = []
    for subj_idx in range(nsubjs):
        for runID in range(nstories):
            fd_val = meanFD_matrix[subj_idx, runID]
            if not np.isnan(fd_val) and fd_val > 0.2:
                high_motion_stories.append({
                    'subject': subID_name[subj_idx],
                    'subject_idx': subj_idx,
                    'runID': runID,
                    'meanFD': fd_val
                })

    log_print(f"\n[1] Stories with meanFD > 0.2: {len(high_motion_stories)}")
    for item in high_motion_stories:
        log_print(f"  {item['subject']} - run index {item['runID']}: {item['meanFD']:.6f}")

    # [2] 组水平分析：所有人的所有run放在一起
    all_meanFDs = meanFD_matrix[~np.isnan(meanFD_matrix)]
    if len(all_meanFDs) > 0:
        grp_mean = np.mean(all_meanFDs)
        grp_std = np.std(all_meanFDs)
        grp_threshold_all = grp_mean + 2 * grp_std

        log_print(f"\n[2] Group-level analysis (all stories together):")
        log_print(f"  Group mean: {grp_mean:.6f}")
        log_print(f"  Group std: {grp_std:.6f}")
        log_print(f"  Group threshold (mean + 2*std): {grp_threshold_all:.6f}")

        high_motion_grp_all = []
        for subj_idx in range(nsubjs):
            for runID in range(nstories):
                fd_val = meanFD_matrix[subj_idx, runID]
                if not np.isnan(fd_val) and fd_val > grp_threshold_all:
                    high_motion_grp_all.append({
                        'subject': subID_name[subj_idx],
                        'subject_idx': subj_idx,
                        'runID': runID,
                        'meanFD': fd_val
                    })

        log_print(f"  Stories with meanFD > group threshold: {len(high_motion_grp_all)}")
        for item in high_motion_grp_all:
            log_print(f"    {item['subject']} - run index {item['runID']}: {item['meanFD']:.6f}")
    else:
        grp_threshold_all = np.nan
        high_motion_grp_all = []
        log_print("\n[2] No valid meanFD values found for group analysis!")

    # [3] 被试水平分析：每个人的所有run meanFD平均后
    subject_meanFDs = np.nanmean(meanFD_matrix, axis=1)
    valid_subj_meanFDs = subject_meanFDs[~np.isnan(subject_meanFDs)]

    if len(valid_subj_meanFDs) > 0:
        subj_grp_mean = np.mean(valid_subj_meanFDs)
        subj_grp_std = np.std(valid_subj_meanFDs)
        subj_grp_threshold = subj_grp_mean + 2 * subj_grp_std

        log_print(f"\n[3] Subject-level analysis (averaged across stories):")
        log_print(f"  Subject group mean: {subj_grp_mean:.6f}")
        log_print(f"  Subject group std: {subj_grp_std:.6f}")
        log_print(f"  Subject group threshold (mean + 2*std): {subj_grp_threshold:.6f}")

        high_motion_subjects = []
        for subj_idx in range(nsubjs):
            subj_meanFD = subject_meanFDs[subj_idx]
            if not np.isnan(subj_meanFD) and subj_meanFD > subj_grp_threshold:
                high_motion_subjects.append({
                    'subject': subID_name[subj_idx],
                    'subject_idx': subj_idx,
                    'meanFD': subj_meanFD
                })

        log_print(f"  Subjects with meanFD > subject group threshold: {len(high_motion_subjects)}")
        for item in high_motion_subjects:
            log_print(f"    {item['subject']}: {item['meanFD']:.6f}")
    else:
        subj_grp_threshold = np.nan
        high_motion_subjects = []
        log_print("\n[3] No valid subject meanFD values found!")

    return {
        'high_motion_stories': high_motion_stories,
        'high_motion_grp_all': high_motion_grp_all,
        'high_motion_subjects': high_motion_subjects,
        'grp_threshold_all': grp_threshold_all,
        'subj_grp_threshold': subj_grp_threshold,
        'meanFD_matrix': meanFD_matrix,
        'subject_meanFDs': subject_meanFDs
    }

def save_qc_results(qc_results, output_dir):
    """Save QC analysis results"""
    os.makedirs(output_dir, exist_ok=True)

    # Save as pickle file
    saveObject(join(output_dir, 'run_motion_qc_results.pkl'), qc_results)
    log_print(f"\nQC results saved to: {output_dir}")

def main():
    """Main analysis function"""
    # Set up logging first
    log_dir = join(base_dir, '..', 'Results', 'mc_QC', 'logs')
    log_file = setup_logging(log_dir)

    log_print("Starting run motion QC analysis...")
    log_print(f"Log file: {log_file}")

    # Initialize data structures
    nsubjs = len(subID_name)
    nstories = 15
    # Matrix to store meanFD: nsubjs x nstories
    meanFD_matrix = np.full((nsubjs, nstories), np.nan)

    # Process each subject
    for ii, (freesurfer_sub_id, subname, subID) in enumerate(zip(freeSurfer_subID_list, subID_list, subID_name)):


        log_print(f"\nProcessing subject {ii+1}/{nsubjs}: {subID}")

        # Load motion data for all stories
        run_mc_data = load_run_motion_data(freesurfer_sub_id, subname)

        # Create output directory for this subject
        subject_output_dir = join(base_dir, 'results', 'mc_QC', subID)
        os.makedirs(subject_output_dir, exist_ok=True)

        # Calculate meanFD for each run
        run_meanFD, _ = calculate_run_meanFD(run_mc_data, run_order, event_marks, subID, subject_output_dir)

        # Store in matrix
        for runID in range(nstories):
            if runID in run_meanFD:
                meanFD_matrix[ii, runID] = run_meanFD[runID]
        log_print(f"Plots saved to: {subject_output_dir}")

    # Perform QC analysis
    qc_results = perform_qc_analysis(meanFD_matrix, subID_name)

    # Save QC results
    qc_output_dir = join(base_dir, '..', 'Results', 'mc_QC')
    save_qc_results(qc_results, qc_output_dir)

    log_print("\n" + "="*50)
    log_print("ANALYSIS COMPLETE")
    log_print("="*50)

if __name__ == "__main__":
    main()

