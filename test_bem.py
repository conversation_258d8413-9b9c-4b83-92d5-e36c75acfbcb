#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
完整的 BEM 表面创建和处理流程
替代 FreeSurfer 的 mri_make_bem_surfaces 命令

作者: MNE-Python 用户
日期: 2024
"""

import os
import mne
import numpy as np
from pathlib import Path

def create_bem_workflow(subject, subjects_dir, overwrite=False):
    """
    完整的 BEM 工作流程
    
    Parameters
    ----------
    subject : str
        被试名称
    subjects_dir : str
        FreeSurfer subjects 目录路径
    overwrite : bool
        是否覆盖已存在的文件
    """
    # 添加 FSL bin 路径到环境变量，便于调用fsl命令
    os.environ["FSLDIR"] = "/usr/local/fsl"
    os.environ["PATH"] += os.pathsep + "/usr/local/fsl/bin"
    # 设置 FreeSurfer 安装目录，便于调用fsl命令
    os.environ["FREESURFER_HOME"] = "/usr/local/freesurfer"
    os.environ["PATH"] += os.pathsep + os.path.join(os.environ["FREESURFER_HOME"], "bin")

    print(f"开始为被试 {subject} 创建 BEM 模型...")
    print(f"FreeSurfer subjects 目录: {subjects_dir}")
    
    # ===== 步骤 0: 检查前提条件 =====
    print("\n=== 步骤 0: 检查前提条件 ===")
    
    subject_dir = Path(subjects_dir) / subject
    t1_file = subject_dir / 'mri' / 'T1.mgz'
    brain_file = subject_dir / 'mri' / 'brain.mgz'

    brainmask_file = subject_dir / 'mri' / 'brainmask.mgz'
    brainmask_binary_file1 = subject_dir / 'mri' / 'brainmask_binary1.nii.gz'
    brainmask_binary_file = subject_dir /  'mri' / 'brainmask_binary.nii.gz'

    if not subject_dir.exists():
        raise FileNotFoundError(f"被试目录不存在: {subject_dir}")
    
    if not t1_file.exists():
        raise FileNotFoundError(f"T1.mgz 文件不存在: {t1_file}")
        
    if not brain_file.exists():
        raise FileNotFoundError(f"brain.mgz 文件不存在: {brain_file}")
    
    print(f"✓ 被试目录存在: {subject_dir}")
    print(f"✓ T1.mgz 文件存在: {t1_file}")
    print(f"✓ brain.mgz 文件存在: {brain_file}")

    # ===== 创建 inner brainmask_binary for make_watershed_bem  =====
    cmd = f"mri_binarize --i {brainmask_file} \
                    --o {brainmask_binary_file1} \
                    --min 0.1"
    os.system(cmd)
    
    cmd = f"fslmaths {brainmask_binary_file1} -ero {brainmask_binary_file}"
    os.system(cmd)
    

    # ===== 步骤 1: 创建 BEM 表面 =====
    print("\n=== 步骤 1: 创建 BEM 表面 ===")
    
    try:
        bem_dir = subject_dir / 'bem'
        inner_skull_file = bem_dir / 'inner_skull.surf'
        outer_skull_file = bem_dir / 'outer_skull.surf'
        outer_skin_file = bem_dir / 'outer_skin.surf'
        
        # 检查是否已存在 BEM 表面文件
        if (inner_skull_file.exists() and outer_skull_file.exists() 
            and outer_skin_file.exists() and not overwrite):
            print("BEM 表面文件已存在，跳过创建步骤")
        else:
            print("正在创建 BEM 表面（使用 watershed 算法）...")
            print("这可能需要几分钟时间...")
            
            make_watershed_bem(
                subject=subject,
                subjects_dir=subjects_dir,
                overwrite=overwrite,
                volume='T1',  # 使用 T1 图像
                T1=True,
                gcaatlas=True,
                brainmask="../../mri/brainmask.mgz",
                show=False    # 不显示图形界面
            )
            
            print("✓ BEM 表面创建完成")
            
    except Exception as e:
        print(f"✗ BEM 表面创建失败: {e}")
        raise
    
    # ===== 步骤 2: 创建 BEM 模型 =====
    print("\n=== 步骤 2: 创建 BEM 模型 ===")
    
    try:
        print("正在创建三层 BEM 模型...")
        print("导电率设置: 脑组织=0.3 S/m, 颅骨=0.006 S/m, 头皮=0.3 S/m")
        
        bem_model = make_bem_model(
            subject=subject,
            subjects_dir=subjects_dir,
            ico=4,  # 细分级别 (2-6, 4是常用值)
            conductivity=[0.3, 0.006, 0.3]  # 脑、颅骨、头皮
        )
        
        print(f"✓ BEM 模型创建完成")
        print(f"  - 表面数量: {len(bem_model)}")
        print(f"  - 细分级别: ico=4")
        
        # 显示每个表面的信息
        for i, surface in enumerate(bem_model):
            print(f"  - 表面 {i+1}: {surface['ntri']} 个三角形, "
                  f"{surface['np']} 个顶点")
            
    except Exception as e:
        print(f"✗ BEM 模型创建失败: {e}")
        raise
    
    # ===== 步骤 3: 创建 BEM 解决方案 =====
    print("\n=== 步骤 3: 创建 BEM 解决方案 ===")
    
    try:
        print("正在计算 BEM 解决方案...")
        print("这是计算密集的过程，可能需要几分钟到几十分钟...")
        
        bem_solution = make_bem_solution(bem_model)
        
        print("✓ BEM 解决方案计算完成")
        
    except Exception as e:
        print(f"✗ BEM 解决方案计算失败: {e}")
        raise
    
    # ===== 步骤 4: 保存结果 =====
    print("\n=== 步骤 4: 保存结果 ===")
    
    try:
        # 保存 BEM 解决方案
        bem_solution_file = subject_dir / 'bem' / f'{subject}-bem-sol.fif'
        mne.write_bem_solution(str(bem_solution_file), bem_solution)
        print(f"✓ BEM 解决方案已保存: {bem_solution_file}")
        
        # 保存 BEM 模型（可选）
        bem_model_file = subject_dir / 'bem' / f'{subject}-bem.fif'
        mne.write_bem_surfaces(str(bem_model_file), bem_model)
        print(f"✓ BEM 模型已保存: {bem_model_file}")
        
    except Exception as e:
        print(f"✗ 保存结果失败: {e}")
        raise
    
    # ===== 步骤 5: 验证结果 =====
    print("\n=== 步骤 5: 验证结果 ===")
    
    try:
        # 重新加载验证
        loaded_bem_solution = mne.read_bem_solution(str(bem_solution_file))
        loaded_bem_model = mne.read_bem_surfaces(str(bem_model_file))
        
        print("✓ 文件验证成功")
        print(f"  - BEM 解决方案: {len(loaded_bem_solution)} 个表面")
        print(f"  - BEM 模型: {len(loaded_bem_model)} 个表面")
        
        # 可视化检查（可选）
        if mne.viz.get_3d_backend() is not None:
            try:
                print("创建可视化检查...")
                fig = mne.viz.plot_bem(
                    subject=subject,
                    subjects_dir=subjects_dir,
                    brain_surfaces='white',
                    show=False
                )
                print("✓ 可视化检查完成")
            except Exception as viz_e:
                print(f"可视化检查失败（非关键错误）: {viz_e}")
        
    except Exception as e:
        print(f"✗ 验证失败: {e}")
        raise
    
    print(f"\n🎉 {subject} 的 BEM 工作流程成功完成！")
    print(f"生成的文件:")
    print(f"  - BEM 表面: {bem_dir}/{{inner_skull,outer_skull,outer_skin}}.surf")
    print(f"  - BEM 解决方案: {bem_solution_file}")
    print(f"  - BEM 模型: {bem_model_file}")
    
    return bem_solution, bem_model


def batch_process_subjects(subjects_list, subjects_dir, overwrite=False):
    """
    批量处理多个被试
    
    Parameters
    ----------
    subjects_list : list
        被试名称列表
    subjects_dir : str
        FreeSurfer subjects 目录路径
    overwrite : bool
        是否覆盖已存在的文件
    """
    
    print(f"开始批量处理 {len(subjects_list)} 个被试...")
    
    results = {}
    failed_subjects = []
    
    for i, subject in enumerate(subjects_list, 1):
        print(f"\n{'='*60}")
        print(f"处理被试 {i}/{len(subjects_list)}: {subject}")
        print(f"{'='*60}")
        
        try:
            bem_solution, bem_model = create_bem_workflow(
                subject, subjects_dir, overwrite
            )
            results[subject] = {
                'bem_solution': bem_solution,
                'bem_model': bem_model,
                'status': 'success'
            }
            print(f"✓ {subject} 处理成功")
            
        except Exception as e:
            failed_subjects.append(subject)
            results[subject] = {
                'error': str(e),
                'status': 'failed'
            }
            print(f"✗ {subject} 处理失败: {e}")
            continue
    
    # 总结报告
    print(f"\n{'='*60}")
    print("批量处理完成报告")
    print(f"{'='*60}")
    
    successful = [s for s in subjects_list if s not in failed_subjects]
    print(f"成功处理: {len(successful)}/{len(subjects_list)} 个被试")
    
    if successful:
        print("成功的被试:")
        for subject in successful:
            print(f"  ✓ {subject}")
    
    if failed_subjects:
        print(f"\n失败的被试: {len(failed_subjects)} 个")
        for subject in failed_subjects:
            print(f"  ✗ {subject}: {results[subject]['error']}")
    
    return results


# ===== 使用示例 =====
if __name__ == "__main__":

    import os
    os.environ["FREESURFER_HOME"] = "/usr/local/freesurfer"
    os.environ["PATH"] += os.pathsep + os.path.join(os.environ["FREESURFER_HOME"], "bin")

    from mne.bem import make_watershed_bem, make_bem_model, make_bem_solution

    # 设置参数
    subjects_dir = "/usr/local/freesurfer/subjects"  # 替换为实际路径
    
    # 单个被试处理
    subject = "sub02"
    
    # 检查环境变量
    if 'SUBJECTS_DIR' in os.environ:
        subjects_dir = os.environ['SUBJECTS_DIR']
        print(f"使用环境变量 SUBJECTS_DIR: {subjects_dir}")
    
    try:
        # 运行完整流程
        bem_solution, bem_model = create_bem_workflow(
            subject=subject,
            subjects_dir=subjects_dir,
            overwrite=True  # 设置为 True 以覆盖现有文件
        )
        
        print("\n后续使用示例:")
        print("# 加载已保存的 BEM 解决方案")
        print("bem_solution = mne.read_bem_solution('subject_001-bem-sol.fif')")
        print("\n# 用于正向建模")
        print("fwd = mne.make_forward_solution(raw.info, trans, src, bem_solution)")
        
    except Exception as e:
        print(f"流程执行失败: {e}")
    
    # 批量处理示例（取消注释使用）
    # subjects_list = ["subject_001", "subject_002", "subject_003"]
    # results = batch_process_subjects(subjects_list, subjects_dir, overwrite=False)