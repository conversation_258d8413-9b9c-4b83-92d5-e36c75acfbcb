import os
import datetime
import random
import subprocess
import argparse
# import sys
import numpy as np
import nibabel as nib
from nilearn import image
from matplotlib import pyplot as plt

from functions import *
from nilearn.signal import standardize_signal # python 3.8 is _standardize, later version change to 
# 添加 FSL bin 路径到环境变量，便于调用fsl命令
os.environ["FSLDIR"] = "/usr/local/fsl"
os.environ["PATH"] += os.pathsep + "/usr/local/fsl/bin"
# 设置 FreeSurfer 安装目录，便于调用fsl命令
os.environ["FREESURFER_HOME"] = "/usr/local/freesurfer"
os.environ["PATH"] += os.pathsep + os.path.join(os.environ["FREESURFER_HOME"], "bin")
# 设置正确的 SUBJECTS_DIR（FreeSurfer 查找重建数据的位置）
datatype = 'nsd' # 'our'  
if datatype == 'nsd':
    os.environ["SUBJECTS_DIR"] = "/opt/data/private/lq/data/NSD/nsddata/freesurfer"

###-----------------------------------------
### T1.nii.gz is RAS, f.nii.gz-f_skip_stc_mc.nii.gz is LAS.
# after epi_reg, f_skip_stc_mc_fm.nii.gz is RAS, registered to T1.
###-----------------------------------------

# Create log file
def create_log_file(freesurfer_sub_id):
    func_dir = os.path.dirname(os.path.realpath(__file__))
    func_filename = os.path.basename(__file__).split('.')[0]

    log_dir = os.path.join(func_dir, 'log')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    current_time = datetime.datetime.now()
    log_file_name = '{}.{}.{}.{}.log'.format(func_filename, freesurfer_sub_id, current_time.strftime('%Y%m%d.%H%M%S'), random.randint(0, 99999))
    log_file = os.path.join(log_dir, log_file_name)

    while os.path.exists(log_file):
        log_file_name = '{}.{}.{}.{}.log'.format(func_filename,freesurfer_sub_id, current_time.strftime('%Y%m%d.%H%M%S'), random.randint(0, 99999))
        log_file = os.path.join(log_dir, log_file_name)

    log_fid = open(log_file, 'w')
    log_fid.write(func_filename + '\n')
    current_time = datetime.datetime.now()
    log_fid.write('Begin:\t' + current_time.strftime('%Y%m%d:%H:%M:%S') + '.\n')    
    
    return log_fid

def run_command(cmd, log_fid):
    try:
        subprocess.run(cmd, check=True, shell=True)
        log_fid.write(f"Command executed: {''.join(cmd)}\n")
    except subprocess.CalledProcessError as e:
        log_fid.write(f"Error executing command: {''.join(cmd)}\nError message: {e}\n")

#%%     
freesurfer_recon_dir = os.environ.get('SUBJECTS_DIR')

if datatype == 'nsd':
    base_dir = '/opt/data/private/lq/data/NSD/nsddata_rawdata'
    preproc_dir = '/opt/data/private/lq/data/NSD/nsddata_preprocdata/'

    TR_s = 1.6 # nsd: 1.6; our data: 1.5
    Effective_echo_spacing = 0.000329994 # nsd: 0.000329994; our data: 0.00027500
    origvoxelSize = np.array([1.8, 1.8, 1.8])
elif datatype == 'our':
    base_dir = '/opt/data/private/lq/data/NSD/nsddata_rawdata'
    preproc_dir = '/opt/data/private/lq/data/NSD/nsddata_preprocdata/'

    TR_s = 1.5
    Effective_echo_spacing = 0.00027500
    origvoxelSize = np.array([2.0, 2.0, 2.0])

tcustom_dir = os.path.join(base_dir, 'tcustom_epi.txt')

surf_FWHM = 6
vol_FWHM = 6 #6mm FWHM
#%% subj Info

# params input example: 
# python fmri_preprocessing_wrx_v2_taskinput.py --freeSurfer_subID_list yaoruwei20231012 --sesID_list yaoruwei20231012 --func_run_name_list task1 task2 taskLanLoc'
# python fmri_preprocessing_wrx_v2_taskinput.py --freeSurfer_subID_list yaoruwei20231012 --sesID_list yaoruwei20231012 --func_run_name_list task1 task2 task3
# notuse: python fmri_preprocessing_wrx.py --freeSurfer_subID_list 'yaoruwei20231012' --sesID_list 'yaoruwei20231012' --variable 'task1'

# parser = argparse.ArgumentParser()
# parser.add_argument('--freeSurfer_subID_list', nargs='+', help='freeSurfer_subID_list', required=True)
# parser.add_argument('--sesID_list', nargs='+', help='sesID_list', required=True)
# parser.add_argument('--func_run_name_list', nargs='+', help='funcrun_foldername', required=True)
# parser.add_argument('--fmap_run_name_list', nargs='+', help='fmaprun_foldername', required=True)
# args = parser.parse_args()
# freeSurfer_subID_list = args.freeSurfer_subID_list
# sesID_list = args.sesID_list
# func_run_name_list = args.func_run_name_list
# fmap_run_name_list = args.fmap_run_name_list

# test parser.input
# print('freeSurfer_subID_list: ', freeSurfer_subID_list)
# print('sesID_list: ', sesID_list)
# print('func_run_name_list: ', func_run_name_list)
# print('fmap_run_name_list: ', fmap_run_name_list)
# print(type(run_name_list))

## initiate data id number
freeSurfer_subID_list = ['sub-01']
sesID_list = ['ses-nsd01']
# func_run_name_list = ['run-01', 'run-02', 'run-03']  
# fmap_run_name_list = ['run-01', 'run-01', 'run-01']
func_run_name_list = ['run-04', 'run-05', 'run-06', 'run-07', 'run-08', 'run-09', 'run-10', 'run-11', 'run-12']  
fmap_run_name_list = ['run-02', 'run-02', 'run-02', 'run-03', 'run-03', 'run-03', 'run-04', 'run-04', 'run-04']

for i in range(len(freeSurfer_subID_list)):
    freesurfer_sub_id = freeSurfer_subID_list[i]
    freesurfer_sub_id_reconname = freesurfer_sub_id.replace("sub-", "subj") if datatype == 'nsd' else freesurfer_sub_id.copy()
    ses_id = sesID_list[i]
    
    print('freeSurfer_subID: ', freesurfer_sub_id)
    print('sesID: ', ses_id, '\n')
    
    #%% create log file at Code/log/
    log_fid = create_log_file(freesurfer_sub_id)
    
    #%% set up input folders
    anat_input_dir = os.path.join(base_dir, freesurfer_sub_id, 'anat')
    field_input_dir = os.path.join(base_dir,  freesurfer_sub_id, ses_id, 'fmap')
    func_input_dir = os.path.join(base_dir, freesurfer_sub_id, ses_id, 'func')

    ## set up output folders
    anat_output_dir = os.path.join(anat_input_dir, 'anat_output')
    # same reg_output_dir for each session (with multiple tasks) to the recon freesurfer_sub_id
    reg_output_dir = os.path.join(preproc_dir, freesurfer_sub_id, 'reg_output')    
    subspec_ROI_path = os.path.join(preproc_dir, freesurfer_sub_id, 'ROIs')
    
    ## check and create folders
    folders = [anat_input_dir, 
               anat_output_dir,
               reg_output_dir,
               subspec_ROI_path
               ]
    for i_folder in folders:
        path_tmp = os.path.join(os.getcwd(), i_folder)
        if not os.path.exists(path_tmp):
            os.makedirs(path_tmp, exist_ok=True)
            
    ## set up input filenames
    anat_T1_dir = os.path.join(anat_input_dir, 'T1.nii.gz')
    theAparcAseg_dir = os.path.join(freesurfer_recon_dir, freesurfer_sub_id_reconname, 'mri', 'aparc+aseg.mgz')
       
    #%% set up output filenames across all tasks
    anat_brain_dir = os.path.join(anat_output_dir,  'T1_brain.nii.gz')
    anat_pve0_dir = os.path.join(anat_output_dir, 'T1_brain_pve_0.nii.gz')
    anat_pve1_dir = os.path.join(anat_output_dir,  'T1_brain_pve_1.nii.gz')
    anat_pve2_dir = os.path.join(anat_output_dir,  'T1_brain_pve_2.nii.gz')
    
    ## anat_mask_wm_dir is binarized from anat_pve2_dir
    anat_wmseg_dir = os.path.join(anat_output_dir,  'T1_wmseg.nii.gz')
    # anat_mask_wm_dir is in the same resolution with anat_wmseg_dir, but calculated in another way
    anat_mask_wm_dir = os.path.join(anat_output_dir,  'T1_wm_mask.nii.gz') 
    anat_mask_vcsf_dir = os.path.join(anat_output_dir,  'T1_vcsf_mask.nii.gz')
    anat_mask_global_dir = os.path.join(anat_output_dir,  'T1_global_mask.nii.gz')
    recon_anat_brain_dir = os.path.join(freesurfer_recon_dir, freesurfer_sub_id_reconname, 'mri', 'brainmask.mgz')
    registered_anat_brain_dir = os.path.join(anat_output_dir,  'T1_brain.registered.nii.gz') ## output in RAS
    
    ## mask_wm_dir_func is downsampled according to fmri img from anat_wmseg_dir
    mask_vcsf_dir_func = os.path.join(subspec_ROI_path, 'vcsf_mask_func.nii.gz')
    mask_wm_dir_func = os.path.join(subspec_ROI_path, 'wm_mask_func.nii.gz')
    mask_global_dir_func = os.path.join(subspec_ROI_path, 'global_mask_func.nii.gz')
    mask_hippo_dir_func = os.path.join(subspec_ROI_path, 'hippo_mask.nii.gz')
    register_dir = os.path.join(reg_output_dir, 'register.dof6.dat')
    init_register_dir = os.path.join(reg_output_dir, 'init.register.dof6.dat')
    register_dir_downsampledfunc = os.path.join(reg_output_dir, 'register.downsampledfunc.dof6.dat')
    init_register_dir_downsampledfunc = os.path.join(reg_output_dir, 'init.register.downsampledfunc.dof6.dat')
    
    # surf
    mask_surf_lh_dir_func = os.path.join(subspec_ROI_path, 'brain_mask.self.lh.nii.gz')
    mask_surf_rh_dir_func = os.path.join(subspec_ROI_path, 'brain_mask.self.rh.nii.gz')
    
    #%% run preprocessing for all sessions of the subj
    for run_tmp_id in range(len(func_run_name_list)):
        run_id_func = func_run_name_list[run_tmp_id]
        run_id_fmap = fmap_run_name_list[run_tmp_id]
        
        ## set input func raw_data and corresponding fmap data
        filetopreproc_dir = os.path.join(func_input_dir, f"{freesurfer_sub_id}_{ses_id}_task-nsdcore_{run_id_func}_bold.nii.gz")
        
        field_mag_dir = os.path.join(field_input_dir, f"{freesurfer_sub_id}_{ses_id}_{run_id_fmap}_magnitude1.nii.gz")
        field_brain_dir = os.path.join(field_input_dir, 'field_output',  f'{run_id_fmap}_fmap_mag_brain.nii.gz')
        field_fmap_dir = os.path.join(field_input_dir, 'field_output',  f'{run_id_fmap}_fmap_rads.nii.gz')  
        log_fid.write('\n Preprocessing ' + filetopreproc_dir + '\n')

        # -------------------------------------------------------------------------------------
        ## set up preproc_func data output folder and change to this folder
        run_func_output_dir_name = os.path.join(preproc_dir, freesurfer_sub_id, ses_id, run_id_func)
        os.makedirs(run_func_output_dir_name, exist_ok=True)

        os.chdir(run_func_output_dir_name)
        log_fid.write('\nChange to ' + run_func_output_dir_name + '.\n')

        # make output subfolders   
        path_glm_wm = os.path.join(run_func_output_dir_name,'glm_wm')     
        path_glm_vcsf = os.path.join(run_func_output_dir_name,'glm_vcsf')
        func_output_dir_simptask = os.path.join(run_func_output_dir_name,'output_dir_simptask')
        func_output_dir_clean = os.path.join(run_func_output_dir_name,'output_dir_clean')
        surf_output_dir_simptask = os.path.join(run_func_output_dir_name,'surf_output_dir_simptask')
        surf_output_dir_clean = os.path.join(run_func_output_dir_name,'surf_output_dir_clean')

        ## check and create folders
        folders = [func_output_dir_simptask, 
                func_output_dir_clean, 
                surf_output_dir_simptask,
                surf_output_dir_clean
                ]
        for i in folders:
            path_tmp = os.path.join(os.getcwd(), i)
            if not os.path.exists(path_tmp):
                os.makedirs(path_tmp, exist_ok=True)
        ## set up output filenames
        ## volume
        mc_data_dir = os.path.join(run_func_output_dir_name, 'f_skip_stc_mc.nii.gz.par')
        func_fm_dir = os.path.join(run_func_output_dir_name, 'f_skip_stc_mc_fm.nii.gz')
        func_stc_mc_fm_1vol_dir = os.path.join(run_func_output_dir_name, 'f_skip_stc_mc_fm_1vol.nii.gz')
        func_fm_downsample_dir = os.path.join(run_func_output_dir_name, 'f_downsampled.nii.gz')
        func_simptask_dir = os.path.join(func_output_dir_simptask, 'f_simptask.nii.gz')
        func_clean_dir = os.path.join(func_output_dir_clean, 'f_simptask_clean.nii.gz')

        func_simptask_sm_dir = os.path.join(func_output_dir_simptask, 'f_simptask_sm.nii.gz')
        func_clean_sm_dir = os.path.join(func_output_dir_clean, 'f_simptask_clean_sm.nii.gz')

        ## self surf
        surf_simptask_lh_dir = os.path.join(surf_output_dir_simptask, 'lh.func.simptask.nii.gz')
        surf_simptask_rh_dir = os.path.join(surf_output_dir_simptask, 'rh.func.simptask.nii.gz')
        surf_clean_lh_dir = os.path.join(surf_output_dir_clean, 'lh.func.clean.nii.gz')
        surf_clean_rh_dir = os.path.join(surf_output_dir_clean, 'rh.func.clean.nii.gz')
        
        # surf to fs5, and then smooth surf_FWHM              
        fs5_simptask_lh_dir = os.path.join(surf_output_dir_simptask, 'lh.func.simptask.fs5.nii.gz')
        fs5_simptask_rh_dir = os.path.join(surf_output_dir_simptask, 'rh.func.simptask.fs5.nii.gz')
        fs5_clean_lh_dir = os.path.join(surf_output_dir_clean, 'lh.func.clean.fs5.nii.gz')
        fs5_clean_rh_dir = os.path.join(surf_output_dir_clean, 'rh.func.clean.fs5.nii.gz')
        
        fs5_simptask_lh_sm_dir = os.path.join(surf_output_dir_simptask, 'lh.func.simptask.fs5.sm.nii.gz')
        fs5_simptask_rh_sm_dir = os.path.join(surf_output_dir_simptask, 'rh.func.simptask.fs5.sm.nii.gz')
        fs5_clean_lh_sm_dir = os.path.join(surf_output_dir_clean, 'lh.func.clean.fs5.sm.nii.gz')
        fs5_clean_rh_sm_dir = os.path.join(surf_output_dir_clean, 'rh.func.clean.fs5.sm.nii.gz')


        global_meanval_dir = os.path.join(run_func_output_dir_name, 'global.meanval.dat')
        global_waveform_dir = os.path.join(run_func_output_dir_name, 'global.waveform.dat')

        wm_meanval_dir = os.path.join(path_glm_wm, 'wm.meanval.dat')
        wm_waveform_dir = os.path.join(path_glm_wm, 'wm.waveform.dat')
        wm_pcs_dir_orig = os.path.join(path_glm_wm, 'pca-eres', 'u.mtx')
        wm_pcs_dir = os.path.join(path_glm_wm, 'wm.dat')                
        
        vcsf_meanval_dir = os.path.join(path_glm_vcsf, 'vcsf.meanval.dat')
        vcsf_waveform_dir = os.path.join(path_glm_vcsf, 'vcsf.waveform.dat')
        vcsf_pcs_dir_orig = os.path.join(path_glm_vcsf, 'pca-eres', 'u.mtx')
        vcsf_pcs_dir = os.path.join(path_glm_vcsf, 'vcsf.dat')

        confound_ev_dir = os.path.join(run_func_output_dir_name, 'confound_EVs.txt')
        confound_ev_zscore_dir = os.path.join(run_func_output_dir_name, 'confound_EVs_zscore.txt')
        confound_ev_avgwf_dir = os.path.join(run_func_output_dir_name, 'confound_EVs_avgwf.txt')
        confound_ev_avgwf_zscore_dir = os.path.join(run_func_output_dir_name, 'confound_EVs_avgwf_zscore.txt')

        # -----------------------------------------------------------------------------------------
        if not os.path.exists('f_skip_stc_mc.nii.gz_mean_reg.nii.gz'):                                                

            cmd = f"slicetimer -i {filetopreproc_dir} -o f_skip_stc.nii.gz -r {TR_s} --tcustom={tcustom_dir}"
            run_command(cmd, log_fid)
            
            # register timeseries to mean volume
            cmd = "mcflirt -in f_skip_stc.nii.gz -out f_skip_stc_mc.nii.gz -cost normmi -meanvol -plots"
            run_command(cmd, log_fid)
            
        if not os.path.exists(func_fm_dir):    
            # using the negative y-direction: pedir=-y
            if os.path.exists(field_fmap_dir) and not os.path.exists('f_skip_stc_mc_fm_warp.nii.gz'):
                cmd = f"epi_reg --echospacing={Effective_echo_spacing} --wmseg={anat_wmseg_dir} \
                        --fmap={field_fmap_dir} \
                        --fmapmag={field_mag_dir} \
                        --fmapmagbrain={field_brain_dir} \
                        --pedir=-y \
                        --epi=f_skip_stc_mc.nii.gz_mean_reg.nii.gz \
                        --t1={anat_T1_dir} \
                        --t1brain={anat_brain_dir} \
                        --out=f_skip_stc_mc_fm"
                run_command(cmd, log_fid)
                
            if os.path.exists('f_skip_stc_mc_fm_warp.nii.gz'):    
                cmd = f"applywarp -i f_skip_stc_mc.nii.gz \
                        -r {anat_T1_dir} \
                        -w f_skip_stc_mc_fm_warp.nii.gz \
                        --interp=spline \
                        -o f_skip_stc_mc_fm.nii.gz"
                run_command(cmd, log_fid)
                
            log_fid.write('\n 1-st level preprocessing for task fMRI data finished \n')
        else: 
            log_fid.write('\n ' + func_fm_dir + ' exists...\n')
        
        
        
        # -------------------------------------------------------------------------
        if not os.path.exists(register_dir):                        
            cmd = f"bbregister --s {freesurfer_sub_id_reconname} --init-fsl --6 --bold --mov {func_stc_mc_fm_1vol_dir} \
                                --reg {register_dir} --init-reg-out {init_register_dir} --frame 0"
            run_command(cmd, log_fid)
        else:
            log_fid.write('\n ' + register_dir + 'for ' + func_stc_mc_fm_1vol_dir + ' exists...\n')
        
        if not os.path.exists(func_fm_downsample_dir):
            # downsample 
            cmd = f"mri_convert --voxsize \
                    {origvoxelSize[0]} {origvoxelSize[1]} {origvoxelSize[2]} \
                    f_skip_stc_mc_fm.nii.gz {func_fm_downsample_dir}" 
            run_command(cmd, log_fid)        
                                    
        if not os.path.exists(mask_global_dir_func):            
            # global_mask_func in this way is good, not with neck part. cause recon_anat_brain_dir is good
            # we can also use registered_anat_brain_dir to run f"fast -B -I 10 -l 10 {anat_brain_dir}" to Segment the T1 brain into gm, wm, and csf
            cmd = f"mri_vol2vol --mov {recon_anat_brain_dir} --regheader \
                    --targ {func_stc_mc_fm_1vol_dir} \
                    --o {registered_anat_brain_dir} --no-save-reg"
            run_command(cmd, log_fid)            
            cmd = f"mri_binarize --i {registered_anat_brain_dir} \
                --o {anat_mask_global_dir} \
                --min 0.1"
            run_command(cmd, log_fid) 
            # downsample
            cmd = f"mri_convert --voxsize \
                {origvoxelSize[0]} {origvoxelSize[1]} {origvoxelSize[2]} \
                {anat_mask_global_dir} {mask_global_dir_func}"
            run_command(cmd, log_fid)
        
        if not os.path.exists(mask_wm_dir_func):  

            # wm_mask_func
            path_tmp = os.path.join(run_func_output_dir_name,'tmp')
            path_tmp1_dir = os.path.join(path_tmp,'wm.seg.recon.nii.gz')
            path_tmp2_dir = os.path.join(path_tmp,'wm.seg.anat.nii.gz')
            path_tmp3_dir = os.path.join(path_tmp,'pvf.nii.gz')
            os.makedirs(path_tmp, exist_ok=True)
            
            cmd = f"mri_binarize --i {theAparcAseg_dir}  --o {path_tmp1_dir} \
                --match 2 41 7 46 251 252 253 254 255 77 78 79"
            run_command(cmd, log_fid) 
            cmd = f"mri_label2vol --seg {path_tmp1_dir} --reg {register_dir} \
                --temp {func_stc_mc_fm_1vol_dir} --fillthresh 0.95 --o {path_tmp2_dir} --pvf {path_tmp3_dir}"
            run_command(cmd, log_fid) 
            cmd = f"mri_binarize --i {path_tmp2_dir} --o {anat_mask_wm_dir} --match 1"
            run_command(cmd, log_fid) 
            # downsample 
            cmd = f"mri_convert --voxsize \
                    {origvoxelSize[0]} {origvoxelSize[1]} {origvoxelSize[2]} \
                    {anat_mask_wm_dir} {mask_wm_dir_func}" 
            run_command(cmd, log_fid)
            cmd = f"rm -rf {path_tmp}"
            run_command(cmd, log_fid)

            # vcsf_mask_func
            path_tmp = os.path.join(run_func_output_dir_name,'tmp')
            path_tmp2_dir = os.path.join(path_tmp,'vcsf.seg.anat.nii.gz')
            path_tmp3_dir = os.path.join(path_tmp,'pvf.nii.gz')
            os.makedirs(path_tmp, exist_ok=True)

            cmd = f"mri_label2vol --seg {theAparcAseg_dir} --reg {register_dir} \
                --temp {func_stc_mc_fm_1vol_dir} --fillthresh 0.95 --o {path_tmp2_dir} --pvf {path_tmp3_dir}"
            run_command(cmd, log_fid) 
            cmd = f"mri_binarize --i {path_tmp2_dir} --o {anat_mask_vcsf_dir} --match 4 5 43 44 31 63"
            run_command(cmd, log_fid) 
            # downsample 
            cmd = f"mri_convert --voxsize \
                    {origvoxelSize[0]} {origvoxelSize[1]} {origvoxelSize[2]} \
                    {anat_mask_vcsf_dir} {mask_vcsf_dir_func}" 
            run_command(cmd, log_fid)
            cmd = f"rm -rf {path_tmp}"
            run_command(cmd, log_fid)
            # lh self
            cmd = f"mri_vol2surf --mov {func_stc_mc_fm_1vol_dir} \
                --reg {register_dir} \
                --trgsubject {freesurfer_sub_id_reconname} \
                --interp nearest --projfrac 0.5 \
                --hemi lh --o {mask_surf_lh_dir_func} \
                --noreshape --cortex"
            run_command(cmd, log_fid)
            cmd = f"mri_binarize --i {mask_surf_lh_dir_func} \
                --min 0.00001 --o {mask_surf_lh_dir_func}"
            run_command(cmd, log_fid)
            
            # rh self
            cmd = f"mri_vol2surf --mov {func_stc_mc_fm_1vol_dir} \
                --reg {register_dir} \
                --trgsubject {freesurfer_sub_id_reconname} \
                --interp nearest --projfrac 0.5 \
                --hemi rh --o {mask_surf_rh_dir_func} \
                --noreshape --cortex"
            run_command(cmd, log_fid)
            cmd = f"mri_binarize --i {mask_surf_rh_dir_func} \
                --min 0.00001 --o {mask_surf_rh_dir_func}"
            run_command(cmd, log_fid)
            
        else:
            log_fid.write('Func_to_anat register.dat, and global, wm, vcsf masks_func, lh and rh cortex masks exist for ' + freesurfer_sub_id + '\n')
        
        if not os.listdir(func_output_dir_clean):
            # ----------------------------------------------------------------------
            # calculate confounds 
            # just calculate global mean signal intensity but not regressed, added by wrx
            cmd = f"meanval --i {func_fm_downsample_dir} \
                --m {mask_global_dir_func} --o {global_meanval_dir} --avgwf {global_waveform_dir}"
            run_command(cmd, log_fid)   
        
            # extract WM signals
            cmd = f"mri_glmfit --y {func_fm_downsample_dir} \
                --mask {mask_wm_dir_func} --qa --glmdir {path_glm_wm} \
                --pca --no-est-fwhm"
            run_command(cmd, log_fid)
            cmd = f"meanval --i {func_fm_downsample_dir} \
                --m {mask_wm_dir_func} --o {wm_meanval_dir} --avgwf {wm_waveform_dir}"
            run_command(cmd, log_fid)

            cmd = f"cp {wm_pcs_dir_orig} {wm_pcs_dir}"
            run_command(cmd, log_fid)

            # extract VCSF signals                    
            cmd = f"mri_glmfit --y {func_fm_downsample_dir} \
                --mask {mask_vcsf_dir_func} --qa --glmdir {path_glm_vcsf} \
                --pca --no-est-fwhm"
            run_command(cmd, log_fid)
            cmd = f"meanval --i {func_fm_downsample_dir} \
                --m {mask_vcsf_dir_func} --o {vcsf_meanval_dir} --avgwf {vcsf_waveform_dir}"
            run_command(cmd, log_fid)

            cmd = f"cp {vcsf_pcs_dir_orig} {vcsf_pcs_dir}"
            run_command(cmd, log_fid)
            
            ## ----------------------------------------------------------------------
            ## merge regressors together
            motion_regressors = np.loadtxt(mc_data_dir)
            wm_regressors = np.loadtxt(wm_pcs_dir)[:,:5]
            vcsf_regressors = np.loadtxt(vcsf_pcs_dir)[:,:5]
            multi_regressors = np.column_stack((motion_regressors, wm_regressors, vcsf_regressors))
            np.savetxt(confound_ev_dir, multi_regressors)

            multi_regressors_zscore = standardize_signal(multi_regressors, standardize="zscore_sample", detrend=False)
            np.savetxt(confound_ev_zscore_dir, multi_regressors_zscore)

            meanwm_regressors = np.loadtxt(wm_waveform_dir)
            meanvcsf_regressors = np.loadtxt(vcsf_waveform_dir)
            multi_avgwf_regressors = np.column_stack((motion_regressors, meanwm_regressors, meanvcsf_regressors))
            np.savetxt(confound_ev_avgwf_dir, multi_avgwf_regressors)

            multi_avgwf_regressors_zscore = standardize_signal(multi_avgwf_regressors, standardize="zscore_sample", detrend=False)
            np.savetxt(confound_ev_avgwf_zscore_dir, multi_avgwf_regressors)
                                    
            ## get simptask, clean, taskforFC img using orthogonal methods
            log_fid.write('Get simptask, clean, forFC volume data for ' + filetopreproc_dir + '.\n')

            img = image.load_img(func_fm_downsample_dir)
            img_mean = image.math_img('np.mean(img, axis=3)', img=img)
            img_demean = image.math_img('img-img_mean[:,:,:,np.newaxis]', img=img, img_mean=img_mean)
            
            simptask_img = image.clean_img(img, 
                                    confounds=mc_data_dir, 
                                    t_r = TR_s, 
                                    high_pass=1/128, 
                                    low_pass=None,
                                    clean__filter="cosine",
                                    clean__standardize_confounds="zscore_sample",
                                    detrend=True, 
                                    standardize=False,
                                    ensure_finite=True, 
                                    mask_img=mask_global_dir_func)
            nib.save(simptask_img, func_simptask_dir)
            
            clean_img = image.clean_img(img, 
                                    confounds=confound_ev_dir, 
                                    t_r = TR_s, 
                                    high_pass=1/128, 
                                    low_pass=None,
                                    clean__filter="cosine",
                                    clean__standardize_confounds="zscore_sample",
                                    detrend=True, 
                                    standardize=False,
                                    ensure_finite=True, 
                                    mask_img=mask_global_dir_func)

            nib.save(clean_img, func_clean_dir)


        else:
            log_fid.write('preprocessed volume files for ' + filetopreproc_dir + 'already existed. \n')
        
        if not os.path.exists(register_dir_downsampledfunc):                        
            cmd = f"bbregister --s {freesurfer_sub_id_reconname} --init-fsl --6 --bold --mov {func_fm_downsample_dir} \
                                --reg {register_dir_downsampledfunc} --init-reg-out {init_register_dir_downsampledfunc} --frame 0"
            run_command(cmd, log_fid)
        else:
            log_fid.write('\n ' + register_dir_downsampledfunc + 'for downsampled func:' + func_fm_downsample_dir + ' exists...\n')
        
            
        if not os.path.exists(func_clean_sm_dir):
            ## 6mm FWHM 
            ## simptask    
            cmd = f"3dBlurToFWHM -mask {mask_global_dir_func} -FWHM {vol_FWHM} \
                -input {func_simptask_dir} -prefix {func_simptask_sm_dir} \
                -blurmaster {func_simptask_dir} -detrend -bmall"
            run_command(cmd, log_fid) 
            ## clean                
            cmd = f"3dBlurToFWHM -mask {mask_global_dir_func} -FWHM {vol_FWHM} \
                -input {func_clean_dir} -prefix {func_clean_sm_dir} \
                -blurmaster {func_clean_dir} -detrend -bmall"
            run_command(cmd, log_fid) 
            
            
        if not os.path.exists(surf_simptask_rh_dir):
            ## ==== vol2surf =======
            
            ## ---------------- lh ------------------                            
            ## downsampled f_simptask.nii.gz to surf                                                                       
            cmd = f"mri_vol2surf --mov {func_simptask_dir} \
                --reg {register_dir_downsampledfunc} \
                --trgsubject {freesurfer_sub_id_reconname} \
                --interp trilin --projfrac 0.5 \
                --hemi lh --o {surf_simptask_lh_dir} \
                --noreshape --cortex"
            run_command(cmd, log_fid)
            
            ## f_clean.nii.gz to surf
            cmd = f"mri_vol2surf --mov {func_clean_dir} \
                --reg {register_dir_downsampledfunc} \
                --trgsubject {freesurfer_sub_id_reconname} \
                --interp trilin --projfrac 0.5 \
                --hemi lh --o {surf_clean_lh_dir} \
                --noreshape --cortex"
            run_command(cmd, log_fid)                    
            
            ## ---------------- rh ------------------                            
            ## f_simptask.nii.gz to surf
            cmd = f"mri_vol2surf --mov {func_simptask_dir} \
                --reg {register_dir_downsampledfunc} \
                --trgsubject {freesurfer_sub_id_reconname} \
                --interp trilin --projfrac 0.5 \
                --hemi rh --o {surf_simptask_rh_dir} \
                --noreshape --cortex"
            run_command(cmd, log_fid)
            
            ## f_clean.nii.gz to surf
            cmd = f"mri_vol2surf --mov {func_clean_dir} \
                --reg {register_dir_downsampledfunc} \
                --trgsubject {freesurfer_sub_id_reconname} \
                --interp trilin --projfrac 0.5 \
                --hemi rh --o {surf_clean_rh_dir} \
                --noreshape --cortex"
            run_command(cmd, log_fid)

            
        if not os.path.exists(fs5_simptask_rh_sm_dir): 
            ## ==== surf2surf and fs5smooth =======                        
            ## ---------------- lh ------------------    
            ## simptask                                                
            # Convert to fsaverage5-space              
            cmd = f"mri_surf2surf --sval {surf_simptask_lh_dir} \
                --srcsubject {freesurfer_sub_id_reconname} \
                --trgsubject fsaverage5 \
                --hemi lh --tval {fs5_simptask_lh_dir} \
                --cortex"
            run_command(cmd, log_fid)
            # smooth
            cmd = f"mris_fwhm --s fsaverage5 \
                --hemi lh --smooth-only \
                --i {fs5_simptask_lh_dir} \
                --fwhm {surf_FWHM} \
                --o {fs5_simptask_lh_sm_dir} \
                --cortex"
            run_command(cmd, log_fid)
            
            ## clean                                                
            # Convert to fsaverage5-space
            cmd = f"mri_surf2surf --sval {surf_clean_lh_dir} \
                --srcsubject {freesurfer_sub_id_reconname} \
                --trgsubject fsaverage5 \
                --hemi lh --tval {fs5_clean_lh_dir} \
                --cortex"
            run_command(cmd, log_fid)
            # smooth
            cmd = f"mris_fwhm --s fsaverage5 \
                --hemi lh --smooth-only \
                --i {fs5_clean_lh_dir} \
                --fwhm {surf_FWHM} \
                --o {fs5_clean_lh_sm_dir} \
                --cortex"
            run_command(cmd, log_fid)
            
            
            ## ---------------- rh ------------------    
            ## simptask
            cmd = f"mri_surf2surf --sval {surf_simptask_rh_dir} \
                --srcsubject {freesurfer_sub_id_reconname} \
                --trgsubject fsaverage5 \
                --hemi rh --tval {fs5_simptask_rh_dir} \
                --cortex"
            run_command(cmd, log_fid)
            # smooth
            cmd = f"mris_fwhm --s fsaverage5 \
                --hemi rh --smooth-only \
                --i {fs5_simptask_rh_dir} \
                --fwhm {surf_FWHM} \
                --o {fs5_simptask_rh_sm_dir} \
                --cortex"
            run_command(cmd, log_fid)
            
            ## clean                                                
            # Convert to fsaverage5-space
            cmd = f"mri_surf2surf --sval {surf_clean_rh_dir} \
                --srcsubject {freesurfer_sub_id_reconname} \
                --trgsubject fsaverage5 \
                --hemi rh --tval {fs5_clean_rh_dir} \
                --cortex"
            run_command(cmd, log_fid)
            # smooth
            cmd = f"mris_fwhm --s fsaverage5 \
                --hemi rh --smooth-only \
                --i {fs5_clean_rh_dir} \
                --fwhm {surf_FWHM} \
                --o {fs5_clean_rh_sm_dir} \
                --cortex"
            run_command(cmd, log_fid)
            log_fid.write('\n Successfully preprocess the session ' + filetopreproc_dir + ' \n')
        else:
            log_fid.write('preprocessed surface files for ' + filetopreproc_dir + 'already existed. \n')
        
        if os.path.exists('f_skip_stc_mc.nii.gz'): 
            os.remove('f_skip_stc_mc.nii.gz')
            os.remove('f_skip_stc.nii.gz')
                    
    log_fid.write('\n Successfully run preproc-sess of ' + freesurfer_sub_id + '\n')
             
#%% Close log file
current_time = datetime.datetime.now()
log_fid.write('\nEnd:\t' + current_time.strftime('%Y%m%d:%H:%M:%S') + '.\n')
log_fid.close()

# # Print log
# print('\n\n\n')
# with open(log_fid.name, 'r') as file:
#     print(file.read())
