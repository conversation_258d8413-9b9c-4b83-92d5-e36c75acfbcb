import numpy as np
import scipy
import scipy.io as sio
import matplotlib.pyplot as plt
import os
import sys
import re
import glob
from os.path import join, exists, split
import h5py
import nibabel as nib
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp
from functools import partial
import pickle
import time

# 添加 rootfolder 到 sys.path
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), "..")))
print(os.path.join(os.path.dirname(os.getcwd())))
print(os.path.abspath(os.path.join(os.getcwd(), "..",'preprocess')))

# 简单的对象保存和加载函数，替代preprocess.functions
def saveObject(obj, filename):
    """保存对象到文件"""
    with open(filename, 'wb') as f:
        pickle.dump(obj, f)

def openObject(filename):
    """从文件加载对象"""
    with open(filename, 'rb') as f:
        return pickle.load(f)

# 添加 FSL bin 路径到环境变量，便于调用fsl命令
os.environ["FSLDIR"] = "/usr/local/fsl"
os.environ["PATH"] += os.pathsep + "/usr/local/fsl/bin"
# 设置 FreeSurfer 安装目录，便于调用fsl命令
os.environ["FREESURFER_HOME"] = "/usr/local/freesurfer"
os.environ["PATH"] += os.pathsep + os.path.join(os.environ["FREESURFER_HOME"], "bin")
os.environ["SUBJECTS_DIR"] = "/opt/data/private/lq/data/NSD/nsddata/freesurfer"

# 全局配置
base_dir = '/opt/data/private/lq/data/NSD/nsddata_rawdata'
preproc_dir = '/opt/data/private/lq/data/NSD/nsddata_preprocdata/'
designmatrix_dir = '/opt/data/private/lq/data/NSD/nsddata/experiments/nsd/' 
nsd_ref_beta_dir = '/opt/data/private/lq/data/NSD/nsddata_betas/ppdata'
TR_s = 1.6 # nsd: 1.6; our data: 1.5
tr_target = 1.0
datatype = 'nsd'

def find_sessions(nativesurf_inputdir):
    """
    自动搜索指定路径下的session文件
    只搜索左脑lh.betas_session--.hdf5文件，因为左右脑数量一致
    
    Args:
        nativesurf_inputdir: 输入目录路径
        
    Returns:
        list: session编号列表，如['01', '02', '03', ...]
    """
    # 搜索左脑的session文件
    pattern = join(nativesurf_inputdir, "lh.betas_session*.hdf5")
    session_files = glob.glob(pattern)
    
    # 提取session编号
    sessions = []
    for file_path in session_files:
        filename = os.path.basename(file_path)
        # 使用正则表达式提取session编号
        match = re.search(r'lh\.betas_session(\d+)\.hdf5', filename)
        if match:
            session_num = match.group(1)
            sessions.append(session_num)
    
    # 排序session编号
    sessions.sort()
    print(f"Found {len(sessions)} sessions: {sessions}")
    return sessions

def process_single_session(args):
    """
    处理单个session的函数，用于并行处理
    
    Args:
        args: 包含所有必要参数的元组
        
    Returns:
        dict: 处理结果信息
    """
    (freesurfer_sub_id, session_num, nativesurf_inputdir, 
     fs5_outputdir, freesurfer_sub_id_reconname) = args
    
    try:
        print(f"Processing {freesurfer_sub_id} session {session_num}...")
        
        # 定义文件路径
        nsd_surf_data_dir = {
            'lh': join(nativesurf_inputdir, f"lh.betas_session{session_num}.hdf5"),
            'rh': join(nativesurf_inputdir, f"rh.betas_session{session_num}.hdf5"),
        }
        
        nsd_surf_dir = {
            'lh': join(nativesurf_inputdir, f"lh.betas_session{session_num}.nii.gz"),
            'rh': join(nativesurf_inputdir, f"rh.betas_session{session_num}.nii.gz"),
        }
        
        nsd_fs5_dir = {
            'lh': join(fs5_outputdir, f"lh.betas_session{session_num}.fs5.nii.gz"),
            'rh': join(fs5_outputdir, f"rh.betas_session{session_num}.fs5.nii.gz"),
        }
        
        nsd_fs5_sm_dir = {
            'lh': join(fs5_outputdir, f"lh.betas_session{session_num}.fs5.sm.nii.gz"),
            'rh': join(fs5_outputdir, f"rh.betas_session{session_num}.fs5.sm.nii.gz"),
        }
        
        # 检查输入文件是否存在
        for hem in ['lh', 'rh']:
            if not exists(nsd_surf_data_dir[hem]):
                print(f"Warning: {nsd_surf_data_dir[hem]} does not exist, skipping...")
                return {'status': 'skipped', 'subject': freesurfer_sub_id, 
                       'session': session_num, 'reason': 'input file missing'}
        
        # 读取HDF5数据并转换为NIfTI格式
        ref_data = {}
        for hem in ['lh', 'rh']:
            with h5py.File(nsd_surf_data_dir[hem], "r") as f:
                # 查看文件中所有主键（数据集或组）
                print(f"Processing {hem} hemisphere, keys: {list(f.keys())}")
                
                # 读取betas数据
                ref_data[hem] = f["betas"][:]   # 读取数据为 numpy 数组
                print(f'Original shape of {hem} data: {ref_data[hem].shape}')
                
                # 转换数据格式为NIfTI
                img_data = np.zeros((ref_data[hem].shape[-1], 1, 1, ref_data[hem].shape[0]))
                img_data[:,0,0] = ref_data[hem].T.copy()
                vol_img = nib.Nifti1Image(img_data, affine=np.eye(4))
                nib.save(vol_img, nsd_surf_dir[hem])
        
        # 转换到fsaverage5空间并平滑
        for hem in ['lh', 'rh']:
            # Convert to fsaverage-space
            cmd = f"mri_surf2surf --sval {nsd_surf_dir[hem]} \
                --srcsubject {freesurfer_sub_id_reconname} \
                --trgsubject fsaverage5 \
                --hemi {hem} --tval {nsd_fs5_dir[hem]} \
                --cortex"
            
            result = os.system(cmd)
            if result != 0:
                print(f"Error in mri_surf2surf for {hem} hemisphere")
                return {'status': 'error', 'subject': freesurfer_sub_id, 
                       'session': session_num, 'step': 'mri_surf2surf', 'hemisphere': hem}
            
            # 平滑处理
            cmd = f"mris_fwhm --s fsaverage5 \
                --hemi {hem} --smooth-only \
                --i {nsd_fs5_dir[hem]} \
                --fwhm 4 \
                --o {nsd_fs5_sm_dir[hem]} \
                --cortex"
            
            result = os.system(cmd)
            if result != 0:
                print(f"Error in mris_fwhm for {hem} hemisphere")
                return {'status': 'error', 'subject': freesurfer_sub_id, 
                       'session': session_num, 'step': 'mris_fwhm', 'hemisphere': hem}
        
        print(f"Successfully processed {freesurfer_sub_id} session {session_num}")
        return {'status': 'success', 'subject': freesurfer_sub_id, 'session': session_num}
        
    except Exception as e:
        print(f"Error processing {freesurfer_sub_id} session {session_num}: {str(e)}")
        return {'status': 'error', 'subject': freesurfer_sub_id, 
               'session': session_num, 'error': str(e)}

def check_existing_outputs(all_tasks):
    """
    检查哪些任务已经完成，避免重复处理

    Args:
        all_tasks: 所有任务列表

    Returns:
        list: 需要处理的任务列表
    """
    tasks_to_process = []

    for task_args in all_tasks:
        freesurfer_sub_id, session_num, nativesurf_inputdir, fs5_outputdir, freesurfer_sub_id_reconname = task_args

        # 检查输出文件是否已存在
        output_files = [
            join(fs5_outputdir, f"lh.betas_session{session_num}.fs5.sm.nii.gz"),
            join(fs5_outputdir, f"rh.betas_session{session_num}.fs5.sm.nii.gz")
        ]

        if all(exists(f) for f in output_files):
            print(f"Skipping {freesurfer_sub_id} session {session_num} - outputs already exist")
        else:
            tasks_to_process.append(task_args)

    return tasks_to_process

def main(skip_existing=True, max_workers=None, test_mode=False):
    """
    主函数：自动搜索session并并行处理sub-01到sub-08的所有session

    Args:
        skip_existing: 是否跳过已存在的输出文件
        max_workers: 最大并行工作进程数，None表示自动选择
        test_mode: 测试模式，只处理每个被试的第一个session
    """
    print("NSD Beta Projection to fsaverage5")
    print("=================================")

    # 定义被试列表：sub-01到sub-08
    freeSurfer_subID_list = [f'sub-{i:02d}' for i in range(1, 9)]

    # 收集所有需要处理的任务
    all_tasks = []

    for freesurfer_sub_id in freeSurfer_subID_list:
        # 转换被试ID格式
        freesurfer_sub_id_reconname = freesurfer_sub_id.replace("sub-", "subj") if datatype == 'nsd' else freesurfer_sub_id

        # 定义输入和输出目录
        nativesurf_inputdir = join(nsd_ref_beta_dir, freesurfer_sub_id_reconname,
                                  "nativesurface", "betas_fithrf_GLMdenoise_RR")
        fs5_outputdir = join(nsd_ref_beta_dir, freesurfer_sub_id_reconname,
                            "fsaverage5", "betas_fithrf_GLMdenoise_RR")

        # 检查输入目录是否存在
        if not exists(nativesurf_inputdir):
            print(f"Warning: Input directory {nativesurf_inputdir} does not exist, skipping {freesurfer_sub_id}")
            continue

        # 创建输出目录
        os.makedirs(fs5_outputdir, exist_ok=True)

        # 自动搜索该被试的所有session
        sessions = find_sessions(nativesurf_inputdir)

        if not sessions:
            print(f"No sessions found for {freesurfer_sub_id}")
            continue

        # 测试模式只处理第一个session
        if test_mode:
            sessions = sessions[:1]
            print(f"Test mode: only processing first session for {freesurfer_sub_id}")

        # 为每个session创建任务
        for session_num in sessions:
            task_args = (freesurfer_sub_id, session_num, nativesurf_inputdir,
                        fs5_outputdir, freesurfer_sub_id_reconname)
            all_tasks.append(task_args)

    print(f"Total tasks found: {len(all_tasks)}")

    if not all_tasks:
        print("No tasks to process!")
        return

    # 检查已存在的输出文件
    if skip_existing:
        all_tasks = check_existing_outputs(all_tasks)
        print(f"Tasks to process after skipping existing: {len(all_tasks)}")

    if not all_tasks:
        print("All tasks already completed!")
        return

    # 设置并行工作进程数
    if max_workers is None:
        max_workers = min(mp.cpu_count(), 6)  # 限制最大并行数，避免系统过载

    print(f"Using {max_workers} parallel workers")

    start_time = time.time()
    results = []
    completed_count = 0

    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_task = {executor.submit(process_single_session, task): task for task in all_tasks}

        # 收集结果并显示进度
        for future in as_completed(future_to_task):
            task = future_to_task[future]
            completed_count += 1

            try:
                result = future.result()
                results.append(result)

                # 显示进度
                progress = (completed_count / len(all_tasks)) * 100
                elapsed_time = time.time() - start_time
                estimated_total_time = elapsed_time * len(all_tasks) / completed_count
                remaining_time = estimated_total_time - elapsed_time

                print(f"[{completed_count}/{len(all_tasks)}] ({progress:.1f}%) "
                      f"Completed: {result['subject']} session {result['session']} - "
                      f"Status: {result['status']} | "
                      f"Remaining: {remaining_time/60:.1f} min")

            except Exception as exc:
                print(f"Task {task} generated an exception: {exc}")
                results.append({'status': 'exception', 'task': task, 'error': str(exc)})

    end_time = time.time()

    # 统计结果
    successful = sum(1 for r in results if r['status'] == 'success')
    failed = sum(1 for r in results if r['status'] in ['error', 'exception'])
    skipped = sum(1 for r in results if r['status'] == 'skipped')

    print(f"\n=== Processing Summary ===")
    print(f"Total tasks: {len(all_tasks)}")
    print(f"Successful: {successful}")
    print(f"Failed: {failed}")
    print(f"Skipped: {skipped}")
    print(f"Total time: {end_time - start_time:.2f} seconds ({(end_time - start_time)/60:.1f} minutes)")

    # 保存结果到文件
    results_file = f"projection_results_{int(time.time())}.pkl"
    saveObject(results, results_file)
    print(f"Results saved to: {results_file}")

    # 显示失败的任务
    if failed > 0:
        print(f"\n=== Failed Tasks ===")
        for result in results:
            if result['status'] in ['error', 'exception']:
                print(f"Subject: {result.get('subject', 'unknown')}, "
                      f"Session: {result.get('session', 'unknown')}, "
                      f"Error: {result.get('error', result.get('reason', 'unknown'))}")

    return results

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='NSD Beta Projection to fsaverage5')
    parser.add_argument('--test', action='store_true',
                       help='Test mode: only process first session for each subject')
    parser.add_argument('--no-skip', action='store_true',
                       help='Do not skip existing output files')
    parser.add_argument('--workers', type=int, default=None,
                       help='Number of parallel workers (default: auto)')
    parser.add_argument('--dry-run', action='store_true',
                       help='Dry run: only show what would be processed')

    args = parser.parse_args()

    if args.dry_run:
        # 只显示会处理什么，不实际执行
        print("DRY RUN MODE - No actual processing will be performed")
        print("="*50)

        freeSurfer_subID_list = [f'sub-{i:02d}' for i in range(1, 9)]
        total_tasks = 0

        for freesurfer_sub_id in freeSurfer_subID_list:
            freesurfer_sub_id_reconname = freesurfer_sub_id.replace("sub-", "subj") if datatype == 'nsd' else freesurfer_sub_id
            nativesurf_inputdir = join(nsd_ref_beta_dir, freesurfer_sub_id_reconname,
                                      "nativesurface", "betas_fithrf_GLMdenoise_RR")

            if exists(nativesurf_inputdir):
                sessions = find_sessions(nativesurf_inputdir)
                if args.test:
                    sessions = sessions[:1]
                print(f"{freesurfer_sub_id}: {len(sessions)} sessions")
                total_tasks += len(sessions)
            else:
                print(f"{freesurfer_sub_id}: directory not found")

        print(f"\nTotal tasks that would be processed: {total_tasks}")

    else:
        # 实际执行处理
        main(skip_existing=not args.no_skip,
             max_workers=args.workers,
             test_mode=args.test)
