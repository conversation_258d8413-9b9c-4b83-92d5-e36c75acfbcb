# NSD Beta投影到fsaverage5皮层 - 并行处理版本

## 概述

这个项目包含了将NSD数据集的beta值从native surface投影到fsaverage5标准皮层的并行处理脚本。基于原始的`projections.py`，扩展为支持：

1. 自动搜索指定路径下的所有session文件
2. 处理sub-01到sub-08的8个被试
3. 并行处理所有session以提高效率
4. 完善的错误处理和进度监控

## 文件说明

### 主要脚本

1. **`projections_parallel.py`** - 主要的并行处理脚本
   - 自动搜索session文件
   - 并行处理多个被试和session
   - 包含进度监控和错误处理
   - 支持命令行参数

2. **`projections_test.py`** - 测试脚本
   - 用于验证路径设置和session搜索
   - 检查数据文件的完整性
   - 估算处理时间

3. **`projections.py`** - 原始脚本（已修改）
   - 原始的单session处理版本

### 配置文件

- **`README_projections.md`** - 本说明文档

## 使用方法

### 1. 测试环境和数据

首先运行测试脚本确认环境配置正确：

```bash
python projections_test.py
```

这将：
- 检查基础路径是否存在
- 测试sub-01的session搜索
- 显示所有被试的session统计
- 估算总处理时间

### 2. 干运行（推荐）

在实际处理前，建议先进行干运行查看会处理哪些数据：

```bash
python projections_parallel.py --dry-run
```

### 3. 测试模式

只处理每个被试的第一个session进行测试：

```bash
python projections_parallel.py --test
```

### 4. 完整处理

处理所有被试的所有session：

```bash
python projections_parallel.py
```

### 5. 高级选项

```bash
# 不跳过已存在的输出文件（重新处理）
python projections_parallel.py --no-skip

# 指定并行工作进程数
python projections_parallel.py --workers 4

# 组合使用
python projections_parallel.py --test --workers 2 --no-skip
```

## 命令行参数

- `--test`: 测试模式，只处理每个被试的第一个session
- `--no-skip`: 不跳过已存在的输出文件，重新处理所有数据
- `--workers N`: 指定并行工作进程数（默认自动选择）
- `--dry-run`: 干运行模式，只显示会处理什么，不实际执行

## 输出文件

处理完成后，每个session会生成以下文件：

```
{nsd_ref_beta_dir}/{subject}/fsaverage5/betas_fithrf_GLMdenoise_RR/
├── lh.betas_session{XX}.fs5.nii.gz      # 左脑投影结果
├── rh.betas_session{XX}.fs5.nii.gz      # 右脑投影结果
├── lh.betas_session{XX}.fs5.sm.nii.gz   # 左脑平滑后结果
└── rh.betas_session{XX}.fs5.sm.nii.gz   # 右脑平滑后结果
```

## 处理流程

1. **Session搜索**: 自动搜索`lh.betas_session*.hdf5`文件
2. **数据转换**: 将HDF5格式转换为NIfTI格式
3. **空间投影**: 使用`mri_surf2surf`投影到fsaverage5空间
4. **平滑处理**: 使用`mris_fwhm`进行4mm FWHM平滑

## 系统要求

### 软件依赖

- Python 3.6+
- FreeSurfer (需要`mri_surf2surf`和`mris_fwhm`命令)
- FSL (环境变量设置)

### Python包依赖

```python
numpy
scipy
matplotlib
h5py
nibabel
```

### 硬件建议

- 内存: 至少16GB（推荐32GB+）
- CPU: 多核处理器（并行处理）
- 存储: 足够的磁盘空间存储输出文件

## 性能优化

1. **并行处理**: 默认使用CPU核心数的较小值作为并行进程数
2. **跳过已处理**: 自动跳过已存在的输出文件
3. **内存管理**: 每个进程独立处理，避免内存泄漏

## 错误处理

脚本包含完善的错误处理：

1. **文件检查**: 处理前检查输入文件是否存在
2. **命令验证**: 检查FreeSurfer命令执行结果
3. **异常捕获**: 捕获并记录处理异常
4. **结果保存**: 将处理结果保存到pickle文件

## 监控和日志

- 实时显示处理进度
- 估算剩余时间
- 详细的错误报告
- 处理结果统计

## 故障排除

### 常见问题

1. **FreeSurfer命令找不到**
   - 检查`FREESURFER_HOME`环境变量
   - 确认FreeSurfer已正确安装

2. **输入文件不存在**
   - 检查`nsd_ref_beta_dir`路径设置
   - 确认数据文件已正确下载

3. **内存不足**
   - 减少并行工作进程数：`--workers 2`
   - 使用测试模式先处理部分数据

4. **权限问题**
   - 确认对输出目录有写权限
   - 检查FreeSurfer subjects目录权限

### 调试建议

1. 先运行`projections_test.py`检查环境
2. 使用`--dry-run`查看处理计划
3. 使用`--test`模式处理少量数据
4. 检查生成的结果文件

## 联系信息

如有问题或建议，请联系开发团队。
