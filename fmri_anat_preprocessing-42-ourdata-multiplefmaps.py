#!/usr/bin/env python
# coding: utf-8

# # fMRI Processing

# ## 1. Preparations
#
# initial setups

# In[1]:


import os
import datetime
import numpy as np
import nibabel as nib
import scipy
import scipy.signal as ss
import scipy.io as io
from matplotlib import pyplot as plt

from glob import glob
from functions import find_files_with_string
from nilearn.plotting import plot_anat, plot_roi
from nilearn import plotting

# 添加 FSL bin 路径到环境变量，便于调用fsl命令
os.environ["FSLDIR"] = "/share/home/<USER>/software/fsl/"
os.environ["PATH"] += os.pathsep + "/share/home/<USER>/software/fsl/bin"

# 设置freesurfer_sub_id
base_dir = '/share/home/<USER>/data/our_nsd/bids'
freesurfer_sub_id = 'sub-01'

# 设置session数量
ses_len = 32  # 可以修改这个值来处理不同数量的session


def setup_anat_directories():
    """设置解剖图像相关的目录和文件路径"""
    ## set up input folders
    anat_input_dir = os.path.join(base_dir, freesurfer_sub_id, 'ses-01', 'anat')
    anat_output_dir = os.path.join(anat_input_dir, 'anat_output')
    reg_output_dir = os.path.join(anat_input_dir, 'reg_output')

    ## check and create folders
    folders = [anat_input_dir, anat_output_dir, reg_output_dir]
    for folder in folders:
        if not os.path.exists(folder):
            os.makedirs(folder, exist_ok=True)

    ## set up input and output filenames
    anat_T1_dir = os.path.join(anat_input_dir, f"{freesurfer_sub_id}_ses-01_T1w.nii.gz")
    anat_brain_dir = os.path.join(anat_output_dir, 'T1_brain.nii.gz')
    anat_wmseg_dir = os.path.join(anat_output_dir, 'T1_wmseg.nii.gz')
    anat_pve0_dir = os.path.join(anat_output_dir, 'T1_brain_pve_0.nii.gz')
    anat_pve1_dir = os.path.join(anat_output_dir, 'T1_brain_pve_1.nii.gz')
    anat_pve2_dir = os.path.join(anat_output_dir, 'T1_brain_pve_2.nii.gz')
    anat_gm_mask_dir = os.path.join(anat_output_dir, "T1_gm_mask.nii.gz")

    tcustom_dir = os.path.join(base_dir, 'tcustom_epi.txt')
    mask_global_dir = os.path.join(anat_output_dir, 'global_mask.nii.gz')

    return {
        'anat_input_dir': anat_input_dir,
        'anat_output_dir': anat_output_dir,
        'reg_output_dir': reg_output_dir,
        'anat_T1_dir': anat_T1_dir,
        'anat_brain_dir': anat_brain_dir,
        'anat_wmseg_dir': anat_wmseg_dir,
        'anat_pve0_dir': anat_pve0_dir,
        'anat_pve1_dir': anat_pve1_dir,
        'anat_pve2_dir': anat_pve2_dir,
        'anat_gm_mask_dir': anat_gm_mask_dir,
        'tcustom_dir': tcustom_dir,
        'mask_global_dir': mask_global_dir,
    }

def setup_field_directories(ses_id):
    """设置field map相关的目录和文件路径"""
    ## set up input folders
    field_input_dir = os.path.join(base_dir, freesurfer_sub_id, ses_id, 'fmap')

    ## set up output folders
    field_output_dir = os.path.join(field_input_dir, 'field_output')
    field_QC_fig_dir = os.path.join(field_input_dir, 'field_output', 'QC')

    ## check and create folders
    folders = [field_input_dir, field_output_dir, field_QC_fig_dir]
    for folder in folders:
        if not os.path.exists(folder):
            os.makedirs(folder, exist_ok=True)

    ## set up input and output filenames
    field_mag_dir = os.path.join(field_input_dir, f"{freesurfer_sub_id}_{ses_id}_magnitude1.nii.gz")
    field_pha_dir = os.path.join(field_input_dir, f"{freesurfer_sub_id}_{ses_id}_phasediff.nii.gz")
    field_brain1_dir = os.path.join(field_output_dir, 'fmap_mag_brain1.nii.gz')
    field_brain_dir = os.path.join(field_output_dir, 'fmap_mag_brain.nii.gz')
    field_fmap_dir = os.path.join(field_output_dir, 'fmap_rads.nii.gz')

    return {
        'field_input_dir': field_input_dir,
        'field_output_dir': field_output_dir,
        'field_QC_fig_dir': field_QC_fig_dir,
        'field_mag_dir': field_mag_dir,
        'field_pha_dir': field_pha_dir,
        'field_brain1_dir': field_brain1_dir,
        'field_brain_dir': field_brain_dir,
        'field_fmap_dir': field_fmap_dir
    }



def plot_brain_slices(img_path, output_dir, prefix):
    """绘制脑图像的切片并保存"""
    # 定义切片坐标
    x_slices = [-40, -20, 0, 20, 40, 60]
    y_slices = [-60, -40, -20, 0, 20, 40]
    z_slices = [10, 30, 40, 50, 60, 70]

    plot_img = nib.load(img_path)

    # X轴切片
    display_x = plot_anat(
        plot_img,
        display_mode='x',
        cut_coords=x_slices,
        title='X Slices',
        draw_cross=False,
        annotate=True,
        bg_img=None,
        black_bg=False
    )
    display_x.savefig(os.path.join(output_dir, f"{prefix}_x_slices.png"), dpi=500)
    display_x.close()
    print(f"已保存 {prefix} x_slices.png")

    # Y轴切片
    display_y = plot_anat(
        plot_img,
        display_mode='y',
        cut_coords=y_slices,
        title='Y Slices',
        draw_cross=False,
        annotate=True,
        bg_img=None,
        black_bg=False
    )
    display_y.savefig(os.path.join(output_dir, f"{prefix}_y_slices.png"), dpi=500)
    display_y.close()
    print(f"已保存 {prefix} y_slices.png")

    # Z轴切片
    display_z = plot_anat(
        plot_img,
        display_mode='z',
        cut_coords=z_slices,
        title='Z Slices',
        draw_cross=False,
        annotate=True,
        bg_img=None,
        black_bg=False
    )
    display_z.savefig(os.path.join(output_dir, f"{prefix}_z_slices.png"), dpi=500)
    display_z.close()
    print(f"已保存 {prefix} z_slices.png")

def process_fieldmap(ses_id):
    """处理单个session的fieldmap"""
    print(f"\n=== 处理 {ses_id} 的fieldmap ===")

    # 设置目录和文件路径
    paths = setup_field_directories(ses_id)

    print(f"Field magnitude: {paths['field_mag_dir']}")
    print(f"Field phase: {paths['field_pha_dir']}")

    # 检查输入文件是否存在
    if not os.path.exists(paths['field_mag_dir']):
        print(f"警告: {paths['field_mag_dir']} 不存在，跳过此session")
        return
    if not os.path.exists(paths['field_pha_dir']):
        print(f"警告: {paths['field_pha_dir']} 不存在，跳过此session")
        return

    ## 1. Skull-strip the magnitude nifti image
    print("1. 进行magnitude图像的skull-stripping...")
    cmd = f"bet2 {paths['field_mag_dir']} {paths['field_brain1_dir']} -f 0.5"
    os.system(cmd)

    ## 2. Erode the brain image a little
    print("2. 对brain图像进行腐蚀操作...")
    cmd = f"fslmaths {paths['field_brain1_dir']} -ero {paths['field_brain_dir']}"
    os.system(cmd)

    ## 3. Plot brain slices for QC
    print("3. 生成brain图像的QC图片...")
    plot_brain_slices(paths['field_brain_dir'], paths['field_QC_fig_dir'], "field_brain")

    ## 4. Create fmap_rads.nii.gz file
    print("4. 创建fieldmap文件...")
    cmd = f"fsl_prepare_fieldmap SIEMENS {paths['field_pha_dir']} {paths['field_brain_dir']} {paths['field_fmap_dir']} 2.46"
    os.system(cmd)

    ## 5. Plot fieldmap slices for QC
    print("5. 生成fieldmap的QC图片...")
    plot_brain_slices(paths['field_fmap_dir'], paths['field_QC_fig_dir'], "field_fmap")

    print(f"=== {ses_id} fieldmap处理完成 ===\n")

def process_anatomical():
    """处理解剖图像（只运行一次）"""
    print("\n=== 处理解剖图像 ===")

    # 设置目录和文件路径
    paths = setup_anat_directories()

    # 检查输入文件是否存在
    if not os.path.exists(paths['anat_T1_dir']):
        print(f"错误: {paths['anat_T1_dir']} 不存在")
        return

    ## 1. Skull-strip the T1 image
    print("1. 进行T1图像的skull-stripping...")
    cmd = f"bet2 {paths['anat_T1_dir']} {paths['anat_brain_dir']}"
    os.system(cmd)

    ## 2. Segment the T1 brain into gm, wm, and csf
    print("2. 对T1 brain进行分割（灰质、白质、脑脊液）...")
    cmd = f"fast -B -I 10 -l 10 {paths['anat_brain_dir']}"
    os.system(cmd)

    ## 3. Create a binary white-matter map
    print("3. 创建二值化白质图...")
    cmd = f"fslmaths {paths['anat_pve2_dir']} -thr 0.5 -bin {paths['anat_wmseg_dir']}"
    os.system(cmd)

    ## 4. Create a binary gray-matter map
    print("4. 创建二值化灰质图...")
    cmd = f"fslmaths {paths['anat_pve1_dir']} -thr 0.4 -bin {paths['anat_gm_mask_dir']}"
    os.system(cmd)

    print("=== 解剖图像处理完成 ===\n")

def main():
    """主函数：协调整个处理流程"""
    print(f"开始处理 {freesurfer_sub_id} 的数据")
    print(f"Session数量: {ses_len}")

    # 1. 处理解剖图像（只运行一次）
    process_anatomical()

    # 2. 循环处理每个session的fieldmap
    for i in range(1, ses_len + 1):
        ses_id = f"ses-{i:02d}"  # 格式化为 ses-01, ses-02, ..., ses-39
        process_fieldmap(ses_id)

    print("所有处理完成！")

if __name__ == "__main__":
    main()

