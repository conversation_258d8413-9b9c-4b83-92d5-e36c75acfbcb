import numpy as np
import matplotlib.pyplot as plt
import nibabel as nib
import os
import sys
from os.path import join, exists, split
import warnings
from regex import R
from tqdm import tqdm
from pprint import pprint
warnings.filterwarnings('ignore')
import matplotlib
from glmsingle.glmsingle import GLM_single
from nilearn.plotting import plot_design_matrix, plot_roi
from nilearn import plotting
from ipywidgets import interact

# 添加 rootfolder 到 sys.path
# os.getcwd()获取当前 Notebook 的工作目录（一般是打开 Notebook 时所在目录）
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), "..")))
print(os.path.join(os.path.dirname(os.getcwd())))
print(os.path.abspath(os.path.join(os.getcwd(), "..",'preprocess')))
# 然后就可以正常导入
from preprocess.functions import openObject, saveObject


# 添加 FSL bin 路径到环境变量，便于调用fsl命令
os.environ["FSLDIR"] = "/usr/local/fsl"
os.environ["PATH"] += os.pathsep + "/usr/local/fsl/bin"
# 设置 FreeSurfer 安装目录，便于调用fsl命令
os.environ["FREESURFER_HOME"] = "/usr/local/freesurfer"
os.environ["PATH"] += os.pathsep + os.path.join(os.environ["FREESURFER_HOME"], "bin")

datatype = 'our'
if datatype == 'nsd':
    base_dir = '/opt/data/private/lq/data/NSD/nsddata_rawdata'
    preproc_dir = '/opt/data/private/lq/data/NSD/nsddata_preprocdata/'
    designmatrix_dir = '/opt/data/private/lq/data/NSD/nsddata/experiments/nsd/' 
    nsd_ref_beta_dir = '/opt/data/private/lq/data/NSD/nsddata_betas/ppdata'

    os.environ["SUBJECTS_DIR"] = "/opt/data/private/lq/data/NSD/nsddata/freesurfer/subjects"
    Results_dir = "/opt/data/private/lq/data/NSD/nsddata_betas/our/"

    TR_s = 1.6 # nsd: 1.6; our data: 1.5
    tr_target = 1.0
    Effective_echo_spacing = 0.000329994 # nsd: 0.000329994; our data: 0.00027500
    origvoxelSize = np.array([1.8, 1.8, 1.8])


elif datatype == 'our':
    os.environ["SUBJECTS_DIR"] = "/usr/local/freesurfer/subjects" # for 20, "/share/home/<USER>/software/freesurfer/subjects" for 42
    Results_dir = "/opt/data/private/lq/data/our_nsd/ROI_indices"
    os.makedirs(Results_dir, exist_ok=True)
    TR_s = 1.5
    Effective_echo_spacing = 0.000275003
    origvoxelSize = np.array([2.0, 2.0, 2.0])


## initiate data id number
freeSurfer_subID_list = ['sub-01', 'sub-02','sub-03','sub-04']
# freeSurfer_subID_list = ['sub-02']
ROI_fs_mask_dir = {}
for hem in ['lh', 'rh']:
    ROI_fs_mask_dir[hem] = join(os.environ["SUBJECTS_DIR"], "fsaverage", "label", f"{hem}.nsdgeneral.mgz")

for i in range(len(freeSurfer_subID_list)):
    freesurfer_sub_id = freeSurfer_subID_list[i]
    freesurfer_sub_id_reconname = freesurfer_sub_id.replace("sub-", "subj") if datatype == 'nsd' else freesurfer_sub_id.replace("sub-", "sub")

    ## Load nsdgeneral ROI mask
    ROI_surf_mask_dir = {}
    ROI_surf_binary_mask_dir = {}
    data_surf_mask = {}
    nsdgeneral_vert_indices = {}
    for hem in ['lh', 'rh']:
        ROI_surf_mask_dir[hem] = join(os.environ["SUBJECTS_DIR"], freesurfer_sub_id_reconname, "label", f"{hem}.nsdgeneral.orig.mgz")
        ROI_surf_binary_mask_dir[hem] = join(os.environ["SUBJECTS_DIR"], freesurfer_sub_id_reconname, "label", f"{hem}.nsdgeneral.mgz")
    
        cmd = f"mri_surf2surf --sval {ROI_fs_mask_dir[hem]} \
                --srcsubject fsaverage \
                --trgsubject {freesurfer_sub_id_reconname} \
                --hemi {hem} --tval {ROI_surf_mask_dir[hem]} \
                --cortex"
        os.system(cmd)

        cmd = f"mri_binarize --i {ROI_surf_mask_dir[hem]} \
             --o {ROI_surf_binary_mask_dir[hem]} \
             --min 0.49999 \
             --binval 1"
        os.system(cmd)


        tmp_data = nib.load(ROI_surf_mask_dir[hem]).get_fdata().squeeze()
        print(f'============= Finishing mapping fs2nativesurface for {freesurfer_sub_id_reconname} =============')
        print(f"Loaded ROI mask from {ROI_surf_mask_dir[hem]} with shape {tmp_data.shape}.")
        print(np.unique(tmp_data))
        del tmp_data

        data_surf_mask[hem] = nib.load(ROI_surf_binary_mask_dir[hem]).get_fdata().squeeze()
        print(f"Loaded ROI binary mask from {ROI_surf_binary_mask_dir[hem]} with shape {data_surf_mask[hem].shape}.")
        print(np.unique(data_surf_mask[hem]))

        nsdgeneral_vert_indices[hem] = np.where(data_surf_mask[hem]>0)[0]
        print(f"{len(nsdgeneral_vert_indices[hem])} vertices in {hem}")

    saveObject(join(Results_dir, f'{freesurfer_sub_id}_nsdgeneral_vert_indices.pkl'), nsdgeneral_vert_indices)
