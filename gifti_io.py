import numpy as np
import nibabel as nib
from scipy.ndimage import zoom
import subprocess

# Function to read in NIfTI file as np.ndarray
def read_nifti(nifti_fn):
    nii = nib.load(nifti_fn)
    data = nii.get_fdata()
    return data

# Function to write np.ndarray to NIfTI file
def write_nifti(data, output_fn, template_fn):
    nii = nib.load(template_fn)
    new_img = nib.Nifti1Image(data, nii.affine)
    nib.save(new_img, output_fn)

# Function to create binary mask by setting thres
def create_binary_mask(input_fn, output_fn, thre=0.9):
    binary_data = read_nifti(input_fn)
    binary_data[binary_data>thre]=1
    binary_data[binary_data<=thre]=0
    write_nifti(binary_data, output_fn, input_fn)
    
    
# Function to read in GIfTI file as np.ndarray
def read_gifti(gifti_fn):
    gii = nib.load(gifti_fn)
    data = np.vstack([da.data[np.newaxis, :]
                      for da in gii.darrays]) 
    return data


# Function to write np.ndarray to GIfTI file
def write_gifti(data, output_fn, template_fn):
    gii = nib.load(template_fn)
    for i in np.arange(gii.numDA):
        gii.remove_gifti_data_array(0)
    gda = nib.gifti.GiftiDataArray(data)
    gii.add_gifti_data_array(gda)
    nib.gifti.giftiio.write(gii, output_fn)
