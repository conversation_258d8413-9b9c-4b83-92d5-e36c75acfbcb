#!/bin/bash

# NSD Beta投影到fsaverage5皮层 - 批处理脚本
# 作者: 基于projections.py扩展
# 日期: $(date)

# 设置脚本在遇到错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "NSD Beta投影到fsaverage5皮层 - 批处理脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -t, --test     测试模式：只处理每个被试的第一个session"
    echo "  -d, --dry-run  干运行：只显示会处理什么，不实际执行"
    echo "  -c, --check    检查环境和数据完整性"
    echo "  -f, --force    强制重新处理（不跳过已存在的文件）"
    echo "  -w, --workers N 指定并行工作进程数"
    echo ""
    echo "示例:"
    echo "  $0 --check           # 检查环境"
    echo "  $0 --dry-run         # 查看处理计划"
    echo "  $0 --test            # 测试模式"
    echo "  $0 --workers 4       # 使用4个并行进程"
    echo "  $0                   # 完整处理"
}

# 检查Python环境
check_python() {
    print_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        print_error "Python3未找到，请安装Python3"
        exit 1
    fi
    
    python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    print_success "Python版本: $python_version"
    
    # 检查必要的Python包
    print_info "检查Python包依赖..."
    required_packages=("numpy" "scipy" "h5py" "nibabel")
    
    for package in "${required_packages[@]}"; do
        if python3 -c "import $package" 2>/dev/null; then
            print_success "✓ $package"
        else
            print_error "✗ $package 未安装"
            print_info "请运行: pip install $package"
            exit 1
        fi
    done
}

# 检查FreeSurfer环境
check_freesurfer() {
    print_info "检查FreeSurfer环境..."
    
    if [ -z "$FREESURFER_HOME" ]; then
        print_error "FREESURFER_HOME环境变量未设置"
        exit 1
    fi
    
    if [ ! -d "$FREESURFER_HOME" ]; then
        print_error "FreeSurfer目录不存在: $FREESURFER_HOME"
        exit 1
    fi
    
    print_success "FreeSurfer目录: $FREESURFER_HOME"
    
    # 检查关键命令
    commands=("mri_surf2surf" "mris_fwhm")
    for cmd in "${commands[@]}"; do
        if command -v $cmd &> /dev/null; then
            print_success "✓ $cmd"
        else
            print_error "✗ $cmd 命令未找到"
            print_info "请检查FreeSurfer安装和PATH设置"
            exit 1
        fi
    done
}

# 检查数据目录
check_data() {
    print_info "检查数据目录..."
    
    # 这里需要根据实际的数据路径进行调整
    data_dir="/opt/data/private/lq/data/NSD/nsddata_betas/ppdata"
    
    if [ ! -d "$data_dir" ]; then
        print_error "数据目录不存在: $data_dir"
        print_info "请检查数据路径设置"
        exit 1
    fi
    
    print_success "数据目录: $data_dir"
    
    # 检查是否有被试数据
    subject_count=$(find "$data_dir" -maxdepth 1 -name "subj*" -type d | wc -l)
    print_info "找到 $subject_count 个被试目录"
}

# 运行环境检查
run_check() {
    print_info "开始环境检查..."
    echo "=================================="
    
    check_python
    echo ""
    check_freesurfer
    echo ""
    check_data
    echo ""
    
    print_success "环境检查完成！"
    
    # 运行Python测试脚本
    if [ -f "projections_test.py" ]; then
        print_info "运行数据完整性测试..."
        python3 projections_test.py
    else
        print_warning "projections_test.py 未找到，跳过数据测试"
    fi
}

# 主函数
main() {
    # 默认参数
    TEST_MODE=false
    DRY_RUN=false
    CHECK_ONLY=false
    FORCE_MODE=false
    WORKERS=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -t|--test)
                TEST_MODE=true
                shift
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -c|--check)
                CHECK_ONLY=true
                shift
                ;;
            -f|--force)
                FORCE_MODE=true
                shift
                ;;
            -w|--workers)
                WORKERS="--workers $2"
                shift 2
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示标题
    echo "========================================"
    echo "  NSD Beta投影到fsaverage5皮层"
    echo "========================================"
    echo ""
    
    # 如果只是检查环境
    if [ "$CHECK_ONLY" = true ]; then
        run_check
        exit 0
    fi
    
    # 检查脚本文件是否存在
    if [ ! -f "projections_parallel.py" ]; then
        print_error "projections_parallel.py 未找到"
        print_info "请确保在正确的目录中运行此脚本"
        exit 1
    fi
    
    # 构建Python命令
    python_cmd="python3 projections_parallel.py"
    
    if [ "$TEST_MODE" = true ]; then
        python_cmd="$python_cmd --test"
        print_info "运行模式: 测试模式（只处理第一个session）"
    fi
    
    if [ "$DRY_RUN" = true ]; then
        python_cmd="$python_cmd --dry-run"
        print_info "运行模式: 干运行（不实际处理）"
    fi
    
    if [ "$FORCE_MODE" = true ]; then
        python_cmd="$python_cmd --no-skip"
        print_info "运行模式: 强制模式（重新处理所有文件）"
    fi
    
    if [ -n "$WORKERS" ]; then
        python_cmd="$python_cmd $WORKERS"
        print_info "并行设置: $WORKERS"
    fi
    
    print_info "执行命令: $python_cmd"
    echo ""
    
    # 执行Python脚本
    if eval $python_cmd; then
        print_success "处理完成！"
    else
        print_error "处理过程中出现错误"
        exit 1
    fi
}

# 运行主函数
main "$@"
