import numpy as np
import scipy
import scipy.io as sio
import matplotlib.pyplot as plt
import os
import sys
import re
import glob
from os.path import join, exists, split
import h5py
import nibabel as nib
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp
from functools import partial
import pickle

# 添加 rootfolder 到 sys.path
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), "..")))
print(os.path.join(os.path.dirname(os.getcwd())))
print(os.path.abspath(os.path.join(os.getcwd(), "..",'preprocess')))

# 简单的对象保存和加载函数，替代preprocess.functions
def saveObject(obj, filename):
    """保存对象到文件"""
    with open(filename, 'wb') as f:
        pickle.dump(obj, f)

def openObject(filename):
    """从文件加载对象"""
    with open(filename, 'rb') as f:
        return pickle.load(f)

# 添加 FSL bin 路径到环境变量，便于调用fsl命令
os.environ["FSLDIR"] = "/usr/local/fsl"
os.environ["PATH"] += os.pathsep + "/usr/local/fsl/bin"
# 设置 FreeSurfer 安装目录，便于调用fsl命令
os.environ["FREESURFER_HOME"] = "/usr/local/freesurfer"
os.environ["PATH"] += os.pathsep + os.path.join(os.environ["FREESURFER_HOME"], "bin")
os.environ["SUBJECTS_DIR"] = "/opt/data/private/lq/data/NSD/nsddata/freesurfer"

base_dir = '/opt/data/private/lq/data/NSD/nsddata_rawdata'
preproc_dir = '/opt/data/private/lq/data/NSD/nsddata_preprocdata/'
designmatrix_dir = '/opt/data/private/lq/data/NSD/nsddata/experiments/nsd/' 
nsd_ref_beta_dir = '/opt/data/private/lq/data/NSD/nsddata_betas/ppdata'
TR_s = 1.6 # nsd: 1.6; our data: 1.5
tr_target = 1.0
datatype = 'nsd'

freeSurfer_subID_list = ['sub-01']
sesID_list = ['ses-nsd01']

for i in range(len(freeSurfer_subID_list)):
    freesurfer_sub_id = freeSurfer_subID_list[i]
    freesurfer_sub_id_reconname = freesurfer_sub_id.replace("sub-", "subj") if datatype == 'nsd' else freesurfer_sub_id.copy()
    nativesurf_inputdir = join(nsd_ref_beta_dir, freesurfer_sub_id_reconname, "nativesurface", "betas_fithrf_GLMdenoise_RR")
    fs5_outputdir = join(nsd_ref_beta_dir, freesurfer_sub_id_reconname, "fsaverage5", "betas_fithrf_GLMdenoise_RR")
    os.makedirs(fs5_outputdir, exist_ok=True)

    # get voxel of orignial nativesurface nsd
    nsd_surf_data_dir = {'lh': join(nativesurf_inputdir, "lh.betas_session01.hdf5"),
                         'rh': join(nativesurf_inputdir, "rh.betas_session01.hdf5"),
                         }
    nsd_surf_dir = {'lh': join(nativesurf_inputdir, "lh.betas_session01.nii.gz"),
                     'rh': join(nativesurf_inputdir, "rh.betas_session01.nii.gz"),
                     }

    nsd_fs5_dir = {'lh': join(fs5_outputdir, "lh.betas_session01.fs5.nii.gz"),
                   'rh': join(fs5_outputdir, "rh.betas_session01.fs5.nii.gz"),
                   }
    nsd_fs5_sm_dir = {'lh': join(fs5_outputdir, "lh.betas_session01.fs5.sm.nii.gz"),
                      'rh': join(fs5_outputdir, "rh.betas_session01.fs5.sm.nii.gz"),
                      }

    ref_data = {}
    for hem in ['lh', 'rh']:
        with h5py.File(nsd_surf_data_dir[hem], "r") as f:
            # 查看文件中所有主键（数据集或组）
            print("Top-level keys:", list(f.keys()))

            # 假设你想读取其中一个数据集，例如 "betas"
            ref_data[hem] = f["betas"][:]   # 读取数据为 numpy 数组
            print('original shape of nsd', ref_data[hem].shape)
            img_data = np.zeros((ref_data[hem].shape[-1], 1, 1, ref_data[hem].shape[0]))
            img_data[:,0,0] = ref_data[hem].T.copy()
            vol_img = nib.Nifti1Image(img_data, affine=np.eye(4))
            nib.save(vol_img, nsd_surf_dir[hem])

    for hem in ['lh', 'rh']:
        # Convert to fsaverage-space
        ## ---------------- lh ------------------
        cmd = f"mri_surf2surf --sval {nsd_surf_dir[hem]} \
            --srcsubject {freesurfer_sub_id_reconname} \
            --trgsubject fsaverage5 \
            --hemi {hem} --tval {nsd_fs5_dir[hem]} \
            --cortex"
        os.system(cmd)
        # smooth
        cmd = f"mris_fwhm --s fsaverage5 \
            --hemi {hem} --smooth-only \
            --i {nsd_fs5_dir[hem]} \
            --fwhm 4 \
            --o {nsd_fs5_sm_dir[hem]} \
            --cortex"
        os.system(cmd)
