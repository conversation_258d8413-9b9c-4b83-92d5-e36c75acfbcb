{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# fMRI Processing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Preparations\n", "\n", "initial setups"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import datetime\n", "import numpy as np\n", "import nibabel as nib\n", "import scipy\n", "import scipy.signal as ss\n", "import scipy.io as io\n", "from nilearn import image\n", "from ipywidgets import interact\n", "from matplotlib import pyplot as plt\n", "%matplotlib inline\n", "\n", "from glob import glob\n", "from functions import find_files_with_string\n", "import pandas as pd\n", "from nilearn.image import resample_to_img\n", "from nilearn.glm import threshold_stats_img\n", "from nilearn.glm.first_level import make_first_level_design_matrix, FirstLevelModel\n", "from nilearn.plotting import plot_anat, plot_roi\n", "from nilearn import plotting\n", "\n", "# 添加 FSL bin 路径到环境变量，便于调用fsl命令\n", "os.environ[\"FSLDIR\"] = \"/share/home/<USER>/software/fsl/\"\n", "os.environ[\"PATH\"] += os.pathsep + \"/share/home/<USER>/software/fsl/bin\"\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["## initiate data id number\n", "base_dir = '/share/home/<USER>/data/our_nsd/bids'\n", "freesurfer_sub_id = 'sub-04'\n", "ses_id = 'ses-01'\n", "run_id_func = 'run-01'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Move all mricron output files to ./nii_output before run next cell.\n", "Do not use to run the next cell if mricron does dicom2nii.gz"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["## set up input folders\n", "anat_input_dir = os.path.join(base_dir,freesurfer_sub_id, 'ses-01', 'anat')\n", "field_input_dir = os.path.join(base_dir, freesurfer_sub_id, ses_id, 'fmap')\n", "func_input_dir = os.path.join(base_dir,freesurfer_sub_id, ses_id, 'func')\n", "\n", "## set up output folders\n", "field_output_dir = os.path.join(field_input_dir, 'field_output')\n", "field_QC_fig_dir = os.path.join(field_input_dir, 'field_output', 'QC')\n", "anat_output_dir = os.path.join(anat_input_dir, 'anat_output')\n", "reg_output_dir = os.path.join(anat_input_dir, 'reg_output')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["## check and create folders\n", "folders = [anat_input_dir, \n", "           field_input_dir, \n", "           field_output_dir, \n", "           field_QC_fig_dir,\n", "           anat_output_dir,\n", "           reg_output_dir\n", "           ]\n", "for i in folders:\n", "    path_tmp = os.path.join(os.getcwd(), i)\n", "    if not os.path.exists(path_tmp):\n", "        os.makedirs(path_tmp, exist_ok=True)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/share/home/<USER>/data/our_nsd/bids/sub-04/ses-01/fmap/sub-04_ses-01_magnitude1.nii.gz\n", "/share/home/<USER>/data/our_nsd/bids/sub-04/ses-01/fmap/sub-04_ses-01_phasediff.nii.gz\n", "/share/home/<USER>/data/our_nsd/bids/sub-04/ses-01/func/sub-04_ses-01_run-01_bold.nii.gz\n"]}], "source": ["## set up input filenames\n", "anat_T1_dir = os.path.join(anat_input_dir, f\"{freesurfer_sub_id}_{ses_id}_T1w.nii.gz\")\n", "field_mag_dir = {}\n", "field_pha_dir = {}  \n", "\n", "field_mag_dir = os.path.join(field_input_dir, f\"{freesurfer_sub_id}_{ses_id}_magnitude1.nii.gz\")\n", "field_pha_dir = os.path.join(field_input_dir, f\"{freesurfer_sub_id}_{ses_id}_phasediff.nii.gz\")\n", "print(field_mag_dir)\n", "print(field_pha_dir)\n", "\n", "func_2d_dir = os.path.join(func_input_dir, f\"{freesurfer_sub_id}_{ses_id}_{run_id_func}_bold.nii.gz\")\n", "print(func_2d_dir)\n", "func_stc_mc_fm_1vol_dir = os.path.join(func_input_dir, 'task1', 'f_skip_stc_mc_fm_1vol.nii.gz')\n", "# ref downsampled_registered_func filename\n", "func_simptask_dir = os.path.join(func_input_dir, 'output_dir_simptask', 'f_simptask.nii.gz')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["## set up output filenames\n", "field_brain1_dir = os.path.join(field_input_dir, 'field_output', 'fmap_mag_brain1.nii.gz')\n", "field_brain_dir = os.path.join(field_input_dir, 'field_output',  'fmap_mag_brain.nii.gz')\n", "field_fmap_dir = os.path.join(field_input_dir, 'field_output',  'fmap_rads.nii.gz')\n", "\n", "\n", "anat_brain_dir = os.path.join(anat_output_dir,  'T1_brain.nii.gz')\n", "anat_wmseg_dir = os.path.join(anat_output_dir,  'T1_wmseg.nii.gz')\n", "anat_pve0_dir = os.path.join(anat_output_dir, 'T1_brain_pve_0.nii.gz')\n", "anat_pve1_dir = os.path.join(anat_output_dir,  'T1_brain_pve_1.nii.gz')\n", "anat_pve2_dir = os.path.join(anat_output_dir,  'T1_brain_pve_2.nii.gz')\n", "\n", "singlevol_dir = os.path.join(func_input_dir, f\"{freesurfer_sub_id}_{ses_id}_task-nsdcore_{run_id_func}_bold_singlevol.nii.gz\")\n", "tcustom_dir = os.path.join(base_dir, 'tcustom_epi.txt')\n", "mask_global_dir = os.path.join(anat_output_dir, 'global_mask.nii.gz')\n", "anat_gm_mask_dir = os.path.join(anat_output_dir, \"T1_gm_mask.nii.gz\")\n", "register_dir = os.path.join(reg_output_dir, 'register.dof6.dat')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# USE this to check the alignment between func_2d and fieldmap, with different parameters though.\n", "# cmd = f\"fslroi {func_2d_dir} {singlevol_dir} 0 1\"\n", "# os.system(cmd)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Visualize Images as a slider\n", "def show_slice_0(i):\n", "    plt.subplot(131)\n", "    plt.imshow(array[i,:,:], cmap='gray')\n", "    plt.show()\n", "def show_slice_1(j):\n", "    plt.subplot(132)\n", "    plt.imshow(array[:,j,:], cmap='gray')\n", "    plt.show()\n", "def show_slice_2(k):\n", "    plt.subplot(133)\n", "    plt.imshow(array[:,:,k], cmap='gray')\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Remove dummy volumes\n", "\n", "Don't have to do this in Siemens Prisma data. Already removed before exporting from the machine. Check if other machines' data is used here."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Skull-stripping and fieldmap preparations\n", "\n", "Stop and check after each command. Slightly adjust the cmd params and re-run the cmd if temporary images are not appropriately displayed."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Preparations:\n", "\n", "1. 3 field map images: 2 magnitude images and 1 phase images\n", "\n", "2. Generate fmap_rads.nii.gz: "]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["## Skull-strip the magnitude nifti image\n", "cmd = f\"bet2 {field_mag_dir} {field_brain1_dir} -f 0.5\"\n", "os.system(cmd)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["'/share/home/<USER>/data/our_nsd/bids/sub-04/ses-01/fmap/field_output/fmap_mag_brain1.nii.gz'"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["field_brain1_dir"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2ed9ff1d30164f30a5af87f0394a37ae", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=52, description='i', max=105), Output()), _dom_classes=('widget-interact…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "34548bec13c34a7b8fc99a8978c9b876", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=52, description='j', max=105), Output()), _dom_classes=('widget-interact…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d7d8a7265025401594e169c38819b3d4", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=34, description='k', max=68), Output()), _dom_classes=('widget-interact'…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.show_slice_2(k)>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the Nifti File\n", "res_img = nib.load(field_brain1_dir)\n", "array = res_img.get_fdata()\n", "\n", "interact(show_slice_0, i=(0, array.shape[0]-1))\n", "interact(show_slice_1, j=(0, array.shape[1]-1))\n", "\n", "interact(show_slice_2, k=(0, array.shape[2]-1))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["## Erode (or dilate) the brain image a little \n", "## if the previous 'bet2 -f' cannot give you a better result\n", "cmd = f\"fslmaths {field_brain1_dir} -ero {field_brain_dir}\"\n", "os.system(cmd)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "91ad89663f6b467282e9167a5361e349", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=52, description='i', max=105), Output()), _dom_classes=('widget-interact…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7bc1035b9ae945469f5f3d155c9c8ab0", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=52, description='j', max=105), Output()), _dom_classes=('widget-interact…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7ca97900eb0941a58a99351a55d39010", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=34, description='k', max=68), Output()), _dom_classes=('widget-interact'…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.show_slice_2(k)>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the Nifti File\n", "res_img = nib.load(field_brain_dir)\n", "array = res_img.get_fdata()\n", "\n", "interact(show_slice_0, i=(0, array.shape[0]-1))\n", "interact(show_slice_1, j=(0, array.shape[1]-1))\n", "interact(show_slice_2, k=(0, array.shape[2]-1))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["已保存 x_slices.png\n", "已保存 y_slices.png\n", "已保存 z_slices.png\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1630x230 with 8 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1390x300 with 8 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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********************************************+jr3FrSTf6WvMvJ7fN41bNfa34+ehv3FrCrZr7G32NW0u6VXNfaym3rl27ZlmWZVOmTMleffXVem9TpkzJsizLunbtul7zavP//wcAAAAAAAAA0IS6du2qlStXasqUKfroo4/qfW3btm210047qVu3blq1alWj59XiSkMAAAAAAAAAwOYka4LSEBwIBgAAAAAAAIBmxIFgAAAAAAAAAKhyHAgGAAAAAAAAgCrHgWAAAAAAAAAAqHIcCAYAAAAAAACAKseBYAAAAAAAAACock1xILjtBr0bAAAAAAAAANDikQgGAAAAAAAAgGZEaQgAAAAAAAAAqHIcCAYAAAAAAACAKseBYAAAAAAbTdu29Q8R8tFHH5V9vE2bNiX/VpKaDgAAAMrjQDAAAAAAAAAAbAY29EBvJRwIBgAAAKqMk79O8PqPikpJXb/Pr0/921DlEsSex4cfftioaQGbk5jeb2xfBgC0PiSCAQAAAAAAAKDKcSAYAAAAQFkxIVgU04Jdu3aVJO21116SpF69ekmSlixZIilP5z711FNl5zVkyBBJUvfu3SVJgwcPliQtXbpUkrTFFluUzHfq1KmSpNWrV9eZ1rp160rub7XVVpKk999/P/l5gGrj/huTv+6LlRK/W265ZYNe19DXAAA2DxwIBgAAAAAAAIBmRCIYAAAAgKQ8devEoP8Q6NatmySpc+fOta/dfvvtJeXp26FDh0qSBg4cWPK4769cuVKStGrVKkl50nfZsmWSpOHDh0vKk8QdO3aUlCeDnW70sg0YMKBkPpLUqVMnSdKCBQskSTNnzpQkzZ07t0GfF2gp6qt97X4Zk772wQcflLwutu/27dtLknr27ClJ6t27t6S8/7z33nuSpIkTJzZ4eWN6mIQwALRMTXEguPzWCQAAAAAAAAA2sgsvvFDPPvusVq5cqYULF+quu+7SDjvsUO97dt55Z915552aPXu2sizTeeed1yTLevzxx2vKlClau3atXnrpJR155JElz19yySWaMmWKVq9erWXLlunhhx/WPvvss17z8oHgSrcNQSIYAAAAaEGc3ouJwZi2dW3dUaNGSZI6dOhQOw2nB538c4pw8eLFkvK0YqwRvOuuu0qS5syZI0nq0qVLyTK9++67Zafr9K7r/Pq+axMXl3e33XaTpNo/+F566aWSZfHrJk+eLElq165dyTxJCKOpuQ26TzrVWxTbZSp16743aNAgSdJOO+0kKU/Kb7311iXz9PM9evQoma77ifua+6okPffccyWvXbFiRckyxJre1OUG0NQOOuggXXPNNXruuee05ZZb6rLLLtNDDz2knXfeWWvWrCn7nk6dOmnWrFm64447dOWVV2605bjppptqr5yKxowZo9tuu00XXXSR7r//fo0bN05333239txzT7366quSpOnTp+vcc8/VrFmz1LFjR51//vl66KGHNGzYsNp9m4YiEQwAAAAAAACgahx55JG6+eabNXnyZL300ks69dRTNXjw4NpBbct5/vnn9e1vf1u///3va09MR23atNGFF16oWbNmac2aNZo0aZKOO+649V7O8847T3/+8591+eWXa+rUqfr3f/93TZw4Ueeee27ta2677TY9+uijmj17tiZPnqwLLrhA3bt31+67797o+ZEIBgAAADYTTgrGtOGOO+4oKU8cjhw5UlKeHFy9erWk0gTi22+/XfIep4WdIozzcELQKRzXQO3evXvJ8xbTjvF5J4OLPM9FixZJymuhjhkzRlJeT9jTcu3jZ555pmQ6/p6oc4pNxW3M/SC252J7dx/bb7/9Sl7zzjvvlLw2Tsv1tffcc09JeVrf73Nfdbrff/h7er402f3GrytOw2Jd4VT/J20PoLl4f8NjE6yviy66SCeddJLOOusszZgxQwceeKBuvfVWLV68WE8++WSjpzdmzBhdccUVJY89+OCDOuaYY8q+vl27djrzzDO1YsUKvfjii42eH4PFAQAAAAAAAKhKbdq00c9+9jM99dRTteUW1sdWW22liy++WIcffnjtSeTZs2dr//3311e+8pX1OhDcr18/LVy4sOSxhQsXql+/fiWPjR07Vrfffrs6deqk+fPn64gjjqgdeLcxOBAMAAAAVLmYcHWy0Km9/v37S8prhjopuHLlypL3F/8wcE1f87Scwo11h/2vpxU5UexkoV/ndKPn5+nE+sbF9/hfpxD9h5Kn5c934IEHSsrrDPt9Dz74YMmyed7l6rYCjZFKm3vQH7f/YkLY7fjQQw+VlLdD90/3244dO0rKE7xOxjsx1rlzZ0l5X3PCN5XS9XTc/j0/STr88MMl5VcL+F/XqnSf8rxj/ydtD6ApXXPNNdp11121//77b9B0hg0bps6dO+vhhx8ueXyrrbbSCy+8UHt/1apVtf/fYost1L59+5LHbr31Vp199tmNmvdjjz2mkSNHqqamRmeccYb+8Ic/aN9996292qOhOBAMAAAAAAAAoOpcffXVOuqoo3TggQdq3rx5GzQtD5o5duzYOtMq1hR2iS1J2nffffXjH/9YBx98cO1jxRNrCxYsUN++fUum1bdv39oBPm3NmjWaOXOmZs6cqb///e+aPn26vvzlL+tHP/rRBn2mTYEDwQAAAAAAAACazNVXX63Pfe5zOvjggzVnzpwNnt7kyZO1bt06bbvttvWWgZg5c2bt/7fZZht98MEHJY8VTZgwQYcddpiuuuqq2seOOOIITZgwod5ladu2be1VWI1BIhgAAACoMr6U3Jdf+9LxT33qU5LyUhA2d+5cSXnJBPNl3L5c3YNRFaftPxb8Wl8C7mSMn48lHnzfyxZLRsRLxz1dP15cliguSxyoyp/TNflGjBghKS9r4Uvm//KXv0jKB8ZjwCs0ltup/3X7HTJkiKS8XMPHP/5xSVKPHj1KXld8r0s1uP35MmPf9+CH7lMuz+DkWc+ePUvm6XnE0i+en/tJLMsi5X1i7dq1JdPyZdeepl/naU2fPr3sPDf0oAMARNdcc43GjRuno48+WqtWrapN3b799tu169Obb75Z8+bN08UXXyzpn+vPnXfeWdI/11sDBw7UHnvsodWrV2vmzJlavXq1Lr/8cl155ZVq27atnnrqKXXv3l377befVq5cqVtuuaXRy3nVVVfpiSee0AUXXKAHHnhAJ5xwgkaPHq0zzzxT0j/3Sb773e/q3nvv1fz581VTU6OvfvWrGjhwoO64445Gz48DwQAAAAAAAACqxjnnnCNJeuKJJ0oeP/XUU3XzzTdLkrbddtuSE28DBgzQpEmTau9/61vf0re+9S09/vjjOuSQQyRJ48eP1+LFi3XRRRdpu+2204oVKzRx4kRddtll67WcEyZM0Lhx4/TDH/5Ql112mWbMmKFjjjmmdlC7Dz/8UCNGjNApp5yimpoaLV26VM8995wOOOAATZ48udHza4oDwW0kcXoPAAAA2MTioGZOth599NGSpO7du0vKd/Bjms/3nSh0ArDcIGmel9ODMU3oRHCchqc9f/58SXlS0Ekdv87pW3NaNyYsi+JgcZZKOvpzx2SkB9NyktIDXr3yyislr2PAK6Sk2ogTv5/85CclScOHDy95/p133pFU+kd4bOtO6LuP+fk4gKO98cYbkqStt95aUp4UtjjAY5xOuQMCcZncX33ptae5/fbbS5KmTp0qSZo4caIk6bXXXiv7ftL2ALBpdO3aVStXrtRf/vKXiuvaLbbYQoceeqi6detWMshdQ5EIBgAAAAAAAIBmRGkIAAAAoJVz+tBpWtcf7devn6R8lGvXxIuJv5gMKY58XUmsFZzi52NC0ulG/+vnY4LY92PquZgcjjWBY9LXYs1TzyN+7o4dO0qSjj/+eEn59+c0I8lgRLFNxPY6atQoSdKOO+4o6Z+jwEt1E/Dl2rWn4X9T/dr1t+O0nMJ3u47LFvuPOf1bX4LM8+zdu7ekvB6xrzLwOmnAgAGSpPvuu09SXqd79erVyWkDrUG8Oib2o7iNZLuB5sKBYAAAAAAAAACochwIBgAAAFo5J4tGjBghSTrooIMkSW+99Zakf46QLeVJQKf34o6+E4Kx9qgVE06xvm5M31ZKO3laTgx6mVIp3bgsnn65WsFOOHpZYtLR84pJ6piU9jIsWbJEkjR27FhJ0qOPPioprxmMzVesbet26cSvE7KusbjvvvtKylO8fp/vu0ZwsS3GmtbmvuF+7Hbtx2Mf69y5c8l0PM9Un421tYvJ+ph2jGlhJ3z9Hk/Lfenkk0+WJN1zzz2S8nXT3/72t5LXAy2V23YqMV+pBmu8UoWEMJrSpl7Htq38EgAAAAAAAABAa0YiGAAANJtyacFyKp0Zj6mmxr4f2JSGDRsmSdp///0l5e3eKbv27dtLyuvwOmHoBGFMNToJG1O3xYRTfc8Vp+1kY0w7xWU0zzvW8bWY9i3WQ439Pc7b74nfQ5x3TFi6jmu3bt0kSUceeWTJ+ydOnChsnmL7dA3cz3zmM5LyOr5O+joR63Su25qnE1PqxdfEhK7/9Wvdrv06J37d/mMi3mL/iPW8/b7iMnl5nT72/R49epRMOyafa2pqJOUJ6c997nOS8r719NNPlywLKUm0NHF7ub5i295Y0wUqoTQEAAAAAAAAAFQ5DgQDAACU4WSgk1P11SMFmttRRx0lSVq+fLmkvJ126tRJUt0auU72pWqCWny+XFLJj/lf9xnPI/Yh69ChQ9nP4unEuqTx8ZiOLD4Wk7++7/SiE9JOK8YklpPEnp7XB/5MCxculCT967/+qyQSwZsjt6nttttOUt7OYyrffdJtzW3IKXOnymPfK9bgjQnemBqOfczt2Klb8zxictjt29xPLKaVi5/P8/Y0PO24rvDn9Pex9dZbS5JWrlwpKf8+Bg8eLEl6/fXXS+ZJMhjNLW6LBg0aJCnvq7GNpq5I8b+vvfZayevL9TOLVwUAG4IDwQAAAAAAAABQ5TgQDAAAqtKGJnc94vsrr7zSqPmR1kBT6t69u6S87qjb4dq1a0teF+v4+t9YCzem+GIKqZh4iq/1czFtGJ83pxn9b+yzMXHoFG+cbvF9/n+q5qkTWrGuqcV5OMXoNKd5unPnzpWU13t1HVhUP7exgw46SJI0fPhwSXla3H3SfTQmW/28a+q6bfpf9yOpbiLdYnLX74lpev8b27cfj30vVf+72F/8fy9TTBXHvufX+fPFedl5550nSfr5z38uSZo1a5aA5hBrcbut9u7dW5K07777SsrrXsfEfdeuXSXlaXdfoeN1wtVXX10yXW9H5s+fv0k+D2AcCAYAAAAAAACAKseBYAAAUJUauwMTE1GuWxgfJ/GLlmT06NGS8iSqk0ix/m6sVeh2HROG5teVSwJbrNEbU4PxPU4IxnqnTt3G+ohO4cY+6ORhuRrBsbZiXLb4uf29xHl7GTt27CgpT1i7FqS/Zycsd9llF0nSs88+K1S3WE/atW5d0zamzGM7dttyu3dbjNuW4v1Un4p9KT7vZU0lG2NaN1Ur26/3Mpdb3rguiQnnOA+/3/Nyn/L3+K1vfUuSdPHFF0uSVqxYUXa+wIYod/WY26z7l9ucE7sf+9jHJEmLFy+WlKf6nQCONem9ffb73Y+OOeYYSXVrfT/22GOSpKVLl9Yuk9PEfo2nTX/A+uBAMAAAAAAAAABUOQ4EAwCAJpdK2W7KOrtdunQpuZ+qIep/XaPNKYzINd9IY6A5edTymKqL6dxYhzem99wfUjWCY4Kw3GMNTc/H+rtOCDo5GVOM5mWLiULfL74mJh9jjdU4yrsT1H7eSWAnuZz8db/3+sSJre22206S9Nxzz9X72dF6eVvhNuYU+KJFiyRJffr0kZRfTWKxHrf/dRuLfdP9oFhL16+JdYZjgtftPNVXnCKMj1vcLsb1RLFWtufhhGLsU348Lmvs7+5bMYX8j3/8Q5J0+OGHS5LuuOOOku+JPob1EdtPuXYUk/b2xS9+UVK+7fK/s2fPllS3Br3XDe4Drg3s7ciQIUNKXu91woEHHihJuueee2rn7de8/PLLktJXzBj9A82NA8EAAAAAAAAA0IxIBAMAgGbX2IRPqt5hQ8R0lmu6WUxGOmGVqi369ttvN3h5o8Z+XhIeMLdbJ5fcLt1eYxLWSeGY2ot1OmNNXb8uJgWLj1lME8e6u5GXPdZhjO089h/Pt1x94/i5Y3+OtU79vcRpxiR0TGt269at5P0DBgwou+yoHm4L/q2dVPW2wDVsXQc0pnKdLncb8f2YIrRif/I0Ukn+2I9TSV7Pywl4vy/WAo590c8X27f7WKxZ6nn7aprYh7yMXoZ4JUCsw+3p9e3bV5K0cOHCkunR59AYDWkvXr/369dPUt4G3WaXLVsmSerVq5ek/GqA5cuXl8wjXmGyatWqkvm4Dcd1hGvSH3LIIbWv9bznzZtXsgypz0P/QH04EAwAAAAAAAAAVY4DwQAAYJOLyYRUQjaVYEjtjDQmCbx69eqyj7tmW0xCOaHh1JO5Nqin15BE8Poi0YHI7XH48OGS8hSe00JOErn9+vmY3ovtPZUEjP8WU4qxtqk5AVyscVoUk8N+XUw1x+R/rI8a7xfFmqox8ewajTF96Hn7eSeHYw1HJ7eczHY6K34WtH5uM95WHHXUUZLqthG3NbcNt4HYXuN6Pb4u1sotKleru/i4xXbveTitHJc11iv2Z7KYqC9yH4r90euamHo09yk/788bl8l98zOf+Ywk6b777pOUJ4OBhqhUw744jsSxxx4rSerfv7+kuv3FSeB45Zjb8JIlS0re53l5O+3XxXr4MZnvGsPFaXjZfvWrX5UsQ0wbN8XYG2i9OBAMAAAAAAAAAFWOA8EAAGCT885Ejx49JOW1FDd0ehtDKlU0cODAknk58RETHQsWLCh5X7m0c6Wap1FMcDip4rRWrD3p1IkTaqheTiPG+psxXec0nttGrIVrfjwmf/2+1OjpxXmlUopRKrUY65fGmqExJRXnX+T3xKRW7DNxWv7+UlctxLqm7muuJen3f/rTn5Yk3XvvvXWWDa2T28SYMWMk5VeB1NTUSJJWrlwpqW6i1W0vlbKN9btjfyq2wVT97Xg/Jvfd3t1u/Xhs7+5zsU/GK2KK9b5j6j7W547viXXKI6fq4zJ4++fE47hx4yRJ1113naT01T7YvMUkerxKI24bDj300Nrn/Fr35dhHPc14RY3brF/nPu8awnFfLu7rxf5ZvCrAfdjvOfLIIyXl65+HH364ZFreR9iUV62h9eJAMAAAAAAAAABUOQ4EAwCATcY1FW306NGSpGeffVZS3RqCFmuCOgkUk0eVkrVFDd2h8TRj+tbL6iTIzJkzyy5DQ+bT0J0vp0dGjhxZ8riXyTUU/fjf//53SXmdWFSfnj17SsrTc04Jxfq6MX2XSsCm2rvF9F7xeb/Xj9VXs7f4eEzdDho0SFKezk/V903VWC2mFOO84+dJfW5/b34+fpZUDdVYzzWmtorLidbJbWOvvfaSJM2bN09S3SswYiLWbSOm/GJ79r8xsVhsyzFBGOsJpxLBMenr51P1u1P9J8633DTjuib293iVQarGuJdt6623LlkG708sXrxYkrTnnntKkp588smynwWbp1QN7mifffaRlPdLXwUm5VevxZr78Yobt3G/zv0tbhfi1S6pq1xiXypyOtl1vnfccUdJ0ty5cyVJY8eOlVR3PIC77rqrZDrUsUdT4UAwAAAAAAAAADQjEsEAgEaplPgCJKlr166SpP33319S3m5cq2z33XeXlKca3Z5iqs6cvnUCK5UgLiemk+qreSrlOz5OHcV0ckxixnSjEx8bwrUnBw8eLCmvqex5Oq0S013+Xl988cWS6bVv315SXkuu+F60Dk7HuR6mf1OnetwGYso+Jn1j4sivj0nCmGgql1CKbSjOIyaG3Qfdl3baaSdJebt0ijmVVmxoTdLia+K843tiLWU/H2uqxs/oZXSazOn91LKj9erbt68k6Y033pCU90W3W6fFnQp024ptzX031uN2m3EbLFfTNNWXYo3v2H/9vFOEMUmcSvx7mWK6sij2qZiajMnD+LljzWR/j7EOa1w2r/ucgCQRDKny3ye+Is3Pe390jz32KHm/lO9zpa5ai4le7/O6jS5atKjkdXE7HfuOeX7ltmlu904Ge3/Y0/DncD9y33/rrbck5VeMeVliPX1sXjgQDAAAAAAAAABVjgPBADZ7scYZKbn6VUoCkxjevDnhs99++0nKEwtON/j5mMJzciGmHP3v0KFDJUlLly6VlCci3M6KyamYpHDawa+plAi2VK1EJ/9ineI40vn6GDJkiCRpm222kZR/f06RONUVay46vejvz8lgL5u/v9tvv712XvUlvdDyfP7zn5eU19F1O4w1bd3+YuI31sCNCflYzzMmW8ulhvweJx3dDmM/97Tcz3v16iVJevPNN0v+7datW8m8YqrZ03Vay5+lX79+9S6nVHfbFLf5Xlb3IU/H80ylkGM/8r+u5Szl6y20DvE3PfTQQyXlfcpJwlgvNKZ2U8nf1DYits1y+1GpacfEopc1ppfj1TYWpxPnXa4uf0wAr2+yMH6PcZ6xrmqnTp0k5QlsrwulfF2EzU9cN3s74+3H8OHDJeXtx9ujJUuWlLxfyq+6iglev8c87XhVm9tkrOFvsd69xSt0ivuz7gdxm+R5+Go187THjBkjSVqwYIEk6fXXX5dEEhibfv+fA8EAAAAAAAAA0IxIBAPY7MWahak0xOYmJmJS6Sd/bzFllpoOqpN/5x122EFSntjzKN+xfqHvO8kQkw2Rn/eozq6/FkdsLnJaKc57QxNDfr8Tf3Hk91WrVtV5T6V+sPPOO5dMM9ZCjSlnf24nQ/z5Yzpl0KBBJY87HSZJy5cvb9Cyofn495PymtXuI17nOkEU7/t3TSWBY8IplVCqlBSU8rbkefXp06fkve57noeX0e9zEjimDuOVA16vOPnk1FZ9tVRjvdVYszHWU/T7ve5yCsvPx88frypyir+YUiQR3LrEBK9T4v6tfUWK+6LbSGzfMdEeawDHlHnsY8W+GKfhecYUcSqV7PteD/gzxXkW66SWu1/cTsRao7G2b0wtxxrgcZlTVyF4enEMAfe1L37xi7Xv+eUvf1nyeVD94t8nJ554Ysnz8+fPl1S3X/v1Mf0rpa8ciQnhyI87dRznlerz8W+t2KeK7/Vj3u+L+9Pu237e64rTTz9dknTZZZeVPI/NEweCAQAAAAAAAKDKcSAYwGYnphuM9EApr/xdk9Sj0Trl5MSLz3r/9a9/lZQnNCzWxUJ18ojJ5hSE00dOOfjxOPq3xaS5X+/kqxNFTnA4nVscfTzWSI0Jx+22206SNGfOnJJ5xmWopJj4k/LPGNON5T6H78dl9LLH9FdcttTjnqdrprp/evrjxo2rfc+tt94qKa93iZbHv5+U/069e/eWlKd5/G9MibuPxTqAsf525NfHJJMV27XbnVO17o9O+jqBHpODMZXv9UTsDzH96Om4fcd6vsXlTvXj+LjXKTH5G5O9XsaYRnR/d0rZn83fCVoft6FvfOMbkvK+F1N7sW95vyjWuI6J+NT6O6Z3i2JC0X0hrvvdh+Lr/bjFJGLs53G+Vlz2VH3s1POpzx2X2f3az/u++1RcN9XU1NSZJjYf/vtkt912k5RvI92PfJWW91NTieDidimVXo/9LVXXO/V8fJ2n73WH+0JcZ0j5Nsh93/fj2BHx6h9vh/09nHLKKZKkX/ziFyqK80Z1a4oDweWPuAAAAAAAAAAAqgaJYAAAAAAAAABoRpSGALDZ6NWrl6R8kI84wIsvGfKlNL6ExpfYRNU+wJIvldp1111LHo+XM/kSon322UeS9Mwzz0iqOyiKLxVGdfJgZ76M2pdZu534MrtY4sGXtPlxD+rh6cSBneKAT75Mu3j5ahyoJlX2ZaeddiqZpl8/Y8aMBn3mOFBVvBSwWK7C/Jhf4/fG9Um8rDxevhsv4/VlhR7gKl5u6Pl6wDFJOumkkyRJN954o6S6ZV3Q/Iq/swdK878uG+G24m2Vf2tv69wf3CctXv4Z+0lsz6mySlJeHsHL5LbkS23dT+MAVnFwqTgvL7Pf5+10HPiquGyp5Y2X5rrvxUGz/Fn8Gbzt8vfsPuWSHMU+JeVlA7zPgdZn2223lSStXLlSUt6n3CZS6/woPh7X96nLystJDSQXLyGPA6y5j8R91bgtTZWAiduccssYSz+k9ovj83G7l1pX+X1xu+jP+tprr9VZFlSvVMmRvn37SsrbqNfVbjdeZ8e/X+JAcEWpEg/xeYuDOMaB7OJniMtqfl+5/ub9Z3+uVFmXuP/tbduLL74oSTriiCMkSQ8//HByXqheHAgGAAAAAAAAgCrHgWAAVc/Jjphu8NlWp4GGDRsmqe6gNk4Hzps3T1LdM8jVIp5hHzx4sKQ8feZEl892O7Hp1JnPOPfp00dS3cG+Bg0aJEl66aWXNt2HQJOLyVP3K/ej1OA5MeHrpEZx0BdJWrBggaS66ds4qE5xIJyYLkyltmIqyZ8lDlSTGujG3PbN0/F3UU5qoC4vexxMLqZFUgN6uZ/69THdWBzUb+HChZKk4447TpJ022231btsaHrFAQn9W8ff0utop4NS2yjfd9tIteeYFPTryvWjOCCdX+t25+2F+3dcxtQVAzGdHtt1XPbifU879veYmoqDZ7m/xnnEdZmX0d+/1xe+H7eJxXlX235Dtfr4xz8uqW66PortP647U0nf1MBR9b2v0qCmcYDFVGoytvu4DU2le+P0i1JJwtS2OKaMvQzuM/HKGP8O8TM57VxMEMd9VVSPuC3y322f+MQnJOUJ/ri/6e1PpYEUyw2SFqdV6coZv97b5TjwdkwKxz7h7UzcLy3+3++NVwHEbZ+n4StzPKCpv7dHHnlEkjRw4EBJ+d+5bK82DxwIBgAAAAAAAIAqx4FgAFUnnjEeMGCApPwsa6yXtvXWW0uqW9fJ//br109Snix2QnHmzJmS6q+b1hr5+4rpj/ivzzD7TLpTGDvssEPJ4z4L7u/fNbweffRRSdXzvW2uDj30UEl1E6tORrldpFKmMcngupqx3zq96oRkTG4Up18uJSzVTSPHNu626ISf5+0URkxIxhSk1Vcn0YmnmB7xesmfz4kPT8Pv8zLHWsNx+qlaxMWkpZfTv5Hrgbt+HGmQ5ldMyHud6z7iBKp/p5hAj6ketzW/LlUj2O08phLL9eFUH4qJX29fXW/X213Pw+3Vn9Ht2P3B8071reL9mGCMCWffj99D/Nxx2+aUc2pcAU/Pry/WCKYvtS5OAHv/JSYGY23r2GZiDWGLSeCY3q+U+i1OMybcY3/18962eLsW+33sFzE1maodXHwu7gfH9UEqvRy578Srcyx1tYLfV/y8qD5uT173HnXUUZLyMV3iutwJWCfL3U68fXFbj1d1FaeV6gdxmdwWvR3wv16HxL+h4roh1i2O+9RF7gfebsb1jfubvxdvu7wsXrZjjz1WkvSLX/xCUj7GxNtvv52cN6oHB4IBAAAAAAAAoMpxIBhA1fEZ0UMOOURSPop5rFXqmlExoRcTB36fX+czqnvssUfJ651kmjNnzkb/TE0hppxinaoo1gCO9a78r9MZfj6VZETL5/TEyJEjax9zf4i12dw+YpI1JjGcFPT7nLSfPXu2pDxh7n48d+5cSXk7qq9eYUPqCRdfbzFZ6fSiX+dliWlmfxanT4rLFufp78GvcfLJiRWvn3wlgj+vEy4xMeznY43hWNuymAiOaf29995bkjRp0iShZSimSv0bxxqEPXv2lJSnf9wOU4mimHRK1Qq2mFYsbhNSSd2YxnX79nbSyx4Tf16PFBN+9SlXyzBVhzS+J5WIju+LfStOL+4reLpeVxU/D3VLWy7vM0p5Ej1u1+K2oVLiNVVfu1JSuFxCOCZ+4zxT04zbgniFS0wCx/fH+8X1RVyHxGRw7CvuIzGd7D4WxxqI9dD9GTw/bw+LfJWSrzpD9XB78rbOf1/EMRW8f+W2Xay1X3y9Xxf35YpikjdVpz62+eJ4DOWej9NP9eui+N64H+nP6fveF/X3Eq+A9b7smWeeWXL/+9//vqTy3weqR1McCK5/7xIAAAAAAAAA0OqRCAbQpGIizskCnxlOjXLus7F+v8/6xrREHEncZ1YbmmBqqfw9FUeoLd6PaRMnOCx+jz4THc/Y+31OI6ZG40bLM3r0aEl5aqActyP3Cyd/YgI/NVK524mTg06nDh8+XFJex3bq1KmSyqcaY13SmF6PZ7hjqsJ10rzMMdUYE8eejz+731dMO8YrCzwvf5cxvezXvfbaa5LyGm9xmWOtRL8/ru/KJftjIqxPnz4l0yK13/yKKT3/tvEqF/cVr1vdNuJVLH6dt1WVrvaIdRPLJYJjKipVbzd+nphOdBv0Z4rb09gWY3qxmFyKKeGYTq50RUD8bKltYpye1wP+bP6+pTxt+uCDD9Y7TzQfjwch5X3M6/J41Yd/v5hkjVeYxSs3opgQjkngcu0kTiu1vo7Tdp9KJYcr1byvr1ZquWR+uc8RXxeTxLGvWkzS+33eLvbo0aP2uXi1EH2teqRS8F4H+++N2ObjurohSde4DYv1vON+oKft+rr+Gyj1GeK/qVR9sf3G/ebU36XmKxrcT+L6zN+X379o0aKS51HdKA0BAAAAAAAAAFWOA8EAWrVyKQSf+YxJQyc1nNIrNwK6lJ99jfUV4xnXOMK6+Sxw8SxuSx7F2MmX7bbbTlI6kRW/r5g0jGfFYx0tJ2P8/Z988smSpGuvvXZjfRRsIv7tYs0zqW5NP7eLmFp0O4j1S2MS0O0ptr8ZM2ZIkgYPHixJGjFihKS8Jnex9m1MUsSaqHHdEBMesW6hxfricXTnuD4qrpf8XKxlF7+/mPDwMrgmXkwlxwRmTEPH9GexVp7f6zSV6y1+7WtfkyT993//d53Pgabxmc98RlJeS1eqWz86pu1j33Lb8fYn1uO1uO4uN4K6VDepFP8vpdODMVWVSunFZGSlOsblno+PxRHWY59Jvd7rsNjX/Lr4e3iZ3ZeHDBlSu0xOiR199NGSpHvuuafe7wFNr7jNcV+JNdzdt9ynYnIuJoRT25hy24hyryv3XFSpTnGs4516f0xAxv5eLmEb+0xMXPp+ahrxipbYN/29ezq+7z7mq3eKKeU999xTkrR8+XJJ0r333lv2c6P1iO3KV4Z5m+erL7x/E+u7u7/GK7LiflmxHVXqo55mTALH/cxUX4ifLc6vXEI/1Y9if4rvjfXtU3+nbbPNNpKkgw8+WJL01FNPSUr/vYzWjQPBAAAAAAAAAFDlOBAMoFUrlwj2qKg+0xnPkBbr9hUfj+JZ3Zi6iLVvfb9v376SSkczbsmJ4AEDBkjKP5dTMU5c+PuKabJYJ87fj5OZgwYNklQ3IerX+Uy+E55SPso6KamWwb/x2LFjJeXpimJKMSYtYp1St5uYnoj9MyZlXWfT7dJ1Tl9//XVJeYrVdR0XLFhQu0xOLyxdurRkGnFd4Hm5raeSxLEem1/vz+SrAGI9umKy0ssQrxiIo1f7dbGOqecZU11x/RTr2MWkVVFMi7jvuq+SBG4+sT9J6eRpbBsxDeT26P7s6fh+bBuptJHVt36O04zpJovtM4p9K74uVXu0+P9Kyx2nEdNW3kZ52eNVD/7X36/TiZ6O11lS/ju6r7nm+bRp08p+fjS94j6bty/eDlVKvsbtXqoWaaUawA3Z90klfOM+WexjsW5/Kn0f+1TsT+WudEnNO/X5UlfhpBLCcX71rV/8WP/+/Uvew/as9Ym/t/9e+NSnPlXy/OLFiyXlf6943RuvXEyNEWPFNpJqw6mEsPf/Yv+KUrWB4/OxjZebdxzbJXUFrD+/H4/7vLGPLFmyRFLdfQf6UHXhQDAAAAAAAAAAbAY29cF9DgQD2GTKrcB85tJJjnLpPClPR8Qav/FsbWoEcv8bawk7VRfri7ZUrmvVq1cvSXU/v89yp2pMxTPuThP6+42/h79v13x1wlNKp8PQPE455RRJeRvxb15s2/7NnDJ0P4v1c6NUvbSY3o0JfLdHp5I9KnI5qffGdHJMHXmd4WWI9dFictDz8eucYiy3fooJjDi6fEx3pmqTx4RL7Dux1nl9yalU/TdSIM2nZ8+ekvJ+JeXt1fWiYwov9s/U1S6pVLnF1F4qhVd8Lr42iqOUu52nPoPbZOyLsS3GPliUSilWatepOsdxGf1vrEuZSkkW3+MrZkgEtxzezkn59iXWgTbvz8SEYayzHdtA7Euxjm9UbKP11eot3q+U9G+oVM3hhtRRTaWV4/Yu9t+4TU5dCZOaT3FeqVrnaH38d8XnPvc5SXm78P6f+6m3ffHvD7eTeAVW7IcN2bbFcSziuCrePsftatxXi203rkPiVSTleN6x38Sr31L7rLHfedk99oaxD1idSAQDAAAAAAAAQJXjQDCAVq3cCqp3796S8rOzMfWWqnsYX5dKD8Xann69z0D7zOqiRYs24JM1HdfWcooxlWT29xBrUsWabv5+YgLYiRrX7vJotOXSJWherq3nGrv+zWIbl/JkhVNxsQZ3rLUdRymu1M8sJgVjkr+YqPL/3ZZ936OIO13iaTjFH0eSjgkrpyX8WV0T1Eky941YR7LcNFNXIvj7jPWGUwnKmOiINeBiStm/YXH53VddF9Pf0/jx4yVJ3//+94Wmcfzxx5fcL9Zmj/WyraH1eN33YvuMKfyYTkwli8vNI4p1iT0tf4bYflMp3Dj9uH6oL+2YSlTVN2J8Ueyr8SoHL5vXK7GWcHEZ/Fr3PbQcxXbvRNyQIUNKnovtzPdj+jSVwKsvwV7u/fUlglMp/Hg/JhBTqcHUVTqV5ld8LJUQjleSxaRmqo9WqrEcE9lS/lt4zAn2K1s/76P5ipKY5I1tMl4Vk2oDnp63s8V1QFzvp+pbm/etvP5P/c0Ypxc1pp/F7Yrv+3N5uxu36d26dZOUb4fjttA1mP/v//6v3mUFKuFAMAAAAAAAAAA0IxLBAKpC8Uyra+8tXLhQUt16Sz5b6zOl8X48wxzP6jrB4DOuft2CBQtKXtfavPnmm5KkHXbYQVLdVJPTTpUSnn68a9eukvLvya977LHHSuZLWqPl+fjHPy4pr5Po/hUTsVI6Lej3+Hd3e7KG1ueN/Swmz8ulbyMnNdwmvfxOvsY2HtNGMZnhzxRHjI+fsfjZYo3TVE3FmJhKjdxeTIqWm04cxd7LVq5uYvwO/X25/t5+++0nSXr66afLzhMbT1yflrtiInVVi/tKfDxeDWMxGdjQPwjqq32dGgE9pqlSV5L487p9xxRVnG6cf3GasR+najamkloxhR9rPMbvI9adLK4PYhrTV04ccsghkupuF9F03C5222232se8rfN+YSqNa7GtpOr3pq5Ei2KCuL5pxOcrXV1TLm3cEOWSivF7qVSXO/b7+Lq4rKn1RNzWFu/7CpfU+ARoPeJ603+n7LjjjpLy37pS/ew4Vkwc06Rcajd1ZVhqe+JpLVu2rGRaMZ3c0O2tlbsqIK4f4v6w5+F93phS9jL69VtvvbWk/Pvxei91lQCqAweCAQAAAAAAAKDKcSAYQKsUEw+ud1TkM5g+S+sznE4J+Mynz9ZWSkvF2sE+wzp16tQN/jwtgb8H18aLNd185tmJ4XhGfdKkSWWnF+tTuiaV06ZoeVwrNqZK/ZsX68ym6g76906lKlKjpMe0RLwf+6GVS03EdKHbstteTMI6UWmpUaP9ffizp1Il5VIUla44iN9DXMaY7IjT8evj417m4vfk1/pffy9vvfWWpHy92pAasdg4Yn8ql3SN6Xhz23DaNLaFmHxNpadS9T5TNXSLUskh98HYx8zL6te5Rna8ciBVC7hcSjGVJraYdoqpMtf+9nRqamrKvs/fu//1ZygmEv153Mc8jz59+pTcJ3nV9Ny2vN2T8t/Dv6FrlFq8IipVQ7hS4rdSOre+P8IrvSf229Q21+J2M267y111k7pirqGfJ047Vfs0rhe9bF6fFK9S8ntcb/2JJ55IfWS0MrNnz5Yk7bTTTpLS+3Dx6qZUirdSkliqXJfay+DtrtO2/lsnjqeSqoMfl6khady4fxxTy94WeRoeK8LbNv+95/1Dv+7111+XRKq+2nEgGAAAAAAAAACqHAeCAbRKXjG5TtQee+xR+5xrRjmd4zPEcXRnn0mN931G1GlIn+31fdeo8lnfKKapWouY1Eot//z58yXl35Pvb0yt9Tts7caOHSupbl1fpwucuikmgisldWO6ITXqeqU6hjG5EROC5ZKTcd5xxOk4Laf2KiWjUmKysJg+iWms4nco5emRVMLa30Mc5TouW2o0bf+mxWWKV0l4GV1XznXjnHh+5pln6v38WH/ua/6urbhe9m8X+5rbTEwlxnrQMWWbatcxXVTf+jiVzE/VBnXK3PN0P/Cy+/PHz5pKJ5arW5xK8qc+R6qeZFzf+V/3Ve8TxKS2v3enr6S8Hrn7mtOnqfrNaDpnnnmmpNLUb7waxn3H60zfj9tK34/pwdR2K3W1RUz2FVWqM5y6+sb/etkbqyFXBMTXppKKcbuW2hbHhHBcx8Xko5SnHfv16ycp74f+7dB6xLbtOt7uq3HdG68giTVzU/V56+tnsS3H7W3cN4vbqlRN+fgZ475xuatg4tUu8TUxIR2T0F7GOK6F08zPPfecJPrK5oIDwQAAAAAAAABQ5TgQDKBV8hnXQYMGSSp/NteJm0WLFkmqm/BwqieO0u7Uj5NJPsO6YMECSXWTwKlRktfn86TeW6xdV3yd37cx6u02dLnnzZu3XtNPpUnKJbnQPNyfnA5wWiCVMJTSNdZiCi+KiT6rVI82Jl/LtdtU6srzdHoo1nqLI5GnklbF9FHxcb/f64xyCbNUUiymQmLS2vOsVOe1Ujqs+HvEOt+eh+87Eel5ktTfdJzCjlekFPtc/I1jnVK/x/f9+8U0eqqPVUoalns89u9KbcRtydtZL6vbXmz/DdluNFRcV1lcF3kZ3Z+9/Y0ps/h9+XUxISblKcXevXuXzMPJx5EjR0qqW2sfm55/53I1pWM97tg+U9sxi4lES6UCG3KlS1RpGxzHy2joejymD8vVGI6fK7W9qbTsqe8jXmUTX+f1SLna47NmzZJEurE1i20wbhdjv4ntI17ZaamxGeoTU8fmtt29e/eSacb9xCi1Dqnv76C4X2jx6r24HvGyuL/4e+nfv7+k/PtdvHixJGoDY+PhQDAAAAAAAAAANCMSwQBatUceeUSSdMghh9Q+5oRNTDU6FRBHePXZ1zjSt8/S+nWuDRxtyEoyJjO22WYbSemRYC2mU+KZaCcRY72oVF3jjaGxad6G1JtE03JqzWJS1imCYn3bmByoVPPQUjX/4uORkyBxPsVkRxxtOSaBY0rL9z3NVArJ7/f3EKefSn8VXxNTwzGZEtPLsbZiuZHby00vzjfWlCvye2Ii2tOsqakpO21sPEcffbSkvG+VS9D5+4+vcR+M9aFjwjXWCo1tLNYZTNX5LLaD2GdS9REjb6fdnyuN5m6V6i/Wt9xxHZSqV+p/vc/g7aZrZns9EL8/b3edvCyuG72N9nPxNxw4cKAkEsFNyb/fa6+9Jinf/5LyWtaxLcV2F/+NdULj86m+Zan2vj5SV9ukko2pGqb1vW99k/upBHAUE5ux5r33WXz1g5T/Bv49d911V0nSK6+8UnYeaPncb9544w1J0vDhwyXV3bbF7UOskx2vEovtt9j/Un20UpuP+8uxXq+XLVW/OCq3b5u6csbzjlcLxW2aXxfHDfAYO8ZVYNWNA8EAAAAAAAAAUOU4EAygVYpJJ48OLOVnOF3T16/xqN1Tp06VVDlpkRr9fH1WiuXqvRWnNWTIkLLTrjTKeayfFdPNTkbEEXaLr03VXmsszhi3XqmambEmcKzjWRRTibFubmr0cIuJ2Pg6pyc8n5jgKKZZY9IvJnZjSsSvc3+JKQqvUzwPfy8xZRuTyMXvKZW2StUlTo1YHddblZKYMUVaTFrHWqgxyeLfm7696bg2bEzblxtJ3Otxt7+OHTuWvMa/vduvn1+5cqWkuom+VH3fSum+cnUVG1oTtJI47UrJ4PqWM95PrRdif4+pfddvTiWGnR71eiLunxTn5eX2GAXxKh5senHbMnjwYEmldWadjPNv7HVhqqZorPeZSiZWWp/H2p7lpJYh7svFeaYS8J5XpWUstw3b0HEd4jolVRs47h/E78nruuJjPXv2lCR97GMfk0QiuBq4P8artLzdjCl33/f2NT4ek8HFtl3uahOpbt+Oj/tfL5O3J7F2sOcda/rX16diX4x91+LVan5dHDfAyzZnzhxJ+dW1jz32WMn7Ko3dgdapKQ4Er9+eIAAAAAAAAACshwMOOED33nuv5s2bpyzLastxNcTHP/5xvf/++3rhhRc24RL+0/HHH68pU6Zo7dq1eumll3TkkUeWPH/JJZdoypQpWr16tZYtW6aHH35Y++yzz3rNyweCK902BIlgABtNPBvqmn1PPfVUndfGkb19RtNnhletWrVpF7bAZ1179epVcj+VyLKGjCJbfF2sMeXUkc/qlvvM/j7Wd2TlDR3Vvdz7SSA2rd13311SXr/SnFRIjVAs1W1zsa5wTESlxFqKMSEUE1ep2ovl5l2plq+n7f7iBGW5tFG5z5xK/BdTKDGR63k6CehUYWp0ekulG2OS2qlRL4MfL34WJ1OcZPQ8UzVTP/e5z0mS7rrrLmHjGD16tKT8u/dvUY5/r1SK0O3eaUbXt/c0U3W3Y1tLJQobMsJ66uqV+HxDk8Px+fj+conhVE3TODp7fH2sN+m6vqn6kjH55fWlE8TFdJp/g9RVB+7/2PTid96jRw9Jpf3Kfcf7TP7dYpLQbcbr21i3u6F9p9wVAHGZKqWRU9vYmJ6tNAZF6qqU+uZdaRliP43LFO+nttHxaiVvs+OVNZK0YsUKSdIee+xRsmzsX7Yecd/qmWeeKXl+//33L3mdxfYYx3WIfSle/VZ8b2P7sHk/MtbjjVeaWfwM5fYr43rHUnX/4/qqOL5HcTp9+vSRlPeZQYMGSZLmzp2bXJbWpnPnznrxxRd1ww03NGoftnv37rrlllv06KOPqm/fvhu0DAcddJBuuukmDR06tOzzY8aM0W233aaLLrpI999/v8aNG6e7775be+65p1599VVJ0vTp03Xuuedq1qxZ6tixo84//3w99NBDGjZsmJYsWdKo5SERDAAAAAAAAKCq/PnPf9b48eN19913N+p9v/jFL/S73/1OEyZMqPNcmzZtdOGFF2rWrFlas2aNJk2apOOOO269l/G8887Tn//8Z11++eWaOnWq/v3f/10TJ07UueeeW/ua2267TY8++qhmz56tyZMn64ILLlD37t1rAz2NQSIYQKuSqo9UXFG9+eabTb9gFTgJXBzZWCp/FrqosfXXUokvz9e1iKU8NeZRl9d3GVIbiVTKJJ71bs1nmKuFUzVOBDsd7sSCUwRur8Xf1omCVCLDyaiYLkolDFLtwcmGOL+YHCqKtT5T9UajmIyuJE63XPIolSTzvColLuL7UnUd4/z8Oidiiong1KjW/r5iSsvrMWw8DU3KS3Xr9Dml6HYeawOnRiePaamYeK2vJuj6StUbrVQDuKEjt5dbJ8XlT6XoY2o+jqDu1L5TU15fxpSi15OLFi2SVJr4iklGP+eUd6xhiU3Hv/9nP/tZSXlyzwlwKe9TrjO7bNkySXXrSlvcv0mtzyslhlM19IvPxfV1XIa4TvHnjSn0hmrMfmjsc6kaphbHEkhd8RPXF17HuR8Vv+f4WExBovWbPn26JGmnnXaSVLmGfLyKw+K4DkWpWtqpNLv3m73dcDv0v6krydanzra3/Z5Gpatd4v14lUxNTY0k6ZFHHim7TBtaC7y1OvXUU7XddtvppJNO0r/927/Vef6iiy7SSSedpLPOOkszZszQgQceqFtvvVWLFy/Wk08+2ej5jRkzRldccUXJYw8++KCOOeaYsq9v166dzjzzTK1YsUIvvvhio+fHYHEAAAAAAAAANmvDhg3Tj370Ix1wwAFlT8xttdVWuvjii3X44YfXliyZPXu29t9/f33lK19ZrwPB/fr108KFC0seW7hwofr161fy2NixY3X77berU6dOmj9/vo444ggtXbq00fNrChwIBgAAAAAAANAitW3bVr/73e90ySWXaMaMGWVfM2zYMHXu3FkPP/xwyeNbbbVVyaByxbF5tthiC7Vv377ksVtvvVVnn312o5bvscce08iRI1VTU6MzzjhDf/jDH7Tvvvtq8eLFjZpOlmWNrn3dWBwIBrDRxBWWB/mYNm1ao6eVujx8U5QqiANZxJIQDR0ULsXLHAe78X1fGle8VNXL5EvsPDhKpdINDV3G1MaDUhAtz3PPPSdJ2mGHHSTl7cRnwd1G6itl0tBLyWK7aOil33FAxHjprS+xluqWNmhsm01dspca6CpezutlLF4KGMu2lHtNuXkVB5wqSl1yHKcXB4IrTs+XCsdL2/05YtmKzfXywE3Jg3t4EJJY8qTcQDG+BNW/X/x94m8d0yzxd0z9vnHAxoaIl3Cn5hnnEQd0jOUc4qXicdnLTbOh7TV1+bl5UDEvm7//uKzxs7ikRPExl5cYMGBAyXtjqQFsOu5bcVDR4jbEJbNi6ZJUaQhLDSzV2IF/G/OaVHmJ1OtSA05Z/MwNWfa47YzTiP079mv3lVgixe/zvoe3SXE/oLiOi32qXJoPrUPq7wWXavGBLw/QGd8X/zayWKonNeC2lC6VFPtXLDnk9Yn/jSWYGqr4+tjXvQxxAPLYz+JAsn7c03FZnDlz5pS83ja3PtS1a1ftvffeGjVqlH7+859L+ufv0LZtW73//vv6xCc+Uftdjh07VvPmzSt5v0t3SNLIkSNr/7/vvvvqxz/+sQ4++ODax1yaSJIWLFhQZ0C6vn37asGCBSWPrVmzRjNnztTMmTP197//XdOnT9eXv/xl/ehHP2rU56Q0BAAAAAAAAIDN1sqVK7XrrruWPHbOOefo0EMP1fHHH6/Zs2erbdu2Wrdunbbddtt6y0DMnDmz9v/bbLONPvjgg5LHiiZMmKDDDjtMV111Ve1jRxxxRNmB6oratm1bciKzoTgQjPWSSpkATe2NN95o8GtTaaeNJdbwKZ4RjPOMZ4RTfalSuiLejynC+HxxPj57nUpgWCoVierhVIR3JGI7crLGKYHGJBpSKaI4yJPbZkwfxZR7arCPYuLWj8XBsKJK6eSYvqiUlo8D2hUHqYnLHxOOfk+l9VJ8f6rPpwZPKU4/NbBITK7492+JA3G2Vv69XdfNfXDrrbeWlP9exWSOX5PqC37ev5sHCXWi3+ljiym+1HTrG8AqvsYaup/Y0MFG4+vrSyc2NgmcShD7e4ufxSl7b+MHDhwoKR8kztMt9n+nfDz4mJ9zwrE4iCM2rZ133llSfkVZud/f/c5JVf/rdLhVSs2mtpWpwQ7XZ/8qlWSvtJ1Obf8qKc4ndcVKQ1UaVC9eORP7ifdfi+uZOEisL8Mulx5G6xD7x5FHHikp/7srrnPj1TJx4DdfAem24ERssW3E9X7sq7GdxbaZGpyx3CDn9Sn2jdQ+eFyfxEHhnDr1+73u8/358+dLkkaNGiVJev7550umF+fTGnXu3FnDhg2rvT906FDtscceWrZsmebOnavLLrtMAwcO1CmnnKIsy/Tqq6+WvH/RokVat25dyeOXX365rrzySrVt21ZPPfWUunfvrv32208rV67ULbfc0uhlvOqqq/TEE0/oggsu0AMPPKATTjhBo0eP1plnninpn/sL3/3ud3Xvvfdq/vz5qqmp0Ve/+lUNHDhQd9xxR6Pnx4FgAAAAAAAAAFVl9OjRevzxx2vvX3nllZKkm266Saeddpr69++vbbfdtlHTHD9+vBYvXqyLLrpI2223nVasWKGJEyfqsssuW69lnDBhgsaNG6cf/vCHuuyyyzRjxgwdc8wxtQefP/zwQ40YMUKnnHKKampqtHTpUj333HM64IADNHny5EbPjwPBaBSf7fLZqaFDh0rKUxHxTJTPNLnmDLCxuVZfUUzGWUNr8MUzx6kzrU4BeX4xWVu8TCOVmohnmFPpiPj6VJ3EVKqk3BnouLzbb799yTS8zOtTfxmtU+fOnSXl6aeYiCjXjmPKpqFt2FKppFi3NrbtON9i6tdtu1LCKbUssb+kUl8xjev7TiAVpx9rucXvKZXyb2hKK5U0tnLrRT/mebnOnh+PNVErXZ6GhnN77d+/v6Q8zbtixQpJeXsp1j70/90/Y33DmNw3p588z9heG1v7udiGUv08VTPUvKx+PKbQU6nkxixrpT4R65Z6GWJd7fhZvE7y497HTa2zpDy5Fms1VlpfYuM74YQTJOWJedcZLf4GMYka20K8giP1O6bW4xuz3npD/5BP7dNWWvbGpH5T65RUnVX3Oe8vxxR+vHIt9mX3teIyxisc4pUQaD1Sf9N4He1tYtwWxnEu4lWTsW5vY6SutvK03VbjPFP7kakrbsrVLY77i5XG0oifv6amRlLez7wOXLhwoaQ8GR2XobEp5pboiSeeqHe9e9ppp9X7/ksvvVSXXnppncf/+7//W//93//d4GXwsbOUO++8U3feeWfZ5959910dd9xxDZpXQ3AgGAAAAAAAAACqHAeCq0hD60uVSyiVe67Ir3MtM9eWSSWczPd9xq5cSqohy1x8z6au84rWob5ana7BF1M+Tg44HZUSk+8xbeXRvt0fYu3NcimKSnXkGpq4SCUzY424VC3h4nzjaLqpWqUjRowoed4J4Vg3K/XZWvMZ5M1NbMOpun3Fx2Myt1JyN0qljmPN4FiPzWIN4eI8/Viqxm9UKbWVuu/pO+UXay0Xlz8ub+q+VUpvxW1jXP/EdU/xd/Bryy1vkZfNaVVsOCdEnciJ360TTh4dXcpTTn7O7c2/ddx2xdStxb7Y0LrU5e6n0oSp1Ly3w7EesT+Tl9nfS0wvp5LG5UZ7TyV647LGvpeq1+37TnzF1JU/i/d5/Rkk6e2335aU7zfE7e76JNPQOE67+WouJ0X9exavMPP62s/5757YblN9J15ZlrryI7YdK9dWK9XbL9cHistQaTobI50et1OpecXvsdLVR3HZYj30cp/d69TUd46WL25HfOm+16Pun94WWtw/SqVwo3J/i6Wu/IrPxzEp4r5YbIdxmxaXIabmi/PwvkB9f3dK+XrMYw/4+/J21v1m8ODBkqRnnnmmzudH9WqKA8ENH1EGAAAAAAAAANAqkQjeyOLZ1piaMo9QHOvFvPXWW8lpp476u26k0w2pM+Gxxpun5/c7EVzf2YVUYiumM1NII24e6qtt6ASVRwd1u/OZUKemKtVpin2rT58+kvIzsZUU23IqaZGq2WapZEaldGUqJVWutqPfkxq92p/Xrxs+fLikPNkV6yOmEi7RhoySjU0rlXhwusB14aV0wqKhqaNK6+y4PUklh8ol8N3+nfSKCb6Yxk2lR+J21K/39+ERmT1f9xnXci2+1mJiLPZdvz5+H7FvV+o/qXR3cR6x1npMtixdurTk83Elzobz/pSvUPH2KdY2LPY1czoxrrtjqjQmkOJ+Y9yOxO1Fqq+V69uptJ1f68/hPhH3I2Of83bbyS9/L6n1QzmpbVrqSofYv1N1YWMa0cvm+fi3LNbhj/OI37VT+fSxTce/m9uWfwP/fsW+FhOG/i1jLVKvQ2Ota/cD7yd5XrFNprZ/9e0/Vartm+oj8X2xZnhsk/VdURbnVemqtvh5vR+eSjPHK1pSV9X5+/d6ovhe/+3p7VdDxwlB80v9feDfsHfv3pLS7cZSf4+4f8d1d3F+Ma0e91ndf7ze8PPen/K2zvPwOj6VDI77snHbKeXtPfab2LYr7W/6vvc/hgwZIkn64x//KGw+KA0BAAAAAAAAAFWOA8EtWKVahD7L5bouMT0UU49Wro5SrIHqs1Y+U+SzVvHMdyotYTEt4RSL73t+PltbnHbKoEGDSqYd3zd37tySx0kdVqf66jy5b8ycOVNSftZ2u+22k5Qne322Nvad5cuXS8prKvl1ThzEFEZD6o/GFF8qgWjxzHAqZWUNrQFX7vHUMsT0SKzd6LSFl819L7XMaPmcenJiKlWHrNx6OnV1SiqNGMWa3hbrdabST8XtjxMU3g667XrafjzyMjrRERN+MRnp6fn1vXr1KplOcZlSacPUuiD1PcbESyr16H+dYIsJkeJjMeni93hfwOtSUoobz6xZsyRJO+20k6T8Ki5/9+XWy26H3jfzuthtKNaBjjUG4z5apTqncRtg5eqWxn4b25K3o6l69hb7TqxlmKqDWm77m9qPrpQ+jH0vfn/F9GFxGYsJ4Did1HNx36DSlTRYf15P+/eOKbri1V5O8vo9MbXndh23S6m0fUrqCpn6/mZJ9fPYFyv9HRmnk0oEp9YD5eaRSg9X6oux7nacZ5x33Ecubt88rW7dupVMc9ddd5UkvfLKK3U+B1qWVPv38YKFCxdKymvtx7St+3aqZrXbRmpcleL/U6+J2w3vN/sKMbfD2IbdP+OVZbGNx+M5xeWP/cnTcF/254/p4rgf6WVYvHixJOmNN94oed7TY9+vOnEgGAAAAAAAAACqHAeCW6DU2WDfd91Tcyovjoocz1b7eY9mXHws1tBJ1c2qNDp0nK75fTU1NSXT9ZkoJy+lumd5Pe94Fs/iWTB/HzZt2jSh+qRq/RWlkoX+1wl1t7HYlmKifUMSO5Xqasd6c/GMcRz93We/Lab9YiKkXD3RePY5nmlOjdoc1wOu1eX3+YxyfSmyuCxoWeKoxe4j8b5Ut7Z2KuHU0FrCqWRwrMGYWmapbtLLyYxizV4p317EGnCx7cfvw8vs6fl5JzSdJquvRmhMaFSqEx6/v0rLGus/xnRzcVm8DH6uUk1VbDh/p044de/eXVLd2uz1bW/iNsvbBe//WUwZx3V8TOunEoDl6oG6rcfldB/0NGNt/Vjr0GK7jbVW4/Y4fsZynyuVOo59z9OI7499yMvk/p56X0wBF6fhz+nXpNZr2Phiyq1cOt0pPL821lH3e2IN4LitiNvD2BYrJYfLjeuQWj+n6u5bbHOpfcCU+q4oSyV2U/t5qT4Zt2vxvqcXE6Dui1K+f+Lk/pIlS8rOE62P17XujwsWLJBU96ot//aputjejrj/xnV48bXxSrKYLo41gONVpP7XyxavxEpd5RWP0RSfs1jfO+7TpfbPzf1m0aJFktKpea6urk4cCAYAAAAAAACAKseB4BYgnmXxGaVddtml5HmLZ01TKYJUnaZyZ59jwiiOiBxrNMb0oevh+PE46nU8A5Uacbk4L5/liiPLxjPfPvMbU55+/x577CFJmjRpUsl0jLNbrVOqpq6UrpXptuFUvBNLqVREqhZhKunRkFGe43tTtep8ljom4mP9K/cxJ+V9VjzOp75lSqW+4udJ1VX0Mrn2spfhtddeS84bLdP8+fMl5duhmJYol2yPj8VUe3xdpfrVlbZZsb3GBGG51ziR4RSJp+mabvFKE3/+OO/Y/5z8cLI41gEubtti+iNVf7hScjr+Gz9r7Kdxm1q8miCmR8zL5N9y6tSpwqbhvuW6v26jTggX+bdLbbNSidRKV6LE91e6Mq2YrE/VTvW2y58jlWyPYvt1G/R+pv91ny63nfZ6y/06fi8WU2IxGZ1K78d6sF7GWHu8uK8c94f9O/fs2VNSvi5in3TT877JNttsU/J4ue8+jrfyzjvvSKp79USsiZ1KAldaz9cntt+4rY3zdPv1usViOj/VD+L6wIr3K22vUvvLqW1PuXkUl8Xvj/X/i+uBOP6H1weuK4vWIzXmiFPebgdOtnq74P4Za+XG/cW4Li/uR1a6CjS2XW+ffZWk213sX6m/LVPTL84ndUwobstjP4uvj4lo9w2Pc+F/i2M4ofo0xYFgrsMAAAAAAAAAgCpHIjghni31yJfbb7+9pLq1bp2u8Nlov89nvWJNuFhPqdyosOWSVPXxGSSfbXWiwWeUUrVVLZ4ZjyNal3tNpRFjzc/H0eGdCHFa0XVwYmKaGoitm9O+Un5WdtCgQZLyduYzxXGkVvedeLbWbSL2sfh8pRpw9T1XKQ2SSvamkloWR6Etd4VApWRmpRrB8XF/7z6T7PuenlM48coDtBx33323JOnYY4+VlK/D3QdiikJqeFLVUjWA4+OpFF68X67WaEzmxnpxFtOYqdqLbsuxVrdf7zr3DalBGL/D+HliGjnWRE3VXozririPEOuQF8V1WqyF5/QNNj7vRznRFNuYE7BSvl8Y00vxPXHfLrXNiimr+Dq3GT++YsWKkmWW6tZFjPWKY19M1QJNXYni570v9/bbb0vK+67/LbZrL19Mtltct6Tq38dkZFzXxL4VP2uxj3o5U6nhBx54oGTe2HR+/vOfS5K+//3vS6q7vyTVTSKWu6Ki+J7Y5xp65VhcX8fXlasPmkoVx79n4j5t3ObGeaaWIS5rfVL7AbEvpb6n1LxS64VY71nK14cxxe2/VdF6pNKIvoLC20T/DZhKt1scfyhVL7sorpPd5uI2K24HUtvdSgnLOKZLsd/GK1ZTVwOl0vyeprfl/tfvW7x4saT8ytmYCKZWcHWhNAQAAAAAAAAAVDkOBDejmMhwEthntXwGyCkCn+F0OiiOYJuqoxPPRpd7T/yRK43e6nnEGqtOXcXRXFNnhlM1bIoamlqMZ8i9DE6POB3qRDBns1q3eFbSv7MkjRw5UpI0ZMgQSXnd05i4S9XXjmd1LXV2N/X++tK+sT/GPuZ+7v6fOvPsJEscnTw1Cm1xmWJKuFJfi2ez4/cTp+N1mV/v0Wi93njxxRfLzg/Nx+lD/3ZOTbid+bcsXu0RE/YxiZFKSkVuN36920kcYblScqg4r9hnvdyedrwaIG6zYjo51qT3fNz/UuuUctOur855UZyXpZLXlRLUxenEVLW/F0/76aefLjsNbDz+rgcMGFBy38nR4pUT/h1isi3u/6SSv25LMQFZ6eoWt1XvdxaXKe5zOtkck5MxoVSpBrbFK3OcvncC2X3S/xbnFa9kSM2jXCK0+Hhcn8T0lZcx/i7F78nz9D6of1+vc6dPny40rVj7vijWFo37XHFdH9elMUkc151xny71N01xfR3Ts/EqzFQ/jvVALbbjVO3wqNw2NzUuTUwAp670SdUy9Wf0ssX1TblEcfwO582bJylPkaL1iH9veL25YMECSfk4QLF/+vXlxkWS6o6BVN8VyqnxHVL7XjG1G5cldSwk1rSO+8DF96T2Wc3zin8bxiu44xWc/lvaKXpUv019PIwDwQAAAAAAAADQjEgENyOfjdl5550l5WdsK6We4lmvVOrWGlK3NJWSSJ1ljqO1+t9YLy2VRIpny4ri2feYgooJr/h8TK6lEpMkglu3VE0+KU8M+TGfAY2Pu004SRTPvsaUn9tzqrZtqu6ilE5JxMShed4xGRwTX07hp9KCqfupxxryfCrBEq9WiM/HUaJHjBghSZo7d27ttJ3yon82L9ddc+1c9wknaorbk1SNv5iESqXgY13CONJ5anuRGi25OK243HE74L6f6i/xM6WuCnC7jbX9yyVoyyW9ivNs6OeNz8cUVKyLGreNxWnE7aXTIa+88kryc2Dj6Nmzp6S8bcbUqWv1FZ+LfSWVLoxpuvh8TC9a6gozr+PLJd9dw9f7h/E1qT6WmldKrOkYU/5S/r34uUpjTFTaN031NW+X47a/XB1u/45e53id4dHaYyofm97rr78uKf/dt9tuu9rn/Nt6Hyv2sVgL279b7L/x9XE8k9QVIuX2gVLr4VS6Nl5BEPfFLHW1TqU0ZX2Pxe1N3KamksFxWdxf/DvEGqjlarymxoxhO9b6xPbj39L7Wq5hG/8m8rhLcb0a+2esj13cTsXtY+rvklQ9Xl/tEbdDcZ85bpfjFeDFuvKpq0PNnycel4l17uMYTv67zeNBpOqJo7pwIBgAAAAAAAAAqhwHgpuRz+bstddekvK6LD77mToT29BRXOOZz/rSf/GMbOoMbkzp+kxTrLOYGoUznsGqL6VYqY5cPOMda+B5WVzr0mfEfWYt1lhG61QumeQzwR4NNSYxYpre7TemJWKNuIam9cvVhEudvY2PxzPLrrcYawXH2lOp9UIq2VjuPTGZlZpG7IMxFRXPPMda4uZazv6MkvT888/XWU40PafnnQ6Iir9l7CcWR2dOtaf4/tgPva72ujtePWPFGqGxjmPsd95GVUolRZ6u5+U0mKcft0PFZYy13WIiI5UAjs9XqlEeR4p3/3SCprhMfo0/j1+zfPlySdKyZcvKzgMbT9zXc9t0WyumS91e3Xbi9iCVeIu1blMpoihuE5xqLS5T3BeL741isivWIozpqdS+q+frPlj8HmP9x7hMqX3T1GdIXWkX+7u/V/cjp9OkfB/UNRl9//7775dEErgp+fcsji0hldbQLf52Uv7ben3a0PVxTL6ntjGpRHB9yeC4fYu1R1PTiNu7mE5OjetSLvmY+lstzjteleB1SFwfVBoDINYej1fJFaftqxSeffZZoXWLVyAPHTpUUjrl7r7tYwCxLcf2Vu7KtdSVNJa6Ks3rkXicIf7tFPfV4hgc5fbZUvXu4+fz/nKsf+/9C2+7vP2Nx6DiMpMIrk4cCAYAAAAAAACAKseB4GbkL3bixImSpB122EFSuoZbagRbvy6mGuuroWqVzmCnamKVq+0r1T2jFs8ix/mWS4ykkhqWGoE3JiNTaRIn3UgEV68JEyZIkoYPHy6pbvLOZ0TjCMIx2RRraKfaXuwf5RJMqZFd47yiWAcx9b5UjarU9Op7LPbvVP3imPSNo9TGEepd89Vnqv28z+xLjKDeUvzmN7+RJH3sYx+TVLf2ZX0156PUNis1knJ8PrUTkhrRufhYnLbFthn7k9+Xqmvsf51OStWhK5e+jeI04zLHVGKsAxn3GeL3HNc9xeRh7Mv+HA8//HDZeWLjmzNnjqQ8meMkYkweFh8z/26x3cY2ERPEqf4R20pMGnpZirWw4zSjcu1OylNT/txui3GsiVhvN7WPWPyeUn0mtf1NJR/jdOL3ElONcTrFzxz3I7wPumDBAqF5+KqxfffdV1L59bXbqf+N24zY59xHU1d1pmoHN/Rqz6J4VVsc1yJKJd9jDd24D+ftXH0p/TjuRbxSzN+bv8eYXIzrj5hgjP3HytXj9vrO/86fP7/Oa9C6xRr6vrLQ28/Yb+O6PK6j3f6KiVj/zeLnampqJNXdLsbtqNftPt7g7WXxioPi692GncqNdcmLfSNue1Jp/VT9YfdLfzavA4tjtQAbEweCAQAAAAAAAKAZkQhuAZyGSNUhjWd/KiUbLJWkbcwP6lGNYz1Sn9XymV+fLY5JpVRqt76zyvHsVaXlTtWlNJ/98pnh4gjcqB7F333RokWSpGHDhkmqXAvXZ5Ldrp1AiNzeGzq6d3G+MZlUqc6wVUpbpeowWkxVFl8fz2Kn6k3GPuj7cWTbuK7y9+Xv12nfmAQtLpNrppMIbl4vvviiJOnAAw+UlKcHGpIEirXmU9u21LYu9r9Yn9RiXymmoFJ1GFNXzPh+qi5krMnoNh7fH0dKL86/0lUrqfr3qT6cSilG9dUe9ueKNWRfeeWVstPCxjdv3jxJ0ptvvilJ2mabbSTlfa74ezlpFPtc7FMxGejXx5ReahsQt5FWX0I8vifW/ozTivuV7nupbWGqv8T5F59LvSeVokqlquIyp7Z1vh/T3cVp+bt76623yj6OTS9ua7yv6N9Eyn9r18l334vpWP+d5Pbt+6ltQUzrxjYa20GxH8T2GFOBcdrxb7bUVSlxfeHXpa40K3f1Tepv0piu9/5irEtvqZrCfr33J/179OnTp85ndx1uX21BfdPWL/7d9Y9//ENSnih3qt9Xgvbs2VNSPs6B35e62qvclSVuR14HxCunLKaMPa3YtmP/i/0ztX0q/q3p/8f64J6W+7L7WdxGuR/5M/n13g9JoVZwdeJAMAAAAAAAAABUOQ4EtwBOf8SzVPEsTmqE2VQKN5XOKJ61TdVkitP0Mvhfn4lNnbWKtbTi9FP3i9OKZ5lTCY/UqJ3xLLT/9fc9bdq0sp8ZrVNxReU6TjGhEFMRqXqfMZGQOosbEwuxbRYTUDENkkocpdphTCKlagGn0vblzubGdUoq0RH7VEzyVkolx3WblyEmbCTpr3/9a73TQtPwb/7YY49JknbbbTdJdWtVF8V1dtx2xXV5agTlOAp0TN7HBISXaenSpbWviSMjx+1jrMEfa69WGnU9btvi8+XSJZWWLSbHUrWU47zjuiV+Vn8/fn8xpeJ1otPWf/rTn0qmmapziY3Hv7PTiE4hxbqfxdfG2p1uE077xIRSpXS+pdJ9fn+5qzhSSd8oNTp7TL6navVX2n+srz5+pfEqGpoYjtvCmKSP33vxu/B36ESjE2vsezY9/xZOCzpVWGwfXk/26NFDUt3knH+/+PdQ3LZ4Het5xn4d21SsM1/c/sUrWlK1reO2Il61Fd8fk8RxG+42GtPQ5ZYhviZ+L16WOMZErA3ueft3SNUeLneQwvPyWAekGFu/uJ0p7u9J0uLFiyXlV7Htv//+kurW543tLnW/+Jj/pky1vdR2I5Woj7W93de9rol/MxWvLCmXEi7e9zTj8Rr/65rAfn727NmS8itpU+hD1YkDwQAAAAAAAABQ5TgQ3ALMmjVLUp4CSaV9Uj9EauTZeCYqpjDKTTsmL2KtRr/OZ89jksvL4PpoPrvlM8SpRFi5JEdMlVQaKTYmXWK6JJ79MtIY1aHYhgYMGCApb3c+E+r269qLsX3HGlKpuosWk3pWLk0X0xGWSvjGacREVqredjwznaqDVZxHXMb4+VLLnqpj7r7WrVs3SfmZeK83YvqkmAiuVKcKTcO/qWvFjhkzRlKeNiiOrGxxG5S6GiUmnVLpUyeHYptO9YliO4ptMVV7O6ZlY2I/bhP9uNcFqcRHuVRk7JMNTfXH11Xa9lWqwVxcNn/u3r17S5ImTZpUdpmw6fh3cV3u3XffXVLeNsvVvk1dURLHkrDY5mJyKVUPPi5jfbWBo7itsniFmftSqpZ1avuTSumWe02cZuq9qRRj6vlUmtmKV1B4O+h16IwZM+osL5rWxIkTJUkTJkyQlNeblfK/Y2Jt+phcdeLQ2zO/PvYpP+/3xxrCqeR7sQ3HBGGs/R2XLVVnvqH7oXGbEedbbpoxDZnab/T2OtYOjlfFOg0Zt+nen/T36v1NKa/T/OyzzwrVodLf6t4nffjhhyVJO+ywg6T82Iql/m7x+tn/Fp+Lf+vFNh+vuIxt2OJ+pZ93W47bzPquuvR6JzUuksdkeeGFFyTlf/f68a233lqS9Oqrr9aZdhFJ4OrGgWAAAAAAAAAAqHIcCAYAAAAAAACAKseB4GYUBwjxwAMLFiyQVHlwKd+vdHmtlRuYLXUJb7w0Pg4QlLpM3SoNDhTnX27ggXg/TrPSZUdxEA9frvXmm28K1afY7ufOnStJ6tu3r6S8RIkvHYqDDLh9x5IobjvxMvh4GV281D2WJylKXR6eKukSB/No6AA58TKqcpewp8pRxGVNXXobL4+NlzamSkGk1g9oefxb/u1vf5MkfexjH5OUXzZbFPuD18HxUjzf9yVssX3Fy1F9CWlqQNTipXyWGmQ1XppnqbIvqUFIU5eCx+1VcQcqVXrFYr+Il/mnSkLEzxC/H6+/unfvXnJfyi8v/POf/1yyjJRManq+dNO/k3/v4rat3GPF+3HAGz/uvub3p0qfWKqkUOwXxf+ntjmpweRSpUviv6nB5+J+aFFc98T+GPcfU/uX8X78jF7PFAfzkfJ+Vexr2267rSTpZz/7mSRp4cKFZT8Xmo7bye9//3tJ0ic+8Yna51yixevVt99+W1K+X2luU94OuT36t4/tN5aEiIPI1bed8GtS5cusUnmGuK3wPFKlZ1KlyIr/T5WIif04DkSX6ufuU3FQWPPjHvDPpQAk6ZJLLpGUf9dxYC1Uv0cffVSSdMghh0jKS4e4Hbn9uV+7hGBR3Adzv3HfjqUjXJIkNYijxYHJY+nOeHynuO8XS6nEQYLnz58vKS/zNXXq1DqfS8qPPQGbGgeCAQAAAAAAAKAZkQhuRvGLdVF7n4WOKafUD5E6kxTPEpVL+MRpxtRHfE9MDsezwzEl5bNn8fWpFEbxuVQKqtJgHxbPhDsl6rNoPrOO6lBsQ6+//rokae+995ZUt9C/k6puY+4rPkvr+24jfjyeBY5J1tQAPUWpFG1M41caQKRcMkOqm3xKva/ce1MD66QGLIh9zN+XE9g+w+7v058lDoLkqyHQ8vg384ASI0eOlFQ6gM6qVask1R2oxr9zTOPENh7Tu37e/axLly6S6m5HPH33y3LJcs879ru47Ytpo7is/mwxOZwaLM6K/S01YE9M8MZ/4zKk+rafj6+P94v7FjU1NZKkp59+uuzyo+lNmTJFkjR06FBJ5QeLiilYt6GYBE4N9hSTgKkEeNyfLHcVR2pbFe97meJ2I3XlWEwQRqltZfGxOK/YdyoN/maxn8d1Vxz4zopXwzzxxBOS8iQw6fvm5/7iVKmvyJSkY489VlI+wLT3a3w1jNtf/Dem8lNt0WI7TiVopTxp7mXwPIoDpRbFK1oqDdiaSjDG++VS+qn94bhvG6/WjGnjeIWe//V2y8lOX0Hhz3799dfXztuDAGLzEdu2k7DeZ3W7crtxEtiDybldFY8NxH3XuA/mfV+3QU87vt9/c1YaoDved8K4eBWrryzxNL0s3odzP0klgeO82P5s3jgQDAAAAAAAAABVjgPBLYgTV8OGDZNUt05grLMZ6/PGs9Kpek3Fs7ZxGrHmWio9ZfHssd8XzwSn6lHF6RTfE88SWzxznUpFxbNdPvu3cuXKssuM1q1cG419J5WeiMkNi23JYvv2+2L9xvrOtKaSwW6X8Ux0TPrGaafOLKdSV+VeE+eVSvL7+4yJT9+P9a1838/H79W1rNBy+Td0ymCfffapfS5eleJURKoWaKyFGLc3kRPBMXUXk0bFOp2pOsKpBGTc7jhx5T4da8FFMUlYX53wWG80buvi9jh1ZY2lrnjwfSdd3B+L39OLL75YskxsD5vfPffcI0n63ve+J0lasmRJ7XPuO26f8WqVuJ8YaxHGtGJcp6eSr1auLm9qX9PtO15JFq/iSvWt1NVeqXr55dYfjU02xr4Xv79Yc9Sv9zK5r/n32GabbWqnfe2115bMiyRWy+G2U0ySTp48WVLeZly7O44FEWuLelvjNhDXy3H9n7oiptzVYHH/MKZsY5vy67yMThTHFHJc78dke6XxIopSV6z48bgd8n5hHFsg7k/H/fY+ffpIyuvbP/DAA8llQvWLV2nYhAkTSp6PifPDDjus5PHi1WE+XuC26H4Ux3Dydtn/xn4a//aMfTt1lbT7Ybn6xX7PtGnTJOW1gf1v6vWpvyGxeeJAMAAAAAAAAABsBjb0QG8lHAhuIJ8NdULH9U0r1fyNyY2Y+LByZ4HiWd2YEI7vjWeyfeY7NXp5pcRGuTqnleqRpupspUaJ9rLsvPPOkqRXXnml5LOi+rj9utaiayrFutMxNRhTgBZTEDHFGxNPnn+xDadSwqmUX3xfqq/Gvpka9dyK80klgePyx36aqi0Va2z57LfXE64r66SMa3d5xG60XG5n7lPFRHBs97GGdkz+xrRDTPbG9HusdRtTeH6+3Ojq8cqamD6O28+4XU3V4q5Uc7hcrUUvb1yWVCoxpr1S6a9Y7zGmSJxwcR3M4cOH187r17/+dcm00fzcZpxO7Nu3b+1zsc3E9uvfOm4fUnV44/tjH4zJpXJ/MKRq+Ma+ZDG5FbdhcRkq1Skut61L7SfG1H3sp/H1qXVY3A+PSS/ff/7552unFddbaDn8u7nmp5Qn8m+44QZJedLOSVbX73TbcXrQbcQJ4vi3m+/H7WG8wipeUVWcl5chNS6FtwHeDvqqgpgIjn0njt+QqnNcFLdXcZ0S9w/cN5yQ9rKm9hv8upi69/rRSeBydcux+YnHQjxmjPkKM49N8pvf/EaSdMEFF0gqTQTHfbO33npLUr6ecNtcvny5pLwNxquy/DdP3G6ntqvud/6bqXfv3rXPeT1jDz74YMm8vCwRCWCUQyIYAAAAAAAAAKocB4JbEJ/tnDNnjqS8bs2KFSsk1T1bHGuVxRRVqhZiMfEQazvWV/epuIwx6RfPjsVEYTzrHBMi9dVRjK+JZ5fj54p15/y9uQYzqp/bqWvP7rbbbpJKz/RKeVtyci6mLFK1OGOfinWhYpqq+FhMFqXqqVVKIsb+kardlupz5T5XKmkVk1rx8zqhEUeut7gRccLtb3/7W51lJpnYsjkF5WSRVDfh535WHH25yM9XStHGRGHcjngdH+sZSnXTV7EWapxX7AOpkctTYv/yMhXTf7FvxgRUvBIhXuUSt9cx/entr793/0beTrvfPfvss7XziKNco/n597z33nslSd/5zndqn3MayG3C/dHJoThWROyTlrqaJSZeU7Xsy6XvUn0g7qumrgBoaCox9ptydfUr1b2PtYBjyj7WBvb7nezyNs/fv+fn/lRTUyNJuuuuu4TWo7hNcvu97777JEkHH3ywpPw3j3/nuP25dm3c14vtP/X3UbllMU8zjruSukLUr+vRo4ekfJsQty1xfzMua+wvxe1avCLH+9NxmxrHkohXbfp5b8fiesX757Nnz5YkXXfddWU/MyClE7BeR3td7rbvq4XjfpiU70MtXrxYUt2xnGKfjvtefn7hwoWS8u1HvNrLnFb2dsivl6QXXnih5PP5+Ev8uwtoiKY4EFz/X08AAAAAAAAAgFaPRPB6mjdvniSpf//+kuomd1KjfKdqisZEVPGxVJ3EyK/z2S4vo5fNNbEGDRokqXyNq6J4xrjcMlU6a+5pxGk5ITNz5kxJeQqKs8ebD7elN954Q1JeJ9pnaaOY/on9IdY0jKmrVGK4OM2YNk7VeIv1P1PLGmtNpZJb8UqBopiGimfE/R6fefY8YrLTz8cEjPuin1+wYIEk6amnnir72dByuT05bS9Je+yxh6S8Bm1sg/FqFovJ8pjOS41kHrcrThA54VFchng/NRq6ed5eR8QEVdxuxH4a+05x+k53pGoFp+rix3nEdUgq1RlTinPnzpUk3X///bXzYHvY8pW7SiImAv37ud26bcUrpGK6Nm5//LpUH7Riu461e2P7TNUEj+nCuMyxrn78LDFpWNy2xe1dpdR9qtavP4O/V6cSnXp0vUmPL7D99ttLkq6//vqSZS5+HrQO3j451e2/dz7/+c9Lqtt+XcczbmtSY6nE9Xnq6rDidjO2oXilWfw7z9tEt1u/3u280rYjbtdirdPiNFO17+O6Kn4fMUHs9KP/nvT7Hn74YUnS3//+95LXU3MbDRGPHcT90TvvvLPB0/I+Vezz3hc1X9Ed/yby++MV3b6SxP118uTJkvI+IknPPPNMg5cTqITSEAAAAAAAAABQ5TgQ3AL5zJDTcnvvvbckaciQIZLys9Q+o+QzUD7LGs+SxhFdi2dyY5IjJpNSo5q7Ubhenf/12S2fyfVZrfjZUnV1pMqjOMeajjFN5lpYrvczderUssuO6uckwRNPPCEpTy7GOtvuO05JxDYX6/m63/h9TgXFOoLl0kAxLRmThrEvxvbqacYURCoBmUqhFOcV0x+x/8cEtRO+5u/Tn9vL7O/T09tmm20kSTfddJPQOvm3feSRR2of23XXXUueczuIdXJj209dtWLx/akEULmUe0wJxpqnMfHn7apfF5MdsU/ExGEque8+UO5zxdekapLHZLDvu196WWO9WP8OTmQ7CUxKsXW54oorav8/fvx4SdLSpUsl1U36+reO7THWDo5XjLgNxm2CpZLwxXnFdHGcl++nUreVEpL+rKn1QDHtm6pxXGnsibhPmkoWx2XeeuutJeVXoDk9ykjtrZ9/c19V6CuazjnnHEn5+tbrY6f33EZie3e7TV0x6X4SE/TF59wuvd2K9bgtJtpTdY1jX/R04vbLf9MV1w+ed0xFeh4xjex/fYWY+0pMDrsW8KxZsyTl+/FcxYL1kfpban32gXxFXL9+/STlbdx/C7oP+Cos75t5+xyvknM/c4J42rRpJfOpT9xuss1BY3AgGAAAAAAAAACqHAeCWyCfnXLi97HHHpMkHXLIIZLyxKtryfgMUhzVNSaB4/TLvSeqNFJ6rEfl6fjsl5fRZ71SI7AX0ysx9RRHrY1nlf16n4F76aWXJNWto5OqyYrq53SD28aYMWMk5YmEOKJ6TP/EGmbuQ64fGtO9MdVbFOsmRql0iJfB80wl5WOyOPaf4jI5gRH7fyqp4qR/HO3ZyxZTJk7KeDqxhjBar2L789Urw4cPl5RvuwYMGCAp30bFJGtch6fSTHGb5tc7DeX7xaRtTET6tTFRFRORfl+8aiBu61I1R+N2q/g9xYRjqv59fDyuj+L3FkdtdyrFn8E1F6mp2DoV189z5syRlCeR3JfcBlI1P1NpqLidiH3ObSXVb4rTile9xCtkLLbjuJ2JycmYmE+14+J0vD+Y2jbH9GGs3+0+FGsD++o3j93h52fMmCGptP42qkPsOx5z4nvf+54k6Tvf+Y6k/Ion91H3B7cd94OY+I11u2P/KW7XYurP7dHt1vOKV9F4H9f9In4m9w+/Ls471qkvtx9pXobUdt3P+33Lly8vef2vf/1rSfk22zYkwQlEcXtV7m+ySsdGfHVAU4p/67E/hw3BgWAAAAAAAAAAqHIcCG7BYmrCyeB99tlHUn4WdfDgwZLys6lO48bkUkzUSnVHjo5ne2MdKp8tdgJwyZIlJcvss9OulxbPXMXab/F5qW69qZhQiYkUv97TchI4NW9sfpy4ePzxxyXlI7069eOUvZOLMf3nBEPsB+5rTgXFVFW5RH0qWRX7Rkw7Ok0S637GxEes6RaXoZjO8EjnMZmVSkfFlGWsCezP5gSieXquDUyNt9avuD59/vnnJUl9+/Ytec7r5phYjWmlOMp6KtUYt2W+H0d/Lie+N24fY5rdz7tfxb6Rqm+cqrFafE+smRyTjhZHdvd99xtfHeDne/XqJSnfDm+//faS8n5HcqR1Kq4nb775ZknSkUceKUkaNGiQpDzpF8d58Hsr1QKNaShvf+LVIbHtSun626lUYZy3rxSJif+YxrfYF/1vcdsW9w/NyxRrgvt7cn/354y1wr3v4G2/x6B48MEHS+bLtq36uQ1deumlkqQvfOELkvK2sd1220nKr5Bx+3Qb9N9Rfn0cayJuJ6W6iWDvm8a/qdyOvYxxv9Ht1O07jucS63vH/criVXTxip3Yl/x4rGce+8jrr79e8rnj334kgbEp1HdsILU+jzX2U1e1NHR6lZSrEw5sDE1xILj+2gIAAAAAAAAAgFaPRPB6ivVffKbXI9faEUccISk/u+yz0D7r6sfLJWNjTSqfaYrJYNcG9Xs9EmY8u+X7PrPrhJLrqcUzWeVqFKdGeY61q2JKxCPPxs8CmNvGHXfcIUk66KCDJOVpCNd4c59zgiGmeGPiKdYKjjUOi++NycSY5Ih1dJ3EcMrW04wpq1iHO9YRtWK6JCYxY7o4jm4d1xcxTenvwd+nrwxwzTdqvFUntwOP8r3jjjtKyrc9sd6mtyexDm/sV7GWYqWkYLn693Ga/jeVhEwlH/36eGY8vj+meov3Y6rf30vc7sa0caqea0xreTquze204iuvvCJUB//2f/rTnyRJn/nMZyTl9bhjEjC2z7j/FNtrTNnHdX25fhPbq6W2o3GbFGthmz+L+0vq6paYQCz3Gs/byxTXJZ6G1zVOccZl8rK6jzkJTP3tzU+88uW2224ref7ggw+WlF8p4zbj7aDbpK9I83o8boNiGyzOO+6jperue33gq94GDhxY8rzF+v3l0vaRly9eqRPXHf7X38P06dMlSTNnzpRUd1wXoLk1NCnZ0MTk+iYrucIEmwqlIQAAAAAAAACgynEguBWJtd98/+GHH5aUn3312WXX/3TKMSYH4/+L4jycAPYZ39dee01S3cYRU48TJ06UJO23336S8tRKKoVVblpOGXoZfAbcn9N1ij1yPZAS04FPPPFEyfOnnHKKpLoJ2ZiycEIpVY/NGlK71NN0X/M0XLvN6UknlJYuXSop7+9O38b1Q6y17f5UHMHdid1Y4zGVTIwJlZiUjnUVX3755ZLPRBK4Ovn3nzx5siRp9913l1S3PbkdxJRSTBnG9uXn3XZjLWHfd5peqtt2UzXcYpLP00oloGIqPrb9WJO0mObytqpnz54l04hJsJis8uf0OsB93uktXy3g2sB+/fXXX1/2M6D1ivtcTtPtuuuukurWAnb9UYv17mPd3nilSuybbt/F7YjF7UisU+x9OS/j4sWLJeV1d+OVKObtiafj/uLPUK6eb+xDsQZ4HFsibsPj53SK0YnKWCucJPDmK+73uc94TApfIePtovvctttuK6nu1V7mK2qKCfpYqzfWAI7blLfffrvk9d6fdF9x//C2w+/z8zExH+v1F18b+3m8mtXL+vTTT0uS5syZI0maNGmSAABNjwPBAAAAAAAAAFDlOBDcCsVUXRyp+f7775eUpyxiXSqf+S0+F89EOzXiBJJTJ8uXL1+vZZ4yZYqkPHXhz+Az4cX0lRMsffr0kSQtW7ZMUl7ncMGCBWXnQf1RNFRsI27vbms1NTWS6qajnHxwWqhcyrb4eHEkZvcxJyn8npj6c7+NtUxj+45pC3Pd0XJ15eLrY/23VIoq/utUldcpTno4pe963TFxjc2DrxjZbbfdJOXbKK/vnXSKNT1jXd9Y+9b9z20/1mcr9jdvRzwPp5ScGo6J4FibPtbudh/x+93m/Tq3/dh3iskp18qPaeOYBI4prJjQ97y9nvJ9X3Ezfvz4ks9S36jYaN2cwj/yyCMl5alz1yW1VBtwX4zrfvc997XYF4vbl9TYEnGb5Wm7vcaa9DFBHK9AiTWC/Trvxxb7VayFHJcpte0rl3iUpFmzZkmSHnjggZLPAETeP3KbmjZtmqR82+E26O1jvMLDfTX+vSTV3fd0O/bfan7eyXX3b1/95Xk4je/27z7k/ce4PYx1jYvbbk/D8/ZVa97e/eIXv5CUp5MXLlwYvzIAQDPgQDAAAAAAAAAAVLmmOBDcRhLDHTahxiRjR4wYUfZxn5H2qK7rM20pT1WkGlG5+fu1rp/lGsgWU5r+l9QTGiu25379+kmSTjrppJLXOckQX+8EhNOHTl84yeR0hZQngP3eWO801mSMCeK4LH7eKUun9Z3sKKYjy02n+FgqmRnTkO5jMQ3pec2YMUNSPqJ9pf6P6nbaaaeV3Hdq1uklp3Rdz97tyPfdDuP9WFvY/a6YGHT6yq/xczG972l7WTyPWN/b/dT9zv3QiaxiXy/3vuKyeB0QawPHfubvIy6L7/v7dP964403JEl33XVXybJi83PRRReV3HcbcbLPbcjbrriud79IpfOL+4Axqev3xPYXr6CJ6wEn/v1+vz5ub2KK18tWXA7/3/3PnzcmnN1v4xganocT1r/5zW8k5X3V/7LfiYaK+0Nu726r3/3udyXlbc9Xbxb7kffv4jo/1rb2324DBw6UlPcVbxfdzp0UHjp0qKS8b3oZ4rYn1hKX8m2hx6P54Q9/KCnfLsdtLldvAkDz6tq1q1auXKnjjz++dl2d0rFjR915553q1q1byVgsDUUiGAAAAAAAAACaEaUhqpDPssaURrlE4NSpU+udls/cOvXQ2DO4bjyed6y/WN/8XVcrLgMpJ2wsMZnn+tNO1w4ePFhS3gZjXV+nJWKi1m212Of8Gp95c3Ijjrjsf2P/9bI6qdWzZ8+SZXe9UL8ujvIcawlLeeoj1oOM/dafx/96mf35fYaQJDCk/Pd/+eWXJUkHHHCApLw9uC+4P8Xat07CxpST25/vp7Z1Ut20rOcVU7i+72nHvh5rjXrern8YP3NM8hcTwTFB7+WPdcBjGsspMKc3vaxeRtcG/ulPf1rne8DmIfYBX52x6667Ssq3G07AOo0b26Lba9xOxe1HMSno7YH7q5fFr/F73Tdi0t3rg1g7O/Z3vz/WFI4p3eJ74vfibVX37t1LpunP4MS0/3USOPZJtm1orNhm3Mbcbi+++GJJ0ne+8x1J0vz58yWV9j23Q9fh9TbAqXpvI7bbbjtJ+X6i+7vn5X1cX1HmZXF/8HS8zHFciKIbb7yx5H6s9R2RBAaAzQcHggEAAAAAAACgmW3qE9scCG4mDaldVi5JVbSxztzGdGJD578xlwFIiWnBX//615Kkk08+WZI0bNgwSXWTshaTTOXarFMS/jcmizxvp6qcyIqpW3PiyfN2IsSvjynfmEguPudlislmf06nQGI6f86cOZLyJLD7M3UTN29u088//7wkae+995YkDRo0SFJeUzAm7GPfiEm/2LZjor2YUozJ+Fgr2G20Upt123f/qiTW3S7WUozpKr/Gfdnziulj82eICUonx0jib75i+73zzjsl5cnBQw89VFKe9IsJV/c9t9dU24s17qW8b7j9xaR7bLextn5Mz8f3x/nEK27KXe3m18R6907+mtP2rgVs119/fckyebsMbCzxKjC39x//+McNnsYhhxwiKd+vc3vfaaedJOXt1v18xYoVkvI+4ysvnRz2Nsj7m+6z7nt/+MMfksuSqisMAGiZKA0BAAAAAAAAAFWuKQ4Et5FEPAVAqxFTgieeeKIkaccdd5SUJ5ZiSsj3XY+xmFRyWiKmQGJ6wtN2wsPTcOrP04k1hl130WK615+pWOPNy+t6qjEV5vuup+gE8EsvvSRJeuaZZ0qmTRIYRTGh6qRTTL66HbqOYaxD6OSgk7+p2vXFK0xiW/QyxHrXfk9M8bsPO73cu3dvSXXrnMb3xT5UXI6YmEolIONVAu7j/jeOxn7llVcKqI+3XaeddpqkvI+5Vqi5zbndx3bu/vLWW2/Vvidui1auXCkp78+xnranMWvWLEl5KtepRD/vPpQaF8LLauWuxHFNZPeVmBTu1auXJOnmm2+WJL322msly8A2Dc3FbbRc4n19U7feL9xrr70kpa9ye/rpp+udTjGtv75jyAAAmkfXrl21cuVKHXPMMbV/Y6V06tRJd999t7p161bn6uSGIBEMAAAAAAAAAM2IRDAANNA3v/lNSflIy3EkZSejnCYsJpmclvJ7/JxTH3FF6+edtpg+fbqkPMHk6Tit6xpuMZXh+frx4gjUTod4XrG+qdNgTlP99Kc/lZQnvkhNoSFiO/m3f/s3SXmiL6bjnSQ0P+926rbs/lau/cX+49RxXBb/69e7v8Q+7GRhFNO7MXFYTCm7f8WEY6znHWsEO7Ef1xnjx48vmS61gVGJ2/EFF1wgKU/2ur07letaoq7r7W2Atx9+vVQ3Lex26MTvtttuKylvz26/U6ZMkZSn7WtqaiTV7c9eH8QrWLyd8nyL/cqPxXri/lz+Hq655hpJdWvw05fQEsW62OXSwvU9Hrd7Ke7npHwBoPo4EfzZz362QYnge++9d70TwZVHAwMAAAAAAACAZjR79uza1Gzx9vOf/3yTzfPSSy/VW2+9pTVr1ujhhx+uHbBekgYPHqzrr79es2bN0po1a/Taa6/pe9/7Xp3BdRuq3Gcrd9sQHAgGAAAAAAAA0KLtvffe6tevX+3t8MMPlyTdcccd6zW9Sy65RDfeeGPy+W9/+9v6+te/rrPOOkv77ruv3nnnHT344IO1VzeNGDFCbdu21Ve+8hXtsssuOv/883XWWWfpsssuW6/laYoDwZSGANCq+UzbDjvsIEn6l3/5F0n55bLxkleXiihebhEHulq2bFnJfV/W6std/XrPwwO1eYXsy2T79esnKS8R4UsC/X5fVutLZV1aQsov+/Nlsn6vl8mf25fPTp06teR1XD6LxohlGTxwlc92L1myRFLd9ug+EAd2i/2seEbcfdLTcNmJeBm527ovd4r9LjVYnKcTB7aycgP9eJm8nF5+/+tL4z0PDwrnvr106VJJqk0i0A/RWJXazDHHHCMpH8BtwIABkuqWfSi2a/ch9zE/523W1ltvLSkv6eDHvQ3s06dPyfOxL/l1cdBEzzcOHinVHUgubkf9Rxx9CAAANMSVV16po446SsOHD5f0z/3zyy+/XEcffbTat2+v559/Xueff37toOrRJZdcoiFDhtT+/RO99dZb+ulPf1pbirFbt25auHChTj31VP3+978v+55vfvObOvvss7X99ts3+HO4NMRRRx3VoNIQ999/P6UhAAAAAAAAAFS/du3a6aSTTtINN9xQ+9gdd9yhPn366Mgjj9Ree+2liRMn6tFHH609Ad4YQ4cOVf/+/fXII4/UPrZy5Ur9/e9/15gxY5Lv6969e+0J88ZqikTwlpVfAgAtl5NOr776qqQ84XT88cdLypN7Tvo51VtMKHoafsyJJaebPI04SIdXwE7jmlOEc+fOlZSnKPv37y9JtWcGPV2nrjw/qe7gWx4Ez2f87r333pJ5k57ChnBiz+0odXnUD37wA0l5ii8OtOg0vPtZuXbpfua2HVOGqWSvU7uel/uPB2OM7/M848B15RLBXiZ/Lq9H/Hic54svvihJeuyxx0qWnUEasb5iojfev/vuuyVJZ555pqT8ihIPGuf+VBxEytuLeHVLfO2iRYsk5YONDh06VFKe+HX79yClcQBHp1acVnafMz8u5QPVeZpxXcO2DAAANNQxxxyjHj166KabbpIk7bffftpnn33Up0+f2r9LvvWtb+mYY47R8ccfr+uuu65R0/cVvgsXLix5fOHChbXPRdtvv72+9rWv1Q5m31gNOdDLgWAAAAAAAAAAm40vf/nL+tOf/qT58+dLkvbYYw916dKltmybdezYsTaMtf/+++tPf/pT7XNbbbWV2rRpUxskk6SvfOUr+t3vftfo5RkwYID+/Oc/64477tD111+/Ph+JA8EA0FBOET311FMl/1544YWS8oSfk1FO6RYfcxrQZ/ecHPSK1ukqJ36dioqJ4Ib6whe+IClPVXm6Up6w8rxff/11SdIvfvGLkmmQnsLGFNuR25f/HT9+vKS8dvDYsWMl5fVK3WecLPS/xUSgX+PUsFOJ7oeuu+s+4HStE5Duh+7DrhXsFKL/jencWJu4XI3gmIx2beSRI0dKkr74xS+WfD9xHiSBsaFiH4z3/UeF25oHSIlJeylPy9vgwYMl5dscX2nilItrYXtb6L7o1zsR7P7xzjvvlLzeyWK/z7X4itu2Z555pmSZvD7w9pRtGQAAaIhtt91Whx9+uI499tjax7p06aL58+fr4IMPrvP6FStWSJKef/752n17Sfr617+ugQMH6jvf+U7tY943WrBggSSpb9++tf/3/UmTJpVMv3///nrsscf0t7/9rfYKrvXBgWAAAAAAAAAA+P+ddtppWrRokR544IHaxyZOnKh+/frpgw8+qA1SRevWrdPMmTNr7y9btkzdunUrecxmz56t+fPn67DDDqstDde1a1ftu+++uvbaa2tfN2DAAD322GP6xz/+odNOO22DDtQ2xYHgNpI49Q6gahRTflLdleQvf/lLSXm6UMpTfU41OVnlpKGTiX6PNxIPP/xw2XmnUpXx/vqkByt9PqApxCTsf/zHf0jKz567/qeTgTGZWHyv63e5/7mNx9rBL7/8sqQ88bjLLrtIyvvjiBEjJOXJYqcQnWKMdYCLdcL9eZxgdj3vCy64oGQZSOCjpYh9sFzS3Spta3r37i1J6tmzpyTppJNOkiTdc889kvJt4OTJkzdoGQEAADaGNm3aaPbs2brtttt00UUXlTz35JNPqmvXrvr2t7+t6dOna8CAARo7dqzuuusu/eMf/6gzrUsuuURDhgzRaaedVnZe3/72t3XhhRfqlFNO0ezZs/WDH/xAu+++u3beeWe9++67GjBggB5//HG9/vrrOuWUU0quzoq1hevTtWtXrVy5Up/4xCdq971SOnXqpIceekjdunWrvVqyMUgEAwAAAAAAAGjxDj/8cA0ePFg33HBDnec+/elP6z/+4z904403qnfv3lqwYIGefPLJRh2ULfrJT36izp0761e/+pV69Oihp556Sp/61KdqwyVHHHGEhg8fruHDh2vevHkl7y13gr4SEsEAsIFigs8JRdcnlaQf/ehHkvKao04inn322SXTcMJw+fLlZae9sZZVypNUnnb8F2jJvv71r0vKk4au/ynlfc/9zAnemAz2e3xG/I033pAk9enTR1Jey3vx4sUl83Y9cKcQXd/U0/F8XRdYymujunbYt7/97ZJpkgRGa1bpahU/3tDXAQAAYONyIvjwww9vUCL4kUceWe9EcNv1XUgAAAAAAAAAQOtAIhgAEvbff39J0lNPPbVJ50PaCtXGifbzzz9fkrT77rvXPudkfUwAOxnsJHDXrl0lSW+++aakvB8+/vjj9c77e9/7niSpV69ekvIEsKfbo0cPSdK0adNq37N06VJJKhn0AQAAAACaghPBhx12WIMSwY8++ig1ggEAAAAAAACgNaJGMAAAqCpHH320pLwm94EHHihJtSPsOp07fvx4SXm62DV/K2nXrp0k6f3335ckffKTn5SUJ+89nYceeqj2PU4j+znS+QAAAACaihPBhx56qN555516X9u5c2f95S9/IREMAAAAAAAAAK0RiWAAAFBVUglfJ3mdDG5oArih87ONNV0AAAAA2BicCD744IMblAh+/PHHSQQDAAAAAAAAQGvUFIlgDgQDAIAmk0rkuqZvU80PAAAAAFqSpjgQ3LbySwAAAAAAAAAArRmJYAAAAAAAAABoRpSGAAAAAAAAAIAqx4FgAAAAAAAAAKhyHAgGAAAAAAAAgCrHgWAAAAAAAAAAqHIcCAYAAAAAAACAKseBYAAAAAAAAACochwIBgAAAAAAAIAq1xQHgttu0LsBAAAAAAAAAC0eiWAAAAAAAAAAaEaUhgAAAAAAAACAKseBYAAAAAAAAACochwIBgAAAAAAAIAqx4FgAAAAAAAAAKhyHAgGAAAAAAAAgM3Ahh7orYQDwQAAAAAAAADQjJoiEdx2g97dBC666CJNmDBBixYt0tq1azV9+nRdeeWVqqmpqfPaNm3a6Fvf+pZmzZqltWvX6sUXX9QJJ5zQDEsNtD5HHHGErr/+er388sv64IMPNHv27ORr6WtAZXvvvbeuueYaPf/883rvvfcqbrC/9KUvafLkybXbunPPPbeJlhRo/U4//XQ9/vjjWrBggdatW6dZs2bphhtu0ODBg8u+nv4GrJ8bb7yx9o/U4m3KlCl1Xsv+IrD+yvUz3x566KGS19LXgOpRX98v3jZEi08E77XXXpo0aZJuv/12rVq1SjvttJPOOOMMjR07ViNHjtSaNWtqX/sf//Efuuiii/SrX/1Kzz33nI4++mjddtttyrJMv//975vxUwAt37hx4/T5z39eEydO1FtvvVXva+lrQGWf/vSndfrpp+ull17SrFmztOOOOyZfe+aZZ+qXv/yl7rzzTl1xxRU64IADdPXVV6tTp076yU9+0oRLDbROo0aN0uzZs3Xvvfdq+fLlGjp0qM444wwdddRR2mOPPTR//vza19LfgA2zbt06nX766SWPvf3223Vex/4isP5OOumkOo+NHj1a3/jGN+ocCKavAWisrLXdjj322CzLsuzzn/987WMDBgzI3n333ezqq68uee0TTzyRvfHGG1nbtm2bfbm5cWvJt/79+2dbbrllJim77777stmzZ5d9HX2NG7eG3fr06ZN16NAhk5RdffXVWfbPU7d1bh06dMgWL16c3XfffSWP/+Y3v8lWrVqV9ejRo9k/CzdurfG25557ZlmWZd/5zndqH6O/ceO2Ybcbb7wxW7VqVcXXsb/IjdvGv1133XXZhx9+mA0cOLD2MfoaN27VcevatWuWZVm22267ZUOHDq33tttuu2VZlmVdu3Zdr3k1qjTE4MGDN1k0uTHmzJkjSerRo0ftY0cffbS22mor/c///E/Ja6+99loNGjRIY8aMabLlAzZUc/S1+fPn64MPPqj4Ovoaqsmm7GuLFi3SunXrKr7ukEMOUU1NTZ0+dc0116hLly4aO3bsBi0H0FI09bat3P4i/Q2bg6boa23btlXXrl2Tz7O/iM1BU27XttpqKx133HF64oknNG/evNrH6WtAdWlxpSEWL15c5xKFdu3a6corr9R7770nSerYsaM6depUcVoffvihVqxY0eB59+rVS1tuuaWGDx+uH/3oR/rggw/0+OOP1z4/atQorV69uk59qmeffbb2+aeffrrB8wOaU3P2tUroa6gmLaGvjRo1SpL0/PPPlzz+j3/8Qx9++KFGjRql3/72t42eLtDSNEV/69mzp7bYYgttu+22+vd//3dJ0qOPPlr7PP0Nm4NN3dc6deqklStXqnPnzlq2bJluu+02fec739E777xT+xr2F7E5aMr9yE9/+tPaeuut62yj6GtAdWmKweIadSB4zZo1dVY8P//5z9WlSxcdccQRkqRvf/vb+t73vldxWnPmzNHQoUMbNN++fftqwYIFtffnzp2rcePGadq0abWP9e/fXwsXLqzzXteEGzBgQIPmBbQEzdXXGoK+hmrSEvpa//799cEHH2jx4sUlj7///vtaunQpfQpVoyn627x589ShQwdJ0pIlS/S1r31NjzzySO3z9DdsDjZlX5s/f75+8pOfaOLEiWrbtq0+9alP6atf/ar22GMPHXzwwfrwww8lsb+IzUNT7keeeOKJWrdune68886Sx+lrQHVpcQeCoy9+8Yv66le/qgsuuKA2nXvLLbfoqaeeqvjetWvXNng+y5Yt0+GHH64OHTpo1KhROvbYY9WlS5eS13Ts2FHvvvtunff6styOHTs2eH5AS9NUfa0h6GuoZs3R1zp27FibGonWrVtHn0LV2hT97cgjj1SHDh2000476aSTTlLnzp1Lnqe/YXO0MfvaxRdfXHL/97//vaZPn67LLrtMxx9/fO3AVOwvYnO0qfYju3btqrFjx+qPf/xjnYEZ6WtAdWmKA8HSehYy3mOPPbJ33nkn++1vf7vBRZG7deuW9e3bt/a29dZb1/v6MWPGZFmWZWPHjq197L777stee+21Oq/t2LFjlmVZdtlllzV78Wdu3Nbn1hx9rb7B4uhr3Kr1tjH7WrzVN1jc1Vdfnb3//vtln1u4cGH2u9/9rtm/G27cNvZtU/Y337bbbrtszZo12Ve/+tXax+hv3Da3W1P0tQ4dOmQffPBBdt1119U+xv4it83ttin72qmnnpplWZYde+yxdZ6jr3HjVh03DxY3YsSIbJtttqn3NmLEiA0aLG69EsE9evTQ//7v/2r69Ok6/fTTS57r3LlznbRuOR9++KGWLFkiSbrqqqt06qmn1j73+OOP65BDDkm+d8KECXrrrbd04okn6oEHHpD0z0sfyr2nf//+kqS33nqr4jIBLU1z97Vy6GuoRhu7rzXG/PnzteWWW6p3794ll6u3a9dOvXr1ok+h6jRVf5s1a5ZeeOEFnXjiibrmmmsk0d+weWmqvrZu3TotXbpUPXv2rH2M/UVsTjZ1XzvxxBO1YsUK3X///XWeo68B1aVFloZo06aNfvvb36pHjx46/PDD61zC8M1vfrPRNXB+8pOf6NZbb619bvny5RXf36FDB3Xv3r32/qRJk3TGGWdop512KimUvu+++9Y+D7QmLaWvRfQ1VJtN0dcaw31m9OjR+tOf/lT7+OjRo7XFFlvQp1BVmrq/dezYUe3bt6+9T3/D5qIp+1qXLl1UU1NTcnKF/UVsLjZ1X+vXr58OOeQQ3XTTTWVLG9HXADRWow8EX3LJJfrkJz+pI488UnPmzKnz/PrUwJkyZUqdUS6lf45Im2VZnZXpscceq549e5aM+HzPPffoyiuv1DnnnKOvfe1rtY+fddZZevPNN/W3v/2tIR8PaDGasq81Bn0N1WZT9LXG+Mtf/qKlS5fq7LPPLjkwdfbZZ+udd96pvfIFqAabor9tscUW6tq1a53R1vfee2/ttttu+t3vflf7GP0Nm4tN0dfat2+vdu3aafXq1SWvGT9+vNq2bas///nPtY+xv4jNxabejzzhhBO0xRZb1BmUzuhrQHVpcYngXXfdVePHj9eTTz6pPn366MQTTyx5/re//a1mz56t2bNnb9BC2fDhw/XII4/o97//vaZOnaqPPvpIo0eP1kknnaTZs2frqquuqn3tvHnz9LOf/Uzf/va31a5dOz333HM65phjdOCBB2rcuHH66KOPNsoyAU2hqfuaJO2222767Gc/K0kaNmyYunfvru9+97uSpBdffLH2UiT6GqrJpuxr2267rb74xS9K+mfaUFJtn3r99ddr0/nr1q3T+PHj9T//8z/6wx/+oAcffFAHHHCAvvjFL+riiy9er+Q+0BJtqv7WpUsXzZ07V7///e/16quv6p133tFuu+2m0047TW+//bZ+8IMf1L6W/obNwabqa/369dMLL7yg2267TVOnTpUkffKTn9TYsWP1pz/9Sffcc0/ta9lfxOagKf5mO/HEEzVv3rzaweci+hpQXVrcYHEHHXRQVp/GTKsht169emW/+MUvssmTJ2erVq3K1q1bl02bNi274oorsl69etV5fZs2bbILL7wwmz17drZu3brs5ZdfzsaNG9fsRZ+5cWvsran7mqTslFNOSc7vxhtvLHktfY1btdw2ZV+rb9qPPfZYndeffvrp2ZQpU7J169ZlM2bMyM4777xm/364cduYt03V39q1a5ddeeWV2aRJk7IVK1Zk7777bjZ79uzsuuuuywYPHlz2PfQ3btV821R9rXv37tktt9ySTZ8+PVu9enW2du3a7OWXX84uvPDCbMstt6zzevYXuVX7bVP/zbbDDjtkWZZll19+eb2vo69x49b6bx4sbtiwYVm/fv3qvQ0bNizLsvUfLK7N//8fAAAAAAAAAEAT6tq1q1auXKlhw4bVKcEUdenSRa+99pq6deumVatWNXpeja4RDAAAAAAAAADYeLKWViMYAAAAAAAAALBxcSAYAAAAAAAAAKocB4IBAAAAAAAAoMo1xYHgthv0bgAAAAAAAABAi0ciGAAAAAAAAACa2YYmfivhQDAAAAAAAAAANCNqBAMAAAAAAABAleNAMAAAAAAAAABUOQ4EAwAAAAAAAECV40AwAAAAAAAAAFQ5DgQDAAAAAAAAQJXjQDAAAAAAAAAAVLmmOBDcdoPeDQAAAAAAAABo8UgEAwAAAAAAAEAzojQEAAAAAAAAAFQ5DgQDAAAAAAAAQJXjQDAAAAAAAAAAVDkOBAMAAAAAAABAleNAMAAAAAAAAABUOQ4EAwAAAAAAAECV40AwAAAAAAAAAFS5pjgQ3HaD3g0AAAAAAAAAm7FzzjlHs2fP1tq1a/XMM89o7733bu5FKosDwQAAAAAAAACwHv71X/9VV1xxhS699FLtueeeevHFF/Xggw+qd+/ejZqOE8GVbhuCA8EAAAAAAAAAsB4uuOACXXfddbrppps0ZcoUnXXWWVqzZo2+9KUvNWo6TXEgmBrBAAAAAAAAANBI7dq101577aX//M//rH0syzI98sgjGjNmTKOm1bVr14oHert27bpey2kcCAYAAAAAAACARqqpqdGWW26phQsXljy+cOFCjRgxokHTeO+99zR//ny9+eabDXr9/Pnz9d577zV6WSUOBAMAAAAAAABAs3j33Xc1dOhQbbXVVg16/Xvvvad33313vebFgWAAAAAAAAAAaKQlS5bogw8+UN++fUse79u3rxYsWNDg6bz77rvrfXC3MRgsDgAAAAAAAAAa6f3339c//vEPHXbYYbWPtWnTRocddpgmTJjQjEtWHolgAAAAAAAAAFgPV1xxhW6++WY9//zzevbZZ/WNb3xDnTt31o033tjci1YHB4IBAAAAAAAAYD384Q9/UO/evfX9739f/fr106RJk/SpT31KixYtau5Fq6ONpKy5FwIAAAAAAAAAsOlQIxgAAAAAAAAAqhwHggEAAAAAAACgynEgGAAAAAAAAACqHAeCAQAAAAAAAKDKcSAYAAAAAAAAAKocB4IBAAAAAAAAoMpxIBgAAAAAAAAAqhwHggEAAAAAAACgynEgGAAAAAAAAACqHAeCAQAAAAAAAKDKcSAYAAAAAAAAAKocB4IBAAAAAAAAoMpxIBgAAAAAAAAAqhwHggEAAAAAAACgynEgGAAAAAAAAACqHAeCAQAAAAAAAKDKcSAYAAAAAAAAAKocB4IBAAAAAAAAoMpxIBgAAAAAAAAAqtz/B31+UZLkY2xvAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1390x320 with 8 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "所有图像已成功生成并保存！\n"]}], "source": ["# 1. 定义我们想要在 x, y, z 轴上显示的切片坐标\n", "x_slices = [-40, -20, 0, 20, 40, 60]\n", "y_slices = [-60, -40, -20, 0, 20, 40]\n", "z_slices = [-30, -10, 10, 30, 50, 70]\n", "\n", "plot_img = nib.load(field_brain_dir)\n", "\n", "# 2. 绘制并保存 X 轴方向的多个切片\n", "# display_mode='x' 表示沿着x轴切片\n", "# cut_coords=x_slices 指定切片的具体坐标\n", "# bg_img=None 表示不使用额外的背景图，直接在原图上绘制\n", "# draw_cross=False 表示不显示十字准星\n", "display_x = plot_anat(\n", "    plot_img,\n", "    display_mode='x',\n", "    cut_coords=x_slices,\n", "    title='X Slices',\n", "    draw_cross=False,\n", "    annotate=True,\n", "    bg_img=None  # 明确指定不需要背景图\n", ")\n", "# 保存图像\n", "display_x.savefig(os.path.join(field_QC_fig_dir, \"field_brain_x_slices.png\"), dpi=500)\n", "print(\"已保存 x_slices.png\")\n", "\n", "# 4. 绘制并保存 Y 轴方向的多个切片\n", "display_y = plot_anat(\n", "    plot_img,\n", "    display_mode='y',\n", "    cut_coords=y_slices,\n", "    title='Y Slices',\n", "    draw_cross=False,\n", "    annotate=True,\n", "    bg_img=None\n", ")\n", "display_y.savefig(os.path.join(field_QC_fig_dir, \"field_brain_y_slices.png\"), dpi=500)\n", "print(\"已保存 y_slices.png\")\n", "\n", "\n", "# 5. 绘制并保存 Z 轴方向的多个切片\n", "display_z = plot_anat(\n", "    plot_img,\n", "    display_mode='z',\n", "    cut_coords=z_slices,\n", "    title='Z Slices',\n", "    draw_cross=False,\n", "    annotate=True,\n", "    bg_img=None\n", ")\n", "display_z.savefig(os.path.join(field_QC_fig_dir, \"field_brain_z_slices.png\"), dpi=500)\n", "print(\"已保存 z_slices.png\")\n", "\n", "\n", "# 关闭所有打开的图像窗口（如果在Jupyter等交互式环境外运行）\n", "plotting.show()\n", "\n", "print(\"\\n所有图像已成功生成并保存！\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading volumes\n", "Phase loaded\n", "Magnitude loaded\n", "Mask loaded\n", "Rewrapping phase range to [-pi,pi]\n", "Number of phase splits = 8\n", "Calculating starting matrices (2149 by 2149)\n", "Finished connection_matrices\n", "6330 constraints left\n", "1378 constraints left\n", "160 constraints left\n", "Did while loop 2148 times\n", "Done. Created /share/home/<USER>/data/our_nsd/bids/sub-04/ses-01/fmap/field_output/fmap_rads for use with FEAT.\n"]}, {"data": {"text/plain": ["0"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["## Create fmap_rads.nii.gz file\n", "## This is a necessity for the fieldmap correction step.\n", "## This should be done after you get a good skull-stripping result, at least in the prefrontal area.\n", "# Usage: fsl_prepare_fieldmap <scanner> <phase_image> <magnitude_image> <out_image> <deltaTE (in ms)> [--nocheck]\n", "# <deltaTE> is the echo time difference of the fieldmap sequence - find this out from the operator\n", "# (defaults are *usually* 2.46ms=long echo time of mag.nii.gz-short echo time of mag.nii.gz on SIEMENS)\n", "cmd = f\"fsl_prepare_fieldmap SIEMENS {field_pha_dir} {field_brain_dir} {field_fmap_dir} 2.46\"\n", "os.system(cmd)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bed7f4c29f9f43eb8f9e7804bcb9f450", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=52, description='i', max=105), Output()), _dom_classes=('widget-interact…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "144c7dbc38584c1aa829156dacbb598c", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=52, description='j', max=105), Output()), _dom_classes=('widget-interact…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b66b4287b0bc488fab02fe85b534f954", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=34, description='k', max=68), Output()), _dom_classes=('widget-interact'…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.show_slice_2(k)>"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the Nifti File\n", "res_img = nib.load(field_fmap_dir)\n", "\n", "# Convert the image data as a NumPy array\n", "array = res_img.get_fdata()\n", "\n", "interact(show_slice_0, i=(0, array.shape[0]-1))\n", "interact(show_slice_1, j=(0, array.shape[1]-1))\n", "interact(show_slice_2, k=(0, array.shape[2]-1))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["已保存 fmap x_slices.png\n", "已保存 fmap y_slices.png\n", "已保存 fmap z_slices.png\n"]}, {"data": {"image/png": "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****************************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", "text/plain": ["<Figure size 1630x230 with 8 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1390x300 with 8 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABYIAAAFUCAYAAACZREg5AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8ekN5oAAAACXBIWXMAAA9hAAAPYQGoP6dpAAB2PklEQVR4nO3deXxU1f3/8XcIIQmBBCUswQWiSF2oC4qKu5WKW5W2al0pflGrRQtaFaut1WpLsdqqxaqVKq1iaStuD7VWa8WlKi0UFGtL0bJYkE1IQghZIPn9we8MMydz5t47+9y8no8HjzB37nImuWfunTPv+7lFHR0dHQIAAAAAAAAAhFa3XDcAAAAAAAAAAJBZDAQDAAAAAAAAQMgxEAwAAAAAAAAAIcdAMAAAAAAAAACEHAPBAAAAAAAAABBy3dO1opUrV2rDhg3pWh18qK6u1p577pnrZgAAAAAAAADIc2kZCF65cqU+97nPqbm5OR2rg09lZWVasmQJg8EAAAAAAAAAEkpLaYgNGzYwCJwDzc3NpLABAAAAAAAAeKJGMAAAAAAAAACEHAPBAAAAAAAAABByDAQDAAAAAAAAQMgxEAwAAAAAAAAAIZeVgeBly5apo6PD97/Bgwf7XveXv/xlvfDCC1q7dq1aW1u1fv16ffDBB/rNb36jCRMmqKSkJG5bbB0dHVq2bFnKrxUAAAAAAAAA8k33bGzkySefVHV1dcJ5Ro0apc997nPavHmz6uvrfa13xowZmjBhgiTpH//4h15//XUVFRXpgAMO0MUXX6yLL75Yzz//vNauXZvyawAAAAAAAACAQpWVgeDrr78+4fPDhg3T/PnzJUnf+MY3VFdX57nOL3/5y5owYYLq6up02mmn6Z133ol5fvDgwZowYYJaWlp8tXHfffdVW1ubr3kBAAAAAAAAoJBkZSA4kbKyMv3hD39Q79699ctf/lK//e1vfS331a9+VZI0ffr0ToPAkrRixQrdcsstvtuxZMkS3/MCAAAAAAAAQCHJ+c3ifv7zn+vAAw/U+++/r0mTJvlerl+/fpKk9evXp6UdiWoE77vvvpoxY4aWLVum5uZmrV27Vm+99Za+/e1vq7i4OGbe4uJiXXHFFXr77bdVX1+vpqYmLVy4UJMmTeo0ryRVV1dr6tSp+uc//6nNmzerrq5OS5Ys0a9//WuNHDkyLa8NAAAAAAAAQNeW04HgCy64QJdeeqk2b96sc845R83Nzb6X/eSTTyRJ48aNU0VFRaaaqLPPPlsLFy7UhAkT1NTUpKeffloLFizQHnvsobvuuku9evWKzFtWVqaXX35ZDzzwgIYNG6Z3331Xr7zyimpqanTPPfdozpw5Kioqiszfq1cvzZs3TzfeeKN69eqlV155RS+//LI2bdqk8847T6eddlrGXhcAAAAAAACAriNnpSGGDRumBx98UJJ05ZVX6j//+U+g5R955BGNHz9ehx56qP773/9qzpw5evvtt7VgwQL961//Sksbhw4dqt/85jcqLi7WBRdc0KlsxRe/+EVt3bo18viuu+7SF77wBc2ePVvf+MY31NDQIGnHgO/s2bN11lln6fLLL9dDDz0kaccg81577aVnn31WX/7yl9XR0RFZV3V1tQYMGJCW1wEAAAAAAACga8tJIji6LvCMGTM0a9aswOt4++23ddFFF2nDhg3q37+/rrzySj322GP68MMP9emnn+pHP/qRKisrU2rnNddco/Lycs2YMSNu7eJXXnlFra2tknaUqrjsssu0cuVKXXLJJZFBYElqbGyM3LjuyiuvjEw35S3+8pe/xAwCS9KGDRv0z3/+M6X2AwAAAAAAAICUo4Hg++67TwceeKAWL16sq6++Oun1zJ49W4MHD9aFF16oRx55RIsXL1Z7e7sGDhyo73znO5o/f35ksDUZo0ePlqRIgjeRE044QT169NBLL70Ut8TF2rVrtXTpUn3+859XWVmZJGnBggWSpOuvv15f+9rXYspMAAAAAAAAAEC6ZH0g+Pzzz9dll12mxsbGwHWB42lqatITTzyhCRMm6MADD1RNTY1uvPFGNTU1aZ999tEPf/jDpNe9xx57SJI+/vhjz3mHDBkiSbr88svV0dER99/w4cPVrVs37brrrpJ2JIF/+tOfatCgQZo9e7Y2btyod999V7fffrtqa2uTbjcAAAAAAACQr6ZOnaqRI0eqd+/e6t+/v8aOHaslS5YkXGbmzJkqKiqK+WfClpl0//33a8iQISorK9MRRxyhv/3tbzHPf+Mb39Dee++t8vJy9evXT2eddZb+/e9/Z7xdychqjeBhw4ZF0rVXXnml5x84GevWrdO0adPU1NSk++67T6effnratxFPt247xtQXLlyo9957L+G8LS0tkf9/+9vf1kMPPaSzzjpLo0eP1tFHH60jjjhCN9xwg84//3w99dRTGW03AAAAAAAAkE2vv/66Jk6cqJEjR2rbtm266aabdPLJJ+vDDz9URUWFc7nKysqY8cSioqKU2jFz5kzNnDlTc+fOjfv87373O1177bV68MEHdcQRR+iee+7RmDFjtGTJEvXv31+SdOihh+rCCy/UnnvuqY0bN+rWW2/VySefrGXLlqm4uDil9qVb1gaCS0tL9fvf/169e/fWI488oscffzyj2/vLX/4iacdN15L1ySefaNiwYdp77709B3f/97//SZLeeustfetb3wq0nf/85z/6yU9+op/85CcqLS3VVVddpbvuuksPPPAAA8EAAAAAAAAIlZdeeinm8cyZM9W/f38tWLBAxx13nHO5oqIiDRw40Pl8S0uLbr75Zv32t79VXV2dhg8frmnTpumEE05Iqp0//elPddlll+mSSy6RJD344IN64YUX9Mgjj+jGG2+UtKM6gDFkyBDdcccdOuigg7R8+XLtvffeSW03U7JWGuLnP/+5DjroIH3wwQe66qqrMr69oUOHSpJWrVqV9Dr+/Oc/S4r9g7q89tpr2rZtm8444wx17578+HpLS4vuvvturV69Wv3790+pxjEAAAAAAACQ7+rr6yUpUk7VpbGxUYMHD9Yee+yhs846S//85z9jnr/qqqv0zjvvaPbs2Xr//fd1zjnn6JRTTtHSpUsDt6m1tVULFiyI3ENM2lERYPTo0XrnnXfiLrNlyxY9+uijqq2tjZSc9aO5uVkNDQ2+/qVSZjcrA8GmLvCWLVt07rnnauvWrSmvc8aMGbr55ptVU1PT6blhw4bp7rvvliQ9+eSTSW/jnnvu0datW3XZZZfp3HPP7fT86NGj1aNHD0nS6tWr9cgjj6i2tla//e1vI/HwaHvvvbe+8pWvRB6fddZZOuKIIzrNN2LECA0YMECbN29WXV1d0u0HAAAAAAAA8ll7e7smT56so48+WsOHD3fO97nPfU6PPPKInn32WT3++ONqb2/XUUcdFblKf+XKlXr00Uf1hz/8Qccee6z23ntvXXfddTrmmGP06KOPBm7Xhg0btH37dg0YMCBm+oABA7RmzZqYab/4xS/Uq1cv9erVS3/84x/1yiuvRMYMvTQ3N6u2tlZVVVW+/tXW1iY9GJzx0hB9+vSJ1AVetWqVbrjhBs9lfvzjH3vWD+7bt68mTJig2267Tf/85z/1n//8R9u3b9fgwYM1cuRIFRcXa968ebr99tuTbvvSpUt1ySWX6De/+Y1+97vf6ZZbbtH777+vqqoqDR8+XHvuuaf69Omj1tZWSdKkSZM0ZMgQnX322TrllFO0aNEirVy5UhUVFdp///21zz776JlnnomUezjhhBM0efJk/e9//9PChQvV0NCgQYMG6dhjj1VxcbG+//3vq62tLen2AwAAAAAAAPls4sSJ+uCDD/TWW28lnG/UqFEaNWpU5PFRRx2l/fbbTw899JBuv/12LV68WNu3b9ewYcNilmtpaVHfvn0l7Rgs3n///SPPbdu2TW1tberVq1dk2k033aSbbrop0Gu48MIL9cUvflGffvqp7rrrLp177rn661//6utmdq2trVqzZo1WrlypysrKhPM2NDRozz33VGtra1I3ysv4QHBVVZV69+4taUdS1/5jxDNz5kzPgeCJEyfq+eef15gxY7T//vvrpJNOUq9evbRp0ya99tpr+sMf/qBHHnlE27ZtS6n9v/vd7/Thhx/q+uuv14knnqivfvWr2rRpk5YuXap77rlHjY2NkXmbm5t16qmn6sILL9TXv/51HXzwwTr88MO1fv16rVixQo899phmz54d8zq3bdum4447Tocffriqqqq0Zs0avfjii7r33nsjdY4BAAAAAACAsLnqqqv0/PPP64033tDuu+8eaNmSkhIdcsgh+uijjyTtKBtRXFysBQsWdLpJmxnoHTRokBYtWhSZ/tRTT2nOnDmaNWtWZJopT1FdXa3i4mKtXbs2Zl1r167tVKfYpHX32WcfHXnkkdpll1309NNP6/zzz/f9enr37h0ZQ3Xp6Ojwvb54Mj4QvGLFipTv4BfP6tWr9atf/Uq/+tWvAi1XW1sbd3qiNi5evFjjxo3ztf729nY99thjeuyxxzznfe+99zxvQgcAAAAAAACESUdHh66++mo9/fTTmjt3rnO8LpHt27dr8eLFOu200yRJhxxyiLZv365169bp2GOPjbtM9+7dI/cVk6T+/furvLw8ZprRo0cPHXrooXr11Vc1duxYSTvG/V599dWE9z/r6OhQR0eHWlpaAr0es5zXPKnI+EAwAAAAAAAAABgTJ07UE088oWeffVa9e/eO1NytqqpSeXm5JGncuHHabbfdNHXqVEnSD37wAx155JEaOnSo6urq9JOf/EQrVqzQpZdeKmlHJYILL7xQ48aN0913361DDjlE69ev16uvvqoDDzxQp59+euB2Xnvttfr617+uww47TIcffrjuuecebdmyRZdccokk6b///a9+97vf6eSTT1a/fv30v//9Tz/+8Y9VXl4eGaDOJwwEAwAAAAAAAMiaBx54QNKO+2dFe/TRRzV+/HhJO+r5duvWLfLcpk2bdNlll2nNmjXaZZdddOihh+rtt9+Oqfn76KOP6o477tC3v/1trVq1StXV1TryyCN1xhlnJNXOr33ta1q/fr1uueUWrVmzRgcffLBeeumlyA3kysrK9Oabb+qee+7Rpk2bNGDAAB133HF6++231b9//0DbykYiuKgj1TVI+sc//qFDDz001dUgCQsWLNCIESNy3QwAAAAAAAAAATU0NKiqqkobNmzwdbO46upq1dfXe84bD4lgAAAAAAAAAMghagQDAAAAAAAAQMgxEAwAAAAAAAAAIcdAMAAAAAAAAACEHAPBAAAAAAAAABByDAQDAAAAAAAAQMhlYyC4W0pL/3/V1dUqKytLx6oQQFlZmaqrq3PdDAAAAAAAAAB5Li2J4D333FNLlizRhg0b0rE6+FRdXa0999wz180AAAAAAAAAkIKCKg2x5557MigJAAAAAAAAAAEV1EAwAAAAAAAAACA4BoIBAAAAAAAAIOQYCAYAAAAAAACAkGMgGAAAAAAAAABCjoFgAAAAAAAAAAg5BoIBAAAAAAAAoAtIdaDXS7eMrh0AAAAAAAAAkHO+E8FTpkzJZDsAAAAAAAAAIMa0adNy3YSsoDQEAAAAAAAAAIQcA8EAAAAAAAAAEHIMBAMAAAAAAABAyDEQDAAAAAAAAAAhx0AwAAAAAAAAAIQcA8EAAAAAAAAAEHIMBAMAAAAAAABAyGVjILhbSksDAAAAAAAAAPIeiWAAAAAAAAAAyCFKQwAAAAAAAABAyHWJgeA+ffqoZ8+eCedpampSXV1ddhqELs3P/mgU2n5JX0M+CdLXpMLbN+lvyBf0tcJ7TShcnEcW1mtC4Qrzsc3vayuk14TCFea+lq9CPxDcp08fXXfddSopKUk4X1tbm+666y52KGSU3/3RKKT9kr6GfBK0r0mFtW/S35Av6Gs7FNJrQuHiPLKwXhMKV5iPbUFeW6G8JhSuMPe1fBb6m8X17NkzslPNnDlTRUVFmj9/fqf5SkpKAn0LASTD7I9mXzT/unfvrt12203jx4/XqlWrIvMX0n5JX0M+ibc/hrG/JeprUmG9JhQm+toOhfSaULg4jyys14TCFeZjm9++JhXOa0LhCnNfy2dmINjrXypyXhoCyFc/+MEPVFtbq+bmZr377ruaOXOm3nrrLX3wwQcqKyvLdfOAUKG/AdlBXwOyg74GZA/9DcgO+lrmhb40BJDPTj31VB122GGSpEsvvVTV1dWaNm2annvuOZ177rk5bh0QLvQ3IDvoa0B20NeA7KG/AdlBX8u80JeGAArJscceK0n6+OOPc9wSIPzob0B20NeA7KCvAdlDfwOyg75WmEgEAz4tX75ckrTLLrvktiFAF0B/A7KDvgZkB30NyB76G5Ad9LX0ozQEkEP19fXasGGDmpubNW/ePN12220qLS3VGWeckeumAaFDfwOyg74GZAd9Dcge+huQHfS1zGMgGMih0aNHxzweMmSIHn/8ce2+++45ahEQXvQ3IDvoa0B20NeA7KG/AdlBX8uOVAd6vTAQDDjcf//9GjZsmOrr6/XII4/ojTfeUGlpaa6bBYQS/Q3IDvoakB30NSB76G9AdtDXMo9EMJBDhx9+eOSOmGPHjtUxxxyjCy64QEuWLFGvXr1y3DogXOhvQHbQ14DsoK8B2UN/A7KDvpZ52RgI7pbS0kAXUVxcrKlTp2r16tWaPn16rpsDhBr9DcgO+hqQHfQ1IHvob0B20NcywwwEe/1LBQPBgE8nnHCCDj/8cN1zzz1qbm7OdXOAUKO/AdlBXwOyg74GZA/9DcgO+lr6ZWMgOO9KQzzyyCN66aWXOk0vKSnJQWuAWNdff73OOecczZw5U1dccUWum5MS+hryXVj6m6uvTZo0KQetATqjrwHZEZa+JnEeifwXlv7GsQ35Lix9LV90yRrBDzzwQNzpt956a3YbAsTxla98RXvvvbfuuusuXXbZZbluTkroa8h3Yelvrr42fvz47DYEcKCvAdkRlr4mcR6J/BeW/saxDfkuLH2tK8mb0hDjx49PGHvu06dPrpuILsLsi6YIerRu3brpo48+0kcffaTi4uIctC519DXkkzD3N6++tvvuu+e6iehC6GtAdoS5r0mcRyK/hLm/cWxDPglzX8s3XbI0BAAAAAAAAAB0JV2yNAQAAAAAAAAAdCUMBAMAAAAAAABAyGVjIDinNYKbmprU1tbmOV9bW5uampqy0CJ0ZX73R6OQ9kv6GvJJ0L4mFda+SX9DvqCv7VBIrwmFi/PIwnpNKFxhPrYFeW2F8ppQuMLc1/JZNmoEF3X4XMOUKVNS2pBLnz591LNnz4TzNDU1qa6uLiPbB6L52R+NQtsv6WvIJ0H6mlR4+yb9DfmCvlZ4rwmFi/PIwnpNKFxhPrb5fW2F9JpQuPKpr02bNi0j680XDQ0Nqqqq0ptvvqlevXolnLexsVHHHnus6uvrVVlZGXhbOS8NUVdXxxsY8kaY98cwvzYUnrDvj2F/fSgcYd8Xw/76UFjCvD+G+bWh8IR5fwzza0PhYX/MPmoEAwAAAAAAAEDIhb5GMAAAAAAAAAAg80gEAwAAAAAAAEAOURoCAAAAAAAAAEKOgWAAAAAAAAAACDkGggEAAACkXVFRUdzpqX64AAAAQHIYCAYAAAAAAACALiDTX8ozEAwAAAB0Edu3b5ckFRcXxzw2CeFu3brFXc7+UGLmb29vjzu/az0AAACIj0QwAAAAAAAAAIQcA8EAAAAAEkr0gcAkdktKSiRJ5eXlkqSysjJJ0rZt22Lmb2hoiLses5zZlkn8muVNQtj8tNcbra2tLe4yQFdi9ntX//X6oE+/AQAkg4FgAAAAAAAAAMghEsEAAAAAYpgPACbt26tXL0k76/1KndOGpaWlknYmg83zJulrmHWY5cx8PXr06LSN6Om2RIlgkyY263LVGQbCyO6/NtPnTB3v7t27xyxntLa2OrdBWhgAClM2BoK5iwMAAAAAAACArPnkk080Z84c3X///brzzju1dOlS38v+9a9/Vffu3XXwwQdnroH/3x/+8Aftu+++Kisr0+c//3m9+OKLMc/feuut2nfffVVRUaFddtlFo0eP1rx585LalhkI9vqXCgaCAQAAgAJgTv7LyspUVlam8vJylZeXq1evXurVq5d69uwZ+dejRw/16NFDpaWlkTSwtCOFu337drW3t6u9vV1tbW1qa2vTtm3btG3btsg6zT+zPlu3bt3UrVu3yHrsf0VFRSoqKor50GKeKy4uVnFxsbp37x5JO0Yz8wGFxPVh3e4bZv+vrKxUZWVlpP+avmr6hetfSUmJSkpKEs5j9z8AyEdtbW3q37+/vvjFLwZarq6uTuPGjdNJJ52Uchvmzp2rIUOGOJ9/++23df7552vChAlauHChxo4dq7Fjx+qDDz6IzDNs2DBNnz5dixcv1ltvvaUhQ4bo5JNP1vr16wO3h4FgAAAAAAAAAKGy11576dhjj9WwYcMCLXfFFVfoggsu0KhRozo9197erqlTp6q2tlbl5eU66KCD9OSTTybdxnvvvVennHKKrr/+eu233366/fbbNWLECE2fPj0yzwUXXKDRo0drr7320gEHHKCf/vSnamho0Pvvvx94e9kYCKZGMAAAAJDHXDVDTa3dLVu2eC5r5jXMhwgz3Ty2P1yYWr9mPXaC17W8SfRG1yo10+zawGYddgrYrlMM5FqifTJeuj2a2c/NfGVlZTHram5ultS5z7n6hanPHa/vtrW1xayDvgQgLB599FH997//1eOPP6477rij0/NTp07V448/rgcffFD77LOP3njjDV100UXq16+fjj/++MDbe+edd3TttdfGTBszZoyeeeaZuPO3trbql7/8paqqqnTQQQcF3h43iwMAAAAAAADQpS1dulQ33nij3nzzzbhfvrW0tOhHP/qR/vznP0fSwnvttZfeeustPfTQQ0kNBK9Zs0YDBgyImTZgwACtWbMmZtrzzz+v8847T01NTaqpqdErr7yi6urqwNtjIBgAAADoYuz0np3iM8lB8yEo3nyuZV3SPV+iZe112NNN8tkkh+3Eo3keyBY7WZ8oEWw/Z5YxP83+29LSEvPYbyLelb6PHhiw2+vqS/broZ4wgHy1fft2XXDBBbrtttucpSQ++ugjNTU1dao53NraqkMOOSTyuFevXjHrbWlpiZl20UUX6cEHHwzUvhNPPFGLFi3Shg0b9PDDD+vcc8/VvHnz1L9//0DrYSAYAAAAAAAAQJe1efNmzZ8/XwsXLtRVV10laccXYR0dHerevbtefvllVVRUSJJeeOEF7bbbbjHLR984d9GiRZH/z5s3T1OmTNHcuXMj0yorKyP/HzhwoNauXRuzrrVr12rgwIEx0yoqKjR06FANHTpURx55pPbZZx/96le/0ne+852UXncmMBAMAAAAAAAAIC9VVlZq8eLFMdN+8Ytf6C9/+YuefPJJ1dbWqr29XaWlpVq5cmXCMhBDhw6N/P9///ufunfvHjMt2qhRo/Tqq69q8uTJkWmvvPJK3BvVRWtvb49c+REEiWAAANCl2Cc2yV6Gzo1xUIjsG6y5buBmLiVP9EHAa993PW9v274ZnN8+ZS+XaB7X63bdRM7cCMu+aV68G9QB6WSXL4me5ip5Ypj92pRpMOzHXqUg7OlB2u0qK2H4eW8BgHRpbW3Vpk2bIo/r6uq0du1alZeXq7KyUq+//roaGxt1+umnq1u3bho+fHjM8v3791dZWVnM9Ouuu07XXHON2tvbdcwxx6i+vl5//etfVVlZqa9//euB2zhp0iQdf/zxuvvuu3X66adr9uzZmj9/vn75y19K2nHD3h/+8Ic688wzVVNTow0bNuj+++/XqlWrdM455wTeHgPBAAAAAAAAAEJlzZo1mj17duTxa6+9JkkaPny4TjvtNG3ZskUNDQ2B1nn77berX79+mjp1qv773/+qT58+GjFihG666aak2njUUUfpiSee0He/+13ddNNN2mefffTMM89EBp+Li4v173//W7/+9a+1YcMG9e3bVyNHjtSbb76pAw44IPD2sjEQXNThcw1TpkxJaUMAAAAAOif+7Bs52Tdwcv20b0IVvV7Xc67p9k/XRwRX2tZOGNo/o1+n/Zzrpleu6XaC0ryWkpKSTtsEkuF1Q0Ozz7mmxVvGbx/zSv7a/SNeYth14zm7L23bti1mPpMIJhkMIN9MmzYt103IqIaGBlVVVenpp5+O1Dp22bJli7785S+rvr4+pp6xXySCAQAAAAAAACCHKA0BAAAAhIRJAtqJVq8Teq8apK7no6d5JYBdy7keB21rom0HrQ1u1wQ2N2Mxj82dwUkGI1V2yjcRu46238Sv1/qSXT4eu22mzrbpK656xdTdBoDsYCAYAAAAAAAAAEKOgWAAANCluFJHrmSfq6aq/TyQS2a/NPU4Xak7v+lD1/4e/Tho0teVyvVKNwbht+ax3d/tNth1Ts3zW7dujXm+vLw87voAvyl0ex8Lsm7X8SjZdK3fxLEfrnWYPmjeo1w1xgEAmZPpzy+8kwMAAAAAAABAyJEIBgAAOeOVjLKTlDaTTjI/7cSU+UkiELlk9t+2tjZJ/hK98R4Hmc9O7rlqBBvFxcWSOqfsXY/tRKGr70W3w/RDv7WC/V4aaW+7qakpZnpZWVnC9aDrcO0zqaR1vdLFXtsKWiM7E1xtMclguy9xtQ3yXT70KyAZlIYAAAAAAAAAgJBjIBgAAHRJXomqqqoqSVJ9fb2kzvUMgXxiksCuRJJrv02Uro23vkTJYK962n7X6TdV5Wc+v1cEeCX67d9P9+47PuJs3rxZEongrsxOoWciyZrupGGmBwCCsPugOdbaVxAA+cLsk2YfdT1v2Pt4Jt8rAD8YCAYAAAAAAACAkGMgGAAAhJLfNKJr+l577SVJeu+99yTtrMFaWloqyZ34IOGBbLITRn551S11PU60Ha9lgvZJv30sUZtcdYW95rfX7VrevB+YRHZJSUnC9SM8zL5gEqyGVyLeXj6dUqlD7Ecy6/V7bDS/t5aWFkk7+xbJYOQL+6oXc2WIvW+6rjBx1azPdL8FbAwEAwAAAAAAAEDIMRAMAABCyXUC45reo0ePmMfr1q2TtDPxYZikBwkO5AOTRPUr2Xq8Qdh9zE5RuR4bQVPOiT6s+E0juhK/rkSwabtp69atWyWRCO5KzD5l17R1SaWvJXu8sROHrj7n93iZTF8L2nbzezTJYFN/m2Mtcs1OAtv3jvCqNW/4PcbFe55kPNKBgWAAAAAAAAAACDkGggEAQKi4EhlVVVWSdqaNzE8zv6kBbDQ1NUmSevXqFTPdJD8aGxslSc3NzTHTjegkh/0ckC5m/7XrkqarXq+fBLHXur0e22kqV2rRXj5R0jDZmt1+E1rZSFYjP5m/tTlmuN7fvepv2yldW/SxzJU29krbBkn0+nk+yLJeCWG735v5zWPzmltbWyXtrBkM5Iq5cszsw2YftZPBhn0+6qoRbF9pkoh9VRqQrxgIBgAAAAAAAIAcIhEMAAAKlp3Mip5mTmDslKH5aZIcffv2jVnOPG8SHnb6wkyvqamRJL3//vsx0+Mls+w0F0kOpMpvLUKbK0Fop3D9pBbtFLLhlYD0m7p1JQXt15IKV1tcaUWbnQhD+Jm/tanP7UrreqV0XX3OiO5zQevsuubP5LHHlQB29X9XW1y/F/tYDmSKq5+ZfdB1/A16ZYjdF1x9Jxrnj0gHBoIBAAAAAAAAIOQYCAYAAAUrSErq008/lbQzzWV+HnHEEZI61w7u37+/pM6JYPPT3DXaTgzGS4eR4EC6mH3JrmntJ1UY/bw9n99aw4nalGwtVK8UsisZ6KdNfvueV2rZ1TZXSpmaweFj/rauJLDfvpPMvuFK9rr2W7/pXHt9ru0G6XPpZv/eS0pKJJEMRnpF9zFzfmdz9QP7WOWVGHb1FXt6vPUkW/8eiMZAMAAAAAAAAACEHAPBAAAga/ym5fzUSQuyPmnnHcftNMX8+fNjHpvU0e233y5JamlpkSRt3bpV0s7kx9KlSyV1ToXFS5JkOinllbhEeDQ2NsY8Nvtf0L+9a58JUs/TpJVcNbCD9nfXtuxUVJBawX7TU+lK8Jq/T+/evdOyPuQPk8K3+4xX37NTul41tZPhVQs42RSh37q+yWzDrstv2P3bXq85JpeVlcU8D6QiXj/2W4vfb/3qTJwLchUKksFAMAAAAAAAAACEHAPBAAAg7UwywdThNcxjk5p11TO0E4ZmOT9JKnuandC121RZWZlwfpMQNsuZVNirr74qaWcqyfCbIEmG6/dpfprUs+su9tSUK1x2H7H7kNdy2UgL+a2v6/dO6a7awEGSh373eTshbfqUXQvY9drs5RB+futvuwTpk8mm/lJNArsep3ObQesZm+nmWGxqBgPJiLefuc45XfxekRO0RrCfddnLkgxGvmAgGAAAAAAAAAByiEQwACAQvnFGInYS2NTVNQlV89O+s7JJ9JjpPXr0iHneXm+iZLBJStrbNPPU1dXFrNtO8DU1NUmSHnjgAUmd7wZtEsCDBw+OeY0mndTQ0BC3jfGmuZJOdmrEpJJtdn80bSkvL4953v49o/C49n1XKtHeb/3WJXWlduMlmVzz2Ovym/RzPXb9NPt1dArftc2gyWC/spm4Rm7Y+4TpW159yquPec0XRJBavonaksoHf7/L2u9Nfut326+RRDDSId57eNB+kK4rwey+Ec2rv6TjXhroOhgIBgAAAAAAAICQYyAYQJeXiWRGGJlUpetb76B3iUc4mdTili1bJO1M59qpRbvfmWSPYSdgTQrXTDfzx9vfzDZNvVyTLjY/161bF7OM60Rnw4YNcaePGjVKktTa2hqzHnMnc/M7iK7T67UtV6rQ7m+uGqn278H8/u0awtXV1ZF1US+4sDQ2Nkpy9ylXjUK/tQsNr/fueIlg1zyu/d7er72SxK6fibbtd1m/6Sq7VrBrftPvo9/DSCwWNrt+tC1oMjgT50fJJvy9pKM2cNDX6+pr9vrjpSc594QXrxr2uZTo+JRs2p/PZXDJ9D7PQDAAAAAAAAAA5BCJYABdnleyrquy70zf3Nwc87xdI88knrhjetdmkqh2As4rERidno1+3uxnJn1rUr12Iih6fWZdZl6TJq6oqIj5WV9fn/C12HWLzb7++uuvS5JOO+20mO2ZxKZd3zh6WRc7aWYnnu31eJ2cuerGRievTd/OhxQM4ouXKrXT4141g23JHtv8HBvtfht0/3W9T9g1gV2P47UzU/u3K2GZT+kypJdX30n1+VT4/UCfzv3SKyWfbq4rB8zVONLO4z26Lq8rGL3u1ZBonlT7sNcVKJn4DNrVP88iPgaCAQAAAAAAACDkGAgG0OW43tRcKaqumuox36ibhOeqVatippv6q+Zxnz59JO1MGRpB61OiMJnkhUnjmOSp2T+86hSaJLnZf+xauOaxSQbbCWKzP0bPa7ZhJ4RPOukkSTuTvYbZVkNDQ9w2mp+VlZWSpPLy8pg2mZ81NTWSpLq6usi6Xe8j5vdkkp+uNGHQmnCuJMzGjRsj/991110lkQzOZ9F/E1fK25X2SbYuaZBEkms/S3Tn8+jpXrXDU9kn46WFo6d7sd97XFyvlf5U+FJN5/ntmy7xrnTxqldsbyPdCcNMpn9dNYC9tklf69rs80HD6/hii+4jmbp/TDquGPFKK3tdFcOVr5CyMxDMCAAAAAAAAAAAhByJYAAAAAAAAADIIUpDAOgyom+6E4+5RNtcgmYuIzc/jbBeYmPfHG7Tpk2SpNWrV0vaecm/fQMtc5MOUzpit912k9T5kn/7Jl8Il+ibkEnel0u7bnBl1mP2Q7P/2Jf+melmf4y+dNS+caEp2WAem3InY8aMkST17t1bklRVVSVJ+uUvf5nwtbpuLGl+NjU1dXqN9k3v7Ju3uR6bNrsuU3ddvmhfUmumR7+fmTIRlIgoLH5LQiR7WbrX5aXR+4drXfa89vuBvT97bSvIhxWvPpJo2XiP7b7oYubzulER8l+mbzjo97wx+tJ2r8u/7fmSbVM6eb2XJNv2ZMveIJwyeSO3TN7ELZ5k1u/qD67jrf2YEn5dEwPBAAAAAAAAABByDAQDCC2Tytm8ebMkd7LDpHzMjZ3sm1JVV1dL2nmDKJOaMz/Nek3iuNDTdOb38cknn0iSevXqJWnnN8Z2OspOdG3dujVmfjsJWVFRkdkXgJxwJQ78pijMcnYq1U7X2euJN91MM/uoSdmafdOwU+3Nzc2SdiaE7fkMs97GxkZJOxPA5iZz9o3f4k2zE9Sum+e52mAkSmsmmk/a+bv+7LPPJO18ryMdkj+i9yFXH/J70yjXjZhsftN7ifhNVNqpWa+EsSvtGy+l7NUngt7cze+Nq+g/4eFKh7v6RLIp/WTa5OpbQbcR9Jw1ev8OmnoPcrOu6PldfS+dv1cUHrNfeF2FkcrnsqBXgebDVaJ+zwOznXZGfmEgGAAAAAAAAABCjoFgAKFj3rTs+qD28+abTzslaL5hNjVx6+vrJUnr16+XtDPRWlNTE7OcXcO0UK1YsUJS52SmnXIyz5tko3ne1F81vw+7bqKZ39RjReGJTg3YydZkuerTetUwi3eSYqdCTBtN4tc8Nildk+g17wXl5eWSdr5Okxi222CuNjBJYLOeRLVEXbVP7RSz39SJV2ItETtNY5LB/fr181wW2RFvvzf7ivm7ud6jvdJ1flNAftK9dj+1+6BX2+z1uB67pke3yVXD22+NX3s9Xklqv21G4fJK/nolgbOZuPNKwKfahkT7t9962642eR3P/KbyEW52EjwTSWAvfq8KSLXfJVou2bSyYZ9LoGthIBgAAAAAAAAAQo6BYAChY74JXbt2bcL57NqidoLVpAbN9NLSUkk7E8b//ve/Y+Yzb5YHHnhgzHoKhUlamNSUSWjatbdcyWDXt+JeNV0LvaZyVxSdrPObsvP6e9vJYldCwV7eLGf2Q6lz3WqT6DXTe/ToEdN206dNQjh6XdHz24/N1QJm/fZ7QbyUol1b235s+E2Z2M+7EiF2CjTec6Ss8pv5m/pN17qW90oG2+/1ifqu33q89jpdNX9dvJKGiRLBqdYITjZ1T63gwhK9LyZ6v5S8+5zfxHAm+U0Fek03kjk+uPqK39+raz77mBXd18zx3D5uIzwyPXiVDq5zsXS0zW9dcK/UcqF9TkV6ZWMgmLMgAAAAAAAAAAg5EsEAsspO3tlJX/sbUjtl5Tf1YOqImjSgXUc0H76R9sO8fpOiML83+3XYyV+vpKerFpX5aSeukf9MYjZeHwlaZ9Rmpw9dy9vzx+u3duLR/DT7uJnXvB7zvKkhbNjvIWZ5U0vY7MNmPvMzXk1SV0rR6/3H9X5leN0F2q4jG93f7NeXrnrPSB8/9QFdCWGv5Gqyx6h46VtXrUb7OGJfQeC6YsR+3tUvEqXvXanjZF930KtY4iVMSWDlr+i/TbKJuVSPg7Z4+5DfdQatFZyOq7T81vr1e97odT4Q77hJH+s6/O4vueBVN9xIpr8lW5Pc9fk0E6ll5D9KQwAAAAAAAABAyDEQDCAUor/NNCk/w5WocyUS7BShXTfUTv3Z6aOGhoZO7auqqoq7bD4w7f3kk08kST179pQklZSUSOpcI9hVX9Lwqglsfgfr1q2TJA0aNCgyL99C5ydXmk/yf1d0rySwvS2/6aR4NQLtPmmn3O0Er2Enhu2f9r5v1/e19/F4KWVX21ySTVj5eY+xlzHvcaa++oABAzzXgcyI93d0JYBd+4a9X7q24UoauurCx2tTvOfiPe9qgyvh55Vq9KoD7GfbfvtS0Hql0f3fVXcZ+SPe1UletWz9vt8G/Xv7uULNb9/ymwrMRO1SW9C+5vf3H/0eZV/xY85lET5en0eSXV+0dKf8Xcul8/NP0JrBruX5TBZuDAQDAAAAAAAAQMgxEAwgFOIlgl2JAldaypXAMCk5u8amWc5sz9yh+NNPP5Uk9evXL7IOr9RftsR7QzftLSsri5lupyjtGsGuu8Abpt6q153oGxsbI8uYNDJpqfxiauLGS7MFTQAbdn+z01hed133qk0a/Zzd/8y+6bqruL1P2/uuq1ay/Tgd/T5oDUWvRGb0Y1eS0ZUQRfb4SeT4TYm70nOuetVBBP0Q4fW6TJ8JWvc03jpcbfFKI3olqV3sfhOvH9np+3w5N0D8pLvX/up3Hwlay9TvvSr8bCPV1GSQtvhNErqO237Tl4lw/Aov+/Oa6/lU68BnYhnXcdm1vkykcf0mgZN5/0HhYSAYAAAAAAAAALqATJf/YCAYQMYkqiNm0n6ubza9vlE27ESw/a2uSbGa6a6UYbx1Z0uiN/pddtlF0s6UpElmmtcdr26e1DlNaCekzeNt27bFtMHMX1FRIUmqrKyMrJN0VH4x9aPNvpCI3wRwurhqi0az04V2qt/sm64EhisJbHillOMlzFI96fKqce61nJ+7q1NTMff8/F1daXC/6UXXvuSq8x4vUehV49vmqv3rlfwNckWA6+oev/W2vZKTQWsIo/B49Ylk339T4arZ7eLqI37Xk45Uss3r9+o3te8HV5aFh/lb2p/r8iG5mmqdcL/HGT/P+e0vmU6BojCQCAYAAAAAAACAkGMgGEBBi/etqJ1oNak/m5nPbw1DV20qVz1Bk7TNB4m+qTa1jD/77DNJO39vXneSN2lLO11pb9M8byfBBgwYELMe5A871W3ES7q69i2vtJG9f3glgLy2E91fXe02+2jQdLK9nJ1yN/uw6+qBeG3yu9+7amzbz/tNUEUn/O36zPa85j2hb9++vtqK1PlJAXnx2r+DJlm96nkn2ob92Ct17Gqj4bWca1qiNnnNZ287mSSw632MNHH+iPd+7Tdhl0pyNahkk8F2/81GDVK/fc7v8qm0BYXLdT7odfWHvXwmeF2tEnT5ZOb1Oq54HUdt5j2Cz2dIFQPBAAAAAAAAAJBDJIIBhI75htgr2WpzJe/sJKv9TbT5GV3rNt/4SR3tuuuukqSmpiZJ7pSgnX503eXdPDbp0i1btkiS9t9//1ReCrLA1Iu2awPHS7W5Eqp++b2DsqtOqZ8acV7pEZsrxdTa2iqp8+/FTljF62dB2huP34S163Ey6zSva/369ZJ2Xj2AzHEdb+LxW5MwG2lTv+laVx9xpRxT+ZCSbDrTb8o+mfWn+j6AzEmlr9nzZTIZ7HUc83pfN+dsrqvb0iHZ95ygtcNd8yFc7PdNs+/a+7JrH062Vm4y0r0vxltfqnWJAYmBYAAAAAAAAAAIPQaCAYRCvASESbOVlJRI2plMtdN7dsLQrifqqqdmHvfu3Tvu8tngN/GVKHVpf9NeVlYW89j8flw188x8rprI5ht789OkKnv27OlsSy5+l3CnSu19INEyfqenWkctXm1gFzslYqdIXHdXt1Mmdt1xvwnj6HlTZW8zlZrBrvc0856XrjbDv0R1eIPW+7PfT736jGs9iWqSuvqOS6rP2xL1Qa80oavecCaPO9ms14rMC3r8SwfX8cn1vGEfx13J4Ezye67qtz9wjti12Pus67zQ73HET01+vyn1dO+LidbLfo90YCAYAAAAAAAAAEKOgWAABc2kaaJrdpoE8NatW2PmNdPNvK5kq53UMek/kyju0aOHJKm8vDxumxJ9U+v37uxBJZOqdNX2NT/N78sw85nlzHwmEe1iXpO9vnj8JteQGaY+tN1H7P0rOq1oz+OVogha28w1f6L1eNUrtVMjdprR3tcNk2YPmjoJOm80e1uuxKhXrWU/bbLff8x+QD3T7ElUIzdonUNXn/RaT5BksCsJ7EotetXeDyrRvun1HuSV9LLX45XI9nO8yubd7OFPvPS9X373oUzw+77slT53HQcy8b4ftOZvsjWDEU7m85friicj2WRwMvNkqi54LlK/9KeuhYFgAAAAAAAAAAg5BoIBFDSTyo2uN2unGu0kkvlp0hEm9WeSv+anWY95bG/Tls5vb+11bdq0Ke508wZt6vMGecP2mtf1TXppaWnc5YPWdIuXyqImaW65asS66kS7pkUvEzR1Zy/nd5+It/95JYPtx/bVAbZ07J/JJshcaS5XjWA/63fNayfIUknMIZggfz+/63BJR4LJTs3bXElhw2/yz5boeON1dYLXtv2u1/W8nzba82QyhYngvGqsF8J9DOx9yev45loul/z24XgK4W8Ef1yf27KRYE32PhaFtP+Z36/5vEsyGOnCQDAAAAAAAAAA5BCJYAAFbfPmzZKk/v37R6bZ9U1bWlokSWVlZTHL2ndONvOZJLBhnu/Tp0/McskmYuMx69y4cWPMNg2vhMb69esl7fy23KzP/C7M+iorKzttM6hU0yKJkl98C50brjSvKwkcPb/r7s32uoPeLdxvIjBem1z7qKs2qNfd011pPb81V6N5vS5X/WL7eVdy1HWn+Hhc6Uw7GUxSP794pRSDLhdUOhKDfhOXhr1PJkrfetUGDirZuqTxjm2FkA7rahLVvvb7OJ941eVOp2yds3mdd5MMDjfXuVs+KoQ2GvbVXq7jK8KJgWAAAAAAAAAACDkGggEUNPNtZr9+/SLTWltbJUnNzc2SpLa2Nkk7a9suXbo0Zh123TTzuKSkRJJUVVUlKbVUkVdi0NQAdq3b/tbWVQPR/mlSziYREp00tpdNd+ovmdQUcsNOzARJBCdbP81rPr/pRiPevm2nG1xpIq/ElFeSys++7rfWoesO7161gF1J4ER1Te1lTH04ux4ffTT74qXYvBK9QVP0ftvg2n4iqdZL9EoIB6m/m+zzXm0NkrD2SjrTx3Iv+m9TyH8Pr7rc6ZRq2t4v1/uB614E2WwbMs/8Dc15nt868LlQCO8d9rmf+b2aq0jNuWAhvBYkLxsDwVxPCAAAAAAAACBrPvnkE82ZM0f333+/7rzzzk6hsHhmzZqlgw46SD179lRNTY3+7//+T5999llG2zl37lyNGDFCpaWlGjp0qGbOnBnz/NSpUzVy5Ej17t1b/fv319ixY7VkyZKktmUGgr3+pYJEMIC0M99e9u7dW5L00UcfdZrHTkGYdFtFRYUkdXozd33zmShx4Gf56OdMQteklu1vt+1t2ssHvcN6otSkq45curi27Uo3R8unb/e7gq1bt0ra+bex06RedYDjzWP/DYPWK3Sl3f0kDYPW7k02MRWkbqF95YHrd2q/37hSn/ZP19/MXi5R+00axPXeafaT8vLyTutCegVJl7r2kSB9Jnq6az3x6gf6XZc93e8xLJk63F7rCFpL2aut6WhbJlObiK+r1Y7NZK3gZCV7BZCR6FjrtSwKj7nS01yx6frckst9PB/7mddnSrvN5pzP9Rm1ELW1tal///76/Oc/r2eeecZz/r/+9a8aN26cfvazn+lLX/qSVq1apSuuuEKXXXaZnnrqqaTasHz5ctXW1jrf75YtW6bTTz9dV1xxhWbNmqVXX31Vl156qWpqajRmzBhJ0uuvv66JEydq5MiR2rZtm2666SadfPLJ+vDDDyPjG35RGgIAAAAAAABAqOy1117aa6+9fM//zjvvaMiQIfrWt74lSaqtrdU3vvENTZs2LWa+GTNm6O6779ayZcsi83/zm99Mqo0PPvigamtrdffdd0uS9ttvP7311lv62c9+FhkIfumll2KWmTlzpvr3768FCxbouOOOC7Q9BoIBFBS7jmbfvn0lxaZM//Of//hal51MNfzWA01UNzV6Pmlnks4kgv3WHjTslKBXeteVxjXtiF5XWVlZzONUazu62mDShvb0fPrWvKsyfxu7JqxXajWaV0rOrnlrr8veD4KenEQ/DnqH6XQnOBKl3F3vO17r8vue4fUz3jKu9zTD7B/m/QvZkyh9m2wN22TTu/HSe0GPC660XjrqcAfdttd89k+/V834uUoh6HsU0s+8rxnR+2AYEnC2oMe3oGn/dKzb77YSzdfVkt5dgf2+aT7TpPtKxnRw9bNcJoW9rvyMdx+Zrm7UqFG66aab9OKLL+rUU0/VunXr9OSTT+q0006LzDNr1izdcsstmj59ug455BAtXLhQl112mSoqKvT1r3898DbfeecdjR49OmbamDFjNHnyZOcy9fX1kqRdd9018PYYCAYAAAAAAADQpR199NGaNWuWvva1r6m5uVnbtm3Tl770Jd1///2Reb7//e/r7rvv1le+8hVJO1LDH374oR566KGkBoLXrFmjAQMGxEwbMGCAGhoatHXr1k5l2drb2zV58mQdffTRGj58eBKvMvPy76saAAAAAAAAAPj/PvzwQ02aNEm33HKLFixYoJdeeknLly/XFVdcIUnasmWLPv74Y02YMEG9evWK/Lvjjjv08ccfR9ZzwAEHRJ474IADJClm/lNPPTXpNk6cOFEffPCBZs+endTyHR0dam9vT/iPRDCAvGFfFmRKQ/zjH/+IzGNuxGZfSmSXQPB7EzjXpayuYvvxLkuz22RffuPnMm4/z7u4LsuPbpv5/diXLSV7eZ3X5caJ5uOSvuwyN97wuoQ80U3jvP5mrpIQQbkuV4/evutyc9cN2+w2+b10L8h7h1l30Jvl+X0cdPnoaX7Lepj9BOnjVYrHTxkGrxsret2Qyeumh67HidbpxW9pBL83avNzabjfG1PZ86fzeJTM60FmmPdgUyIiH37n+VTWwE+ZhqD7q9+SEH75OX/Mp98pUuNVSsh187Nscm07l22xH7uOv/YNYbtyn5k6daqOPvpoXX/99ZKkAw88UBUVFTr22GN1xx13RH6XDz/8sI444oiYZaNLwL344otqa2uTJK1atUonnHCCFi1aFHk+OuU7cOBArV27NmZda9euVWVlZac08FVXXaXnn39eb7zxhnbfffekXiOlIQAAAAAAAAB0aU1NTZ3qyEeHqgYMGKBBgwbpv//9ry688ELnegYPHhz5v1nf0KFD4847atQovfjiizHTXnnlFY0aNSryuKOjQ1dffbWefvppzZ07V7W1tcFeWBQGgpEUr29XSTQg00x69V//+pckqbm5OfKceaO1v5W1vxl1fSPqd7+2b/q0adOmuPPFW6fXzeDS9S2sn9SkzX5drvRyunTlb5zzTdCbEMab5jd9aNj7l1dSMMh017z260z1Jjpej6O3aW/Lbovr6gDXe4Od0nb9PeL97fzeuM7Mx40d08/r+BTN68aKySaDXY+TOZfz2obXDev8HgP9zJfK64jmWt7vje4StZPz5OxxXQ1mp+ByyU8bsp1wTbSPppoE9pLs1QypbBP5z76KzeD9ND6/N2U053phvGlca2tr5HO6JNXV1Wnt2rUqLy9XZWWlXn/9dTU2Nur000+XJH3pS1/SZZddpgceeEBjxozRp59+qsmTJ+vwww/XoEGDJEm33XabvvWtb6mqqkqnnHKKWlpaNH/+fG3atEnXXntt4DZeccUVmj59um644Qb93//9n/7yl7/o97//vV544YXIPBMnTtQTTzyhZ599Vr1799aaNWskSVVVVZ1Sw14YCAYAAAAAAAAQKmvWrImppfvaa69JkoYPH67TTjtNW7ZsUUNDQ+T58ePHa/PmzZo+fbq+/e1vq0+fPvrCF76gadOmRea59NJL1bNnT/3kJz/R9ddfr4qKCn3+85/X5MmTk2pjbW2tXnjhBV1zzTW69957tfvuu2vGjBkaM2ZMZJ4HHnhAknTCCSfELPvoo49q/PjxgbaXjYHgog6fa5gyZUpKG0JwfpOQ9vzmp6l54pXwKC0tjbs9IFVmH+zRo0dkmvmm2E4KmNSw137ulRw2z69bty5me6Yt8fZ3rwRi0HqprvV5pQ4TJZfsb4LNvFVVVXGfJ20RPnYqwK63FiRZbq/TK7nhSjn6XU+iBP62bdviti3VOnJBj6GJuOovuxLDds1he3mv6dHTXMdu09fNe6d5j0P6BKln61Wz0+9xJOi5WDre6+11uFKYQVPMiWr9+0nq50o63zvgj73PFGodzKCpecPv60xXkj4Vrrb7uSKIq1LDyx5XcF0Fw9VLO7hqBNuPzTmy+X22tLTEPO5qogd7w6ihoUFVVVW69tprI33JpaWlRT/96U9VX1+vysrKwNsiEQwAAAAAAAAAOURpiBBwJULMT1f9RSm26LXU+Rs0+9tysy6vmo6uNJZdj9DP3V5tpCgg7dy/TRI4ukbwwIEDJXXeXysqKiTtTPLa67ITc2a/tZNLGzdujFnePG/aEi9lYveNoAlgV98x67Of99PX7DbYSV+jrq5O0s40oEkIm1pE9jfJRiq13ZAbmUxI2X9vV2LDbxLYXm+8NtsJ53SnRYL8nrxq/Ca7zVTuvu6a1z5/ML8/EsHp50q0+km4eiW6/dawzuZ7sZ3K9Du/LUibvVKEydYt9bud6GXyIW3ZVRXi54V4SVe/ryNoAthrPdncV1Ppk67PxaREC5d9Lud6HvG5+pOZbu6tY189l09X0SD9sjEQHOwTDgAAAAAAAACg4JAITjM7HWXqk9rpCpPiM8y3aD179oxM85sqaW1tjXlsUkJ2ksWV8DDfwppvnIx4SRjXN7Z20tnmlWpGuCRKhpvE77BhwyTt3CfsOromRWz2Z5N2M4/NN6NmflNE3q6fav+Mlz5wpQC99nfX6/ZKF9l9K14/cC3jqtVqnq+vr5e0Mxlt7p5qfm9+0yPx5uPb59xK5veebL1Cr7rZfmsEJkpO2o/tbaQ7IZTMFSv2+5K9rNfxNWgd1HjvS161Z+1jN1Jn/g6uhJORKOnuN+HrOlYlk2wNym+f8OrXmbxKwYurTwX5PXld2YDsC8t5htd7vtfz6Uj8ZvvcLVGbXVfJkMIvXF77tus8CrFc5w7mHM+M9xhheY9EfJSGAAAAAAAAAICQYyA4j7l+8XYyZ9WqVZJ2pkrMT7u2n/lWJ7rGn+vuka46w676pq6UhP3Nk50sNt/gRaeUXXdON483bdoUty2mjX369Il53lW/FOES/fdtbGyUJC1btixmnt12203Szhq3xtatWyXt3B9NH7HrYdr7pleSK5pdo9Qw/dkrmeiqI+nqe647ske32ZWOcPVvu6+Z5TZs2CBJ6tevnyT/fS6ZGuHILK96736W9WJfUZJqKjeZttrbTrUtfpb3qsdr+E0fmuftvu5VLzbec34TZUgfr+RSov3ZK+3tVa/dKz2ebLIw3rxBp7v2Y7uvJqq165UA9KrT6/X7SaY/eL3uoLWTEW5+aoQns45k5stm7fCg7z2JUr72+bDrfjUoHOZvma2rvPJVuhP4pm/YV8Jy7hduDAQDAAAAAAAAQMgxEJzHTFLQTuOalKOpDdzS0iJJ6tGjR8zy5htPO8Vo1hdv3a5EcLrSJHatVvNz8+bNnZa16xGbee0226mozz77TNLONGf//v3jtgWFzU7RRH/Db/qO2a/MPNXV1ZJ29hmTBN6yZYukzglgm50icKUJ/SSG7f3WK/Eb3W+j1233QVe6MFEb/H7za78u89P83kxdcpO4ttucjlQVMsv1N/FTf82r/p493asmcNDp8WpN27zSdtlIBnvxW+vXdTx21SKNlxb1aqervj/Sx+wz9pUUfhI/Xilaw/7b+6mzHXR6kCR6onV51TVOpka417Zdfchv25NJ0vv9GyD98unKI690eqJlvNaVarI/6PtCOqTatmj2OsznZPvcFIUjF7Xk81HQ1+l1dYuZXlpaKmnnPXS4QiXcGAgGAAAAAAAAgJBjIDgP2IlGk1Y0yVbz2Mxnp0bKyspilnet34j+NsjrW2OvdKHfpIarplu85czrsxPB9jZdiUiTTmxqapIkrV+/XtLOZDCpi3Axf/d4iTV7/zXfcJp9wySC7T7oqpFk7692n/OTAPY73VXDNJXEZqLl47XFzGt+t64+aVIW5vdmJ4Pt9aW7thXSz/U3SubOzEFPMvymnYJwJV/t1+E1n/28a/lEyeB01UZ2sWvnuZKV0bzq/Ju668icZOruJpsK8jvdJUiqPNltuOb3k5wMkq5MtHzQ+dJZOxiZk0/nIKnuq0GkmkLPZBu9koqp/J7y4e+M1Hhd4cTni9TYY0smGWzu7cTvM5yyMRBMphwAAAAAAAAAQo5EsAeTDjLJX1PX1E4CmxReeXm5JHe6x6QS/aSAXG1x1aiz1+GqjWrzql0YvZxpvyt55qor6apzY9KfrlrKKGymX/Tu3TsyzewLZppJ/jY0NEja+Q2nffdZu/60zZVwT4bffmnv/6563H7rM0Y/9qr57Upg2Gl8u46oeVxfXx8zn/l7JEp28K1zbvlNACdKutq8EoN+U4qu5Icfrvbbry9oDWHXawuS9g2asnb9XpJJa9vrdP200yJIH68awfGk++8QNG0Xb//2W580qFRStn5fV9C6k+lM/lKHO/vCksL2W/czmTrEqbbJ8Ps+kEq9bS/2MRKFzz7fMZ9LqP8cn9dVX3bfSHSFK8KD0hAAAAAAAAAAEHIMBOcB8y2NSSmWlJRI6pzEsb+dMcvZSSavxGCib2e9koB2csGrBrBXUjgR+9s9V71i1zfdZn5TQ9nUhyURHC5mPzHJb2ln8tT0HZMGNz/NvmJ/c2zvO656n14pi3j9x5Wm96q77Wqb4dWmIH3Pb+rJdRWCK1VlfpqrHUybq6urO62bb/Pzg71fuerNxlsm6DZc/SZossjPyYp93HS1yT7emv0yHSkJrzrDLn7TX17J4Hi/J1c6JJVjOPxx9QM/vI5BXssZQf++fq4s8buOdKV347XJi9/fQyYSlKn+DVCYclnDNGj9/XTs98m+Xq/7agRpWzJXESG/eJ2z2cngrp7+DnqPJzP2ZMaizOdnhF+m3xcZCAYAAAAAAACAHCIRnAfa2tok7fz2yk7EudKJhp1Qsp9PRz1Tw/VNrFdqzCu9myjJ4Uob2skuVxLS/D75diuczN85OhFcWVkZM81O1Zs+Z3PtS171dhPV4Q06r6uf2v3Aq56vn5rBXuknV1tdyWnzvJ3mt7+hN/OZms11dXWRbQwaNChh25AdQerNBv1bZaomcLx2eKXa7X3d63hpz5eJ1EmqtRJd9RAT/S3teUnmZ4/rypREkj2f8+p76Ugr+j1+BG1bkCSg3/Yn+96VytUJrpqMyL5U6qony+85X7zpfvtUsul6r+mpSNfVCsm0jb4WPvY+bl+xbJ/L5eKcJpPniUF5ffb0Gq9BODEQDAAAAAAAAAAhx0BwHjCpRfvbKvsbJFdtQ7+1QOM976or7PomyLVtrzql9vyJvmnySmR51Ws1z5vfp/lp6sMmW1MP+cn8/UpLSyPTTALV3le8kjhe+4ZXnVQ/yWDXtgy/yRSvlESQusVB+4Krz9m81mfe+0z9bon6XvnCpOhNuiJRYtbvMSjVup3J1AT04pWaddUJT0XQdflN+XtdLZBoe/ayHBezL0gC3Ov9P9kTd7/ngvEETbLbxw9X8j9osjiZNvqt15uJGsLcnT13slkzNp31qL2uCHPNF7QtXttPtKxrm8kk/JNFTeDwsM9fzP1+7Omu+xrkUzo3G4Je7WDO+ekzXQsDwQAAAAAAAAAQcgwE5wGTZKyvr5fk/vbGqz6pK91rS6Xmo9c3uZm442yyr8d8G2buhGkSweZbLzOd5FM4RO9rW7duldT5b2rS4XZq3K5p6+KVvrDni5eaSHZ/C1pX1fU40TqCJjS8+rvXe41Jm/bu3TsybfPmzZKkiooKX21AZpi/jZ0qCFKfM+jJhd99PMhxJWidYRevdIXXcSjePF79w+/xNpUEtT1PV0nL5JNMnDeli99zvVSWdV25Y0umdrbfusRB6+Un2qaLfUWSed3ZrE+LzPO6siyVY1HQxG/QK8eSEXSdQbeVzJVEfJ4LH7+foVznMLl4n032fCrRPp/qvu3qn3YimD6EdGEgGAAAAAAAAAByiERwHrC/nTGpRHu6SS26EsB+6/km+tbUtS6/3xC5avPY/NROtRNo9jJ2itOVkLa1tbVJ2pkI5luvcIj+e9t/Y8NO/Kayn/qZL9HyXst4pSns/uFKbAbZv4Mm/f1+Q+96LfGuYjD1gkkE55ZJBJsrKVw16+NJV6LRb1I9SALfa1/0SpO4nk9nLeGgKS2/6a9E67NTaqQUsyfI3ymVhG70ckHn9zs9Hr9JyGRrZvvZr4Mmg9NVczmTyS6kLhPvc0HPg9JxvMzkuqPXk8w5rauecbpFrzebdYiRG3aC1b7CwlU7OJ1SPQ77lWjf9mJetzmndx2HW1tbJXUeG6HvhBsDwQAAAAAAAAAQcgwE5wHzLYyLKwHsqgnsSgIH+TY26B/d/qYtaN1SPyllrza5ksAmBWq+5TL1Y0kchkv0/mHuJhv0G1MXr/3XT41Hr/3ab73PZGoAB53fqxay39fvN8kZfdf06HrByB37ShTTR8zP6L+Z34R4KikIP22NdxwJWmPePq7adT1dy/upS+dVZ9irrUHfK/ykGlO5ggCZkUyNvmSv0khH7VuvNnmxawP7fd9Ipj5vuubzu1yicwv7ngXIHa/35nj81gL2ko3kXbqS7kHS937X5fc9KwiOY+Fl/rb2FZ7pTr9Hb8t+Ltnjaipt8eLnPhXSziS16yprksBdCwPBAAAAAAAAABByDATnAZNebGpqipnuqgHsSgIbQdKLyX4bnGy6IpVvyVw1UM23WHZyy57fzNezZ8+460F4mD5l6pva+4Ar5Re0ZnA6agMns450Lp+OdQZNcpm+ab7Rj57PruuM3DJ1xewrV/ykS72muwSt6xkkpWykUk/Xj3jH6WSTf6mmGROlEu3fIf2vMCSbnvVbrzedbUrXculMKmWi5m80P/UoSQLnXqL7mvjdL72OHfmQsLP7e9ArAYK8Br+DCsle3ZauK45Q2Fz7i30VsNd7sZ+Uu9d0e5+0rxRLdaAtXkrZ3qb9OqPPh6XONZW9xkzQNTAQDAAAAAAAAAAhx0AwAAAAAAAAAIQcA8F5wFzGbtgFu/3eHC6Zm8IFvRTH9djr8luvNsUrV+E1r+tmPub3Zy4PsW92VFZWlrDNKHzNzc0Jn/dz2aaUmxtOZKpURCpclzv53abrRo7xbhaH/OIqq5Jo3lS3ZT/2OiYkusQ6aEkIw97H7X3f72Xdfi7pS9dl+smczCVzkyRkVrxSJ4bf0g9e03PBa78Pegm+Ee9cLmhZsnTdPM9PGRZzyTKX4uaf6L+f/fdJ9jNDussOxVtXquVYMj0QEG8dmT7eoWvz+zkvnYL2I9fYSJASLK6+39bWJsn9ewgyVgSkgoFgAAAAAAAAAMghEsF5pFevXpJ2phntZLDhSga7vt1J9E2T35s/ee0EiW64EI8rGRVvHtcyruLsZrqd4CovL0+qrSg8jY2NknbeGJD0d3okm6S2+6Trho/IX/YN/uLtC/axKZ03C43HdXVIvGmux662et2E1OYnUZupRFSyKefoeemD+SNRUjTZGyRl49jndV7nNxEf9Hid6PfkN0Ht1TaXIKkzr/cc5E6QzyJey3o99rttP/u11zJ++4GLn300n69CQNdlbo5mbnhsJNsXEnEdT5KVzNViXjew43iDeBgIBgAAAAAAAICQYyA4D5hfcEVFhSTvei7JPo433e+3Vq6EiqlBYyf+7G/ggtQ9DZqa8KobabfJ1GfLRf0gZIerlq2dWDTpxqDrNVwpwVzuW65kYqI2Zar9Xokwk0IkjYhEkq0dmsy6gs6XjFQTGkGTxYn6M6mt/GPeD835VfT7Y7LpwmQFSfV7JZJc83v1h3QmmlJN5adSI7y0tDSpbSJ7gtTAT/Zx0Cs5gs4Tb/5k63IH2YaR7DpJEiOd/KZqg4w5eF1tZcYZMln/2r6q2W9qmH6FeBgIBgAAAAAAAIAuINNfTjMQ7JNJJ5aVlUmSWltbJflP0/qtR+WHV4LD/mnaahIP9nqCJIK95gm6TrvGI0ngrsNO/JpkldkHzPNeCWKXVOqFGnbCyNWGVPfbIG1KNSFst9lV59x+z5NIS+U7P/tj0DqFyfJzrEu2br15nabOnNf8ybzGdP9e/PbT6PdF+5iN/GHXqU7E776U7PlhMueXQdsU9HGQ1xC0n6ZaGzhe7W2ObYXFvE/6vWLJbwLe9TgZqSaEvdaXyjHKb+1kv21IR5vQ9XhdZW2ej3e89ap37/c4YM8X9DiUypUKQCIkggEAAAAAAAAg5BgIziPmGyJTY8ZOM3p9M2UnmTLBXrdpa1NTkySpZ8+eMfOlcgfNoElIr21QH6frMWm+8vJySe6a1mbfsL8RDlrbMFFqyvVGavcpv8lg12OXVFIUfusO221yvSfZ0/kGu3DE6xN+98WgST6v+TP5nm5v2/Wa0pGYSpXfY2S8lCLHxfxl/k7miiup83uma/8L+rwtSErX7zrSxc8+61Ub1UvQq2Bcv8/o+2VwnCtMqdYGzmSbUq2/nYkBgEwl/DlWIZ387IdeNbXtqx7tpLCdCE7XuXG89gPJyMZAcOZGJQEAAAAAAAAAeYFEcEBm5N1OBmfi29JkU7cmqWJ+mhqfPXr0SLidZHglIV3zGXZb0XWY/dWu9WZ/+2rvW3Ya36s+otdj17Ro9jdurv3elSD2EiSl7PV6XUlf+1twF7M+837BN9uFJ8h7ejpqfAbdjisJ5fc4mmzfCNrORNv0m/b3uz2738XbJvJHNmuI+p0v3r6a7X0oyPYylQB2rd+cO3BsCw9Xfc9MCXLMSjbJFXS5IFe+BL3ix88VdYnaBqSDn7EW175nxmvsz5j2vux1hY5hX4nGvY2QKZSGAAAAAAAAAICQYyA4j7kSrJn4NtRvfVH7sflZVVUV89iumxOE69tg+/fhSnfa82WyZjIKg6mxaGoFu/qWvQ/5TYIESUn4TRoaQfdfr3qq8bbvN+Hst+6knRi2v9UuKSlxtgWFIbr2panF7bdWsN/jQtB9OVES3ysZlUy/STR/In7rN/r9ffpNjZBSLEzm/VLaWefeVV896DlX0H0hm/tOOq8c8LqiLNU2eV25g/DwSvml2ift5dJ5ZUCy68zE1Ql2/VS7D3mlKIF0SuWqavvqU8PvAJtr2/b6km0n4MJAMAAAAAAAAACEHAPBecxVh9N1V0r7edf6gtQIdbHTuCYdZtpkP3a1Jd72gn7blYs7yqOwmH3BJINdtaxdy6VrvnjzetVi85rf5jfhGG9bfu9Iby9v19ay61sZ1OkOj3j1eOOlFxItm+x7dDInJakmJf32BXv5ZOqF2/zWybfZCTUSVYXPKyWerjRiLnnVTwzC7iOp1lp0pT/N36G0tDRmOsLLlQy2n/d7vPOz33vV0/W6j0XQK2OSOVb7PUd19UU7ZUlfQr4y+6i5Ks6ebj9OZbwGyAQGggEAAAAAAAAg5BgILiDmD2Gn6oLehTLRNL9pQjuVbNcEthOBrvSEq62J2gAky+yndp1FV01g1/6bamo3mt86oa7+7XreXn+8x36/dfZ6fa5awHbbqJsYTubvaieCvWrbpiodiSE/x6J483vNF69PJdvOoKlG+/0qup4zCluyV1UkmzDKRp3SZLeRrrq/QbiOfdTf7rr8/s3Tcb7oWsbvB/mgV5SlIyEctI189kOhYZ9FocrGQDCf/AEAAAAAAAAg5IiipImrFo2d9vG6g22QbXndHdee33AlhJO523tQJDLgxSQXgyZi01kz2G9NNlcCyW8/TzSfndR1JXm92mzXBHddtYBwM3/3bCWDM3Enc8PvlTZ+rxIIwv59JZt4tK94QHjYV7EErf3pVyppRb99yOY3IZyLJLD9s7y8POYxYMvEMcK1btfzrrq8fs/9XOuN3rbf+qd2G8xy1NcGgOyiNAQAAAAAAAAAhBwDwQXI/pbVJITtpKArxRidynIlKbwSHX4TGK5viIMkLO16xH63BXix01Re6XpbkPppXmkJ12O7jV4pQVf6PlHq3lXD1/Xe4eq/rhri6BpctYKTTQIHrR0ar/6133V7pZqCtiGddYv9srdJ3dKuI92pe/s9PNF+7fc46bdvee2v6UrM++F13KZvwSXoFWReqf5401x9wXWlmH2881t/PtH7StBBBHOe3draKkkqKyvztR4AQHoxEAwAAAAAAAAAIcdAcAjY3ya3tbVJ2vmtq11LLvobX/tbXq/0h0l6JXvn6WSfj2a3EUhV0P3T9abop76aVy03rySWq9ZbMkklr9rddgLYnm6YtpSUlEjqXMccXYud0LNTS64kuSu9no4UXrpTia62JZoeNDHpl/2eQN3SriOZmp7R/B6P7Pn9HOOSrQXslW52HX+ywRzj/P7egXRcJWLzSvLa92tw3RcjnfWKDa/zRfM8SWAAyC0GggEAAAAAAAAg5BgIDiG7TmO8JLBh0g0udgI41TtPu2pFxbsDrWHSham2AfBi9nevOot+E4pB9lW/qUi7L9nJpCDp5qAJYFebTOrElVZG12JfhZKuuqWGq/8lOo7Y0111se11pirI1QHJ1uQ3y5FS7LpMnzPsOt02r+OE63ji5zgTNAkc9H0iG8cX+3Xa58r0MQSVrivP4s1jlrWvxrLfF1znkV5tS9TnXNvw+x4DAMgNBoIBAAAAAAAAIOQYCA4xrzpNknctz3Snb+312XWr4s1LAhi5kmxtYD93WHet06uWqZ30SqWfeC1rJz1cyWBj69atSbcF4WPXCvbqJ8nuN/H6jN+UftDawEHbGi+Bn0zKUur8+zTM49LSUl9tQvjZxwv7/C/Z/pEJdttcyeBMJoFdx9sePXrEfR7IlCD7mjlHM/eGMT/NscD+jGU/9rp6x9UvoudL9xU/AIDwYCAYAAAAAAAAAHIs0190MxCcx/IluZcv7QCkzndcdvG6G7Sf/dq1Djv950oB2gkNO+Wb6MoAe52uddhttdNmzc3NcedH1+bqP5naTxKlb1116oOeAPltezJXA3jN70pE0u9gs1N69pUkfmtjex3jvJ6Lty57OTuNmM3krwtJYOSK67wrmn2uFvTqTq/7w6S6HAAg/1EaAgAAAAAAAABCjoFgAHAwKSqTbHQl79JRV9GVXLRTU15pqe7du8esL1H9Nq/Es52qtOvLtba2xjwPxGPXMfSqNZ0OrlRVuhJ+XnV9/WzP633Dru/q6vsmvQjYXMlB177kt0ZoMrz6XiaSwH4/4Jiko13HHMhnZj8tKyuTJJWXl8dMt5k+Zid7zbHGPO91TKF/AEDhYyAYAAAAAAAAAEKOgWAA8GAng734SU25api6kowmqWFSuCb5a37aSSavWsHR83qlR+xUst02wA+TQrLrlWaT1z7r6pdB1xNkPq87s7uSktRrhF9mnzL7jEnnB+WVhI/3nN8PEfb+ns6awa42mOQjxzIUMq9zOaO0tDTudHNua84nSfwCQPhlYyDYfV0yAAAAAAAAAOTYU089pZNPPll9+/ZVUVGRFi1alPFtdnR06JZbblFNTY3Ky8s1evRoLV26NGaeM888U3vuuafKyspUU1Ojiy++WKtXr056e37+pYKBYAAAAAAAAAB5a8uWLTrmmGM0bdq0tK3z1ltv1fjx453P33nnnbrvvvv04IMPat68eaqoqNCYMWPU3NwcmefEE0/U73//ey1ZskRz5szRxx9/rLPPPjup9mRjIJjSEABCwc9lsX65SkDYj81lseaxKQ1h39zGdXO4RJcMui65teflMkGkk7kM1d7/0tm/XFzbcO3z2WiTV1u8+jgQlKusiF0OyLVvJjom+O0z9vuA/X6QSokIV/81j12XyANdGed6ALDDxRdfLElavny5c566ujpdd911evbZZ9XS0qLDDjtMP/vZz3TQQQcF3l5HR4fuueceffe739VZZ50lSfrNb36jAQMG6JlnntF5550nSbrmmmsiywwePFg33nijxo4dq7a2trwsGccnFgAAAAAAAAAF7ZxzztG6dev0xz/+UQsWLNCIESN00kknaePGjYHXtWzZMq1Zs0ajR4+OTKuqqtIRRxyhd955J+4yGzdu1KxZs3TUUUclNQhMIhgAfDKJJJNkct3cxp4/GWZd5mY25iYeZp0m0WTmc90szk8bXDerstNhQDq5kq2Z3N+80reum8RlMhnsaoNBAhjZ4pW+tY8z0f/3usGiYY6fZn5zjGtpaYmZnsp+b98k1WyDYxkAAEjVW2+9pb/97W9at25d5DP5XXfdpWeeeUZPPvmkLr/88kDrW7NmjSRpwIABMdMHDBgQec6YMmWKpk+frqamJh155JF6/vnnk3oN2bhZHAPBAAAAAAAAAPLCP//5T7388suRx2eccYaOPfbYhMu89957amxsVN++fWOmb926VR9//LEk6c0339Spp54aea61tVUdHR168sknI9MeeughXXjhhYHae/3112vChAlasWKFbrvtNo0bN07PP/984LAKA8EAEND27dslSdu2bZO0M61rJ6LiJZr81uU1y5ptmG2WlZXFXZ9dS9her11nNJrdbrMtIBf8JguTmd9rHjv1b362tbUlXJ/dhnht8nuCRgIYueaqNZ/o+GGY44c5LprHrqtX7PWY6aYvmvXYx0TD9NF47Q/6XgIAALqWoUOHatCgQZHHhx12mOcyjY2Nqqmp0dy5czs916dPn8h6Fi1aFJl+3333adWqVTE3oDMJ4IEDB0qS1q5dq5qamsjza9eu1cEHHxyz/urqalVXV2vYsGHab7/9tMcee+jdd9/VqFGjPNsdjYFgAAAAAAAAAF1GaWlpzE1ky8vLPZcZMWKE1qxZo+7du2vIkCFx5ykvL9fQoUMjj3fddVc1NDTETDNqa2s1cOBAvfrqq5GB34aGBs2bN09XXnmlsx3mS3NTXisIBoIBIEkmbWQST+ZnooLtdg1G++7mdgrKK53rlTK0E1Eko1AovOr5ek1PZp12EtiebqcRk2mD4ef9AsgHdr9JdNyx92f7sauv+O0HQfoLxzsAABDUxo0btXLlSq1evVqStGTJEkk7krsDBw7U6NGjNWrUKI0dO1Z33nmnhg0bptWrV+uFF17Ql7/8ZV+p4mhFRUWaPHmy7rjjDu2zzz6qra3V9773PQ0aNEhjx46VJM2bN09///vfdcwxx2iXXXbRxx9/rO9973vae++9A6eBpewMBHONIwAAAAAAAIC89dxzz+mQQw7R6aefLkk677zzdMghh+jBBx+UtGPg9sUXX9Rxxx2nSy65RMOGDdN5552nFStWdLrhm1833HCDrr76al1++eUaOXKkGhsb9dJLL0XKQvbs2VNPPfWUTjrpJH3uc5/ThAkTdOCBB+r111+PSTT7ZQaCvf6loqjD5xqmTJmS0oYAIB/Eq0voSjGZhCHJJSAYV13sROx+Ztc+NT/j1R2VOl96ZddS9dMWs03XNgAAAABkX3QN3zBqaGhQVVWVRo4cGbkC2WXbtm36+9//rvr6elVWVgbeFolgAAAAAAAAAAg5agQD6FLipXvb2trizhskzQhgJ7vedjRXv4qX1o9+bCeEbSbZb/qz2bZpi73e6PWY/9spYgAAAADIFm4WBwAAAAAAAAAhx0AwAOQQtYGB1MRL2MZLCUs7+5t53lW724u9nKteMYl/AAAAAPkm0+MQDAQDAAAAAAAAQA6RCAYAAKHiVYe3uLg4q9sDAAAAgHzAQDAAAAAAAAAAhBwDwQAAAAAAAAAQctkYCOZ6SQAAAAAAAAAIORLBAAAAAAAAAJBDlIYAAAAAAAAAgJBjIBgAAAAAAAAAQo6BYAAAAAAAAAAIOQaCAQAAAAAAACDkGAgGAAAAAAAAgJBjIBgAAAAAAAAAQo6BYAAAAAAAAAAIuWwMBHdLaWkAAAAAAAAAQN4jEQwAAAAAAAAAOURpCAAAAAAAAAAIOQaCAQAAAAAAACDkGAgGAAAAAAAAgJBjIBgAAAAAAAAAQi6vBoL79Omjnj17prSxfNDU1KS6urpcNwNIaNCgQbluQsroaygEhXRso0+hkBVSX5Pobyhc9DUgewqpv9HXgMKR6kCvF98Dwdddd51KSkoy2ZasaGtr01133cWbIPLapEmTct2ElNHXkO/69OlTUMc2+hQKVaH1NYn+hsJEXwOyp9D6G30NKAzZSAR38ztjrt7gfvSjH+nII49Uv379VFZWpn322UeTJ0/W+vXrO83b3t6uO++8U7W1tSorK9OBBx6o3/72tzHzlJSUFMy3dkA2vfzyy5owYYKGDx+u4uJiDRkyxDkvfQ1h0LNnz4we2/72t7/pm9/8pg499FCVlJSoqKgo4fy/+tWvtN9++0WOdT//+c9jnqdPoVBluq9J0sMPP6zjjz9eAwYMUGlpqWpra3XJJZdo+fLlceenvyGMstHXxo8fr6Kiok7/9t13307zcr6IMMt0f4vXz8y/L37xizHz0teA8DADwV7/UpH3NYIXLFiggw8+WOedd5569+6tf/3rX3r44Yf1wgsvaNGiRaqoqIjMe/PNN+vHP/6xLrvsMo0cOVLPPvusLrjgAhUVFem8887L4asA8t8TTzyh3/3udxoxYoRnaQr6GuDtxRdf1IwZM3TggQdqr7320n/+8x/nvA899JCuuOIKffWrX9W1116rN998U9/61rfU1NSkKVOmZLHVQGFauHChamtrdeaZZ2qXXXbRsmXL9PDDD+v555/Xe++9F3Nco78BqSktLdWMGTNiplVVVXWaj/NFIHmPPfZYp2nz58/Xvffeq5NPPjlmOn0NQBBFHZkuPpEBc+bM0dlnn63f/va3kTe2VatWqba2VpdffrmmT58uacdI+vHHH69ly5Zp+fLlKi4uliTde++9Wr16dc7aD3iZNm1a1re5evVq9evXTyUlJTrjjDP0wQcfxE1S0dcQFoMGDcpoGZa1a9eqsrJS5eXluuqqq3T//ffH/fZ269at2mOPPXTkkUfq+eefj0y/6KKL9Mwzz+iTTz7RLrvsIok+hcKU6b7msmDBAh122GGaOnWqbrzxRkn0N4RbNvra+PHj9eSTT6qxsTHhfJwvIuxycWy79NJL9cgjj2jlypXafffdJdHX0HXkYowkmxoaGlRVVaXBgwerW7fExRva29u1YsUK1dfXq7KyMvC2fJeGkKTly5cnvEQhW8wl69H1bZ599lm1tbXpm9/8ZmRaUVGRrrzySv3vf//TO++8k7X2AanKRV8bNGiQr8ub6GsIk0z2tQEDBqi8vNxzvtdee02fffZZTJ+SpIkTJ2rLli164YUXUmoHkC+yfWyLd75If0NXkI2+tn37djU0NDif53wRXUE2j2stLS2aM2eOjj/++MggsERfA8Im70pD9OvXr9MlCm1tbbrmmmvUo0cPSTvuRtnU1OS5ruLi4kjiwktHR4c+++wzbdu2TUuXLtWNN96o4uJinXDCCZF5Fi5cqIqKCu23334xyx5++OGR54855hhf2wNyLVd9zQ/6GsIkH/rawoULJUmHHXZYzPRDDz1U3bp108KFC3XRRRcFXi+Qb7LR3z777DNt375dK1eu1A9+8ANJ0kknnRR5nv6GriDTfa2pqUmVlZVqamrSLrvsovPPP1/Tpk1Tr169IvNwvoiuIJvnkS+++KLq6up04YUXxkynrwHhko2bxQUaCK6oqOh0cjxx4kQ1NjbqlVdekSTdeeeduu222zzXNXjwYOcNPGxr165VTU1N5PHuu++uJ554IuamBJ9++qkGDBjQ6Zs3sxyXQKCQ5Kqv+UFfQ5jkQ1/79NNPVVxcrP79+8dM79Gjh/r27UufQmhko7/ttttuamlpkST17dtX9913X8xNdehv6Aoy2ddqamp0ww03aMSIEWpvb9dLL72kX/ziF3rvvfc0d+5cde++4+Ml54voCrJ5Hjlr1iyVlpbq7LPPjplOXwPCJe8Ggm2/+c1v9Itf/EJ33323TjzxREnSuHHjfH3j5OdyWWPXXXfVK6+8oubmZi1cuFBPPfVUp7pUW7duVWlpaadly8rKIs8DhSpbfc0P+hrCLBd9bevWrZHUiK2srIw+hdDKRH/74x//qObmZv3rX//S448/ri1btsQ8T39DV5TOvjZ16tSYx+edd56GDRumm2++WU8++WTk/i2cL6IrytR5ZENDg1544QWddtpp6tOnT8xz9DUgXPJ6IHjRokW64oordP755+vaa6+NTN9rr7201157BVpXfX19zBtUjx49tOuuu8Y8Hj16tCTpjDPO0EknnaSjjz5a/fv31xlnnCFpxxunSYBEa25ujjwPFKJs9jU/6GsIq3T2tSDKy8vV2toa97nm5mb6FEIpU/3NfPA+9dRTddZZZ2n48OHq1auXrrrqKkn0N3Q92Ti2XXPNNfre976nP//5z5GBYM4X0dVksq/NmTNHzc3NncpCSPQ1IGzydiB406ZN+upXv6phw4ZpxowZMc81NjZ63kVW2lEDp1+/fpKkSZMm6de//nXkueOPP15z5851LnvUUUeppqZGs2bNigwE19TU6LXXXlNHR0fMZRGffvqppB03wgIKTa77Wjz0NYRRuvtaEDU1Ndq+fbvWrVsXc7l6a2urPvvsM/oUQidb/W3vvffWIYccolmzZkUGgulv6Eqy1dfKy8vVt29fbdy4MTKN80V0JZnua7NmzVJVVVVk7CMafQ0Il7wcCG5vb9eFF16ouro6/fnPf1bPnj1jnr/rrrsC18C54YYbYmrr+LnZTnNzs+rr6yOPDz74YM2YMUP/+te/tP/++0emz5s3L/I8UEjypa/Z6GsIm0z0tSBMn5k/f75OO+20yPT58+ervb2dPoVQyXZ/27p1a0xSiv6GriKbfW3z5s3asGFDzCAW54voKjLd1z799FO99tprGj9+fNwSEPQ1AEEFHgi+7bbb9Kc//Ul//OMfVVtb2+n5ZGrg7L///jFvWsaWLVtUVFTU6c10zpw52rRpU8wdn8866yxdc801+sUvfqHp06dL2jFK/uCDD2q33XbTUUcd5fs1Avkgm30tCPoawiYTfS2IL3zhC9p11131wAMPxAxMPfDAA+rZs6dOP/30pNYL5KNM9Ldt27Zp8+bNnb7c/Nvf/qbFixfrggsuiEyjv6GryERfa25uVltbm3r37h0zz+23366Ojg6dcsopkWmcL6KryPR55OzZsyODzfHQ14BwybtE8OLFi3X77bfruOOO07p16/T444/HPH/RRReltd7U0qVLNXr0aH3ta1/Tvvvuq27dumn+/Pl6/PHHNWTIEE2aNCky7+67767JkyfrJz/5idra2jRy5Eg988wzevPNNzVr1iwVFxenpU1ANmS7r0nS+++/r+eee06S9NFHH6m+vl533HGHJOmggw7Sl770JUn0NYRLJvvaihUr9Nhjj0nakTaUFOlTgwcP1sUXXyxpx4n/7bffrokTJ+qcc87RmDFj9Oabb+rxxx/XD3/4w8B1vIF8lan+1tjYqD322ENf+9rXdMABB6iiokKLFy/Wo48+qqqqKn3ve9+LzEt/Q1eQqb62Zs0aHXLIITr//PO17777SpL+9Kc/6cUXX9Qpp5yis846KzIv54voCrLxmW3WrFkaNGiQTjjhhLjP09eAcMnGQHBRR4A1zJ07N3ITjkw0xrZhwwbdfPPNeuONN/TJJ5+ora1NgwcP1umnn66bb75Z1dXVMfO3t7dr2rRpeuihh/Tpp59qn3320Xe+851O357de++9Wr16dVrbCqTTqaeemtW+JkkzZ87UJZdcEve5r3/965o5c2bkMX0NYTBo0CAddNBBGetriY6Z8epzP/zww7r77ru1bNky7bHHHrrqqqs0adKkmHpv9CkUokGDBmnSpEkZO49sbW3VDTfcoNdee03Lly/X1q1bNWjQII0ePVrf/e53NWTIkE7L0N8QRpnua3V1dbr66qv17rvvavXq1dq+fbuGDh2qCy+8UNddd51KSkpi5ud8EWGW6fNISVqyZIn23XdfXXvttbr77rud89HX0BVMmzYt103IqIaGBlVVVal///7q1q1bwnnb29u1bt061dfXq7KyMvC2Ag0EhwVvgMh3YXmTo68hn5kPzIWEPoVCVIh9TaK/ofDQ14DsKcT+Rl9DIQvLGImLGQju16+fr4Hg9evXJz0QHLhGMAAAAAAAAAAgffKuRjAAAAAAAAAAIL0YCAYAAAAAAACAkMu7m8UBAAAAAAAAANLD1AjeZZddfNUI3rRpU9I1ghOvHQAAAAAAAABQ8CgNAQAAAAAAAAA5lunCDQwEAwAAAAAAAEAO+RkE5mZxAAAAAAAAAFDAGAgGAAAAAAAAgJBjIBgAAAAAAAAAQo6BYAAAAAAAAAAIOQaCAQAAAAAAACDkGAgGAAAAAAAAgJDLxkBwt5SWBgAAAAAAAADkPRLBAAAAAAAAAJBDlIYAAAAAAAAAgJBjIBgAAAAAAAAAQo6BYAAAAAAAAAAIOQaCAQAAAAAAACDkGAgGAAAAAAAAgJBjIBgAAAAAAAAAQo6BYAAAAAAAAAAIuWwMBHdLaWkAAAAAAAAA8KGtrU1TpkzR5z//eVVUVGjQoEEaN26cVq9enXC5qVOnauTIkerdu7f69++vsWPHasmSJRlta0dHh2655RbV1NSovLxco0eP1tKlS2PmOfPMM7XnnnuqrKxMNTU1uvjiiz1fSy4xEAwAAAAAAAAg45qamvSPf/xD3/ve9/SPf/xDTz31lJYsWaIzzzwz4XKvv/66Jk6cqHfffVevvPKK2tradPLJJ2vLli1Jt+XWW2/V+PHjnc/feeeduu+++/Tggw9q3rx5qqio0JgxY9Tc3ByZ58QTT9Tvf/97LVmyRHPmzNHHH3+ss88+O6n2dHR0+PqXiqKOVNcAAAAAAAAAAEn4+9//rsMPP1wrVqzQnnvu6WuZ9evXq3///nr99dd13HHHSZLq6up03XXX6dlnn1VLS4sOO+ww/exnP9NBBx0Udx233nqrli9frpkzZ3Z6rqOjQ4MGDdK3v/1tXXfddZKk+vp6DRgwQDNnztR5550Xd53PPfecxo4dq5aWFpWUlPh6LQ0NDaqqqpIkFRUVJZzXDOPW19ersrLS1/qjkQgGAAAAAAAAkBP19fUqKipSnz59Ai0jSbvuumtk2jnnnKN169bpj3/8oxYsWKARI0bopJNO0saNGwO3admyZVqzZo1Gjx4dmVZVVaUjjjhC77zzTtxlNm7cqFmzZumoo47yPQhsy2QaWGIgGAAAAAAAAEAONDc3a8qUKTr//PN9J1zb29s1efJkHX300Ro+fLgk6a233tLf/vY3/eEPf9Bhhx2mffbZR3fddZf69OmjJ598MnC71qxZI0kaMGBAzPQBAwZEnjOmTJmiiooK9e3bVytXrtSzzz4baFs9evTQwIEDfc8/cOBA9ejRI9A2DAaCAQAAAAAAAKTdrFmz1KtXr8i/N998M/JcW1ubzj33XHV0dOiBBx7wvc6JEyfqgw8+0OzZsyPT3nvvPTU2Nqpv374x21u2bJk+/vhjSdKbb74Z89yPfvSjTu2bNWtW4Nd4/fXXa+HChXr55ZdVXFyscePGBUrvlpWVadmyZaqvr/f1b9myZSorKwvcTknqntRSAAAAAAAAAJDAmWeeqSOOOCLyeLfddpO0cxB4xYoV+stf/uI7DXzVVVfp+eef1xtvvKHdd989Mr2xsVE1NTWaO3dup2VMyYnDDjtMixYtiky/7777tGrVKk2bNi0yzSSATUJ37dq1qqmpiTy/du1aHXzwwTHrr66uVnV1tYYNG6b99ttPe+yxh959912NGjXK12uSdgwGJzu4GwQDwQAAAAAAAADSrnfv3urdu3fMNDMIvHTpUr322mvq27ev53o6Ojp09dVX6+mnn9bcuXNVW1sb8/yIESO0Zs0ade/eXUOGDIm7jvLycg0dOjTyeNddd1VDQ0PMNKO2tlYDBw7Uq6++Ghn4bWho0Lx583TllVc629ne3i5Jamlp8XxNuUBpCAAAAAAAAAAZ19bWprPPPlvz58/XrFmztH37dq1Zs0Zr1qxRa2trZL6TTjpJ06dPjzyeOHGiHn/8cT3xxBPq3bt3ZJmtW7dKkkaPHq1Ro0Zp7Nixevnll7V8+XK9/fbbuvnmmzV//vzA7SwqKtLkyZN1xx136LnnntPixYs1btw4DRo0SGPHjpUkzZs3T9OnT9eiRYsiyebzzz9fe++9d6A0cDaRCAYAAAAAAACQcatWrdJzzz0nSZ1KLLz22ms64YQTJEkff/yxNmzYEHnO1BA2zxuPPvqoxo8fr6KiIr344ou6+eabdckll2j9+vUaOHCgjjvuuE43fPPrhhtu0JYtW3T55Zerrq5OxxxzjF566aVICYeePXvqqaee0ve//31t2bJFNTU1OuWUU/Td735XpaWlSW0z04o6glQvBgAAAAAAAAAUHEpDAAAAAAAAAEDIMRAMAAAAAAAAACHHQDAAAAAAAAAAhBwDwQAAAAAAAAAQcgwEAwAAAAAAAEDIMRAMAAAAAAAAACHHQDAAAAAAAAAAhBwDwQAAAAAAAAAQcgwEAwAAAAAAAEDIMRAMAAAAAAAAACHHQDAAAAAAAAAAhBwDwQAAAAAAAAAQcgwEAwAAAAAAAEDIMRAMAAAAAAAAACHHQDAAAAAAAAAAhBwDwQAAAAAAAAAQcgwEAwAAAAAAAEDIMRAMAAAAAAAAACHHQDAAAAAAAAAAhNz/A4Km1uGYsZPDAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1390x320 with 8 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "所有图像已成功生成并保存！\n"]}], "source": ["plot_img = nib.load(field_fmap_dir)\n", "\n", "# 2. 绘制并保存 X 轴方向的多个切片\n", "# display_mode='x' 表示沿着x轴切片\n", "# cut_coords=x_slices 指定切片的具体坐标\n", "# bg_img=None 表示不使用额外的背景图，直接在原图上绘制\n", "# draw_cross=False 表示不显示十字准星\n", "display_x = plot_anat(\n", "    plot_img,\n", "    display_mode='x',\n", "    cut_coords=x_slices,\n", "    title='X Slices',\n", "    draw_cross=False,\n", "    annotate=True,\n", "    bg_img=None,  # 明确指定不需要背景图\n", "    black_bg=False,\n", ")\n", "# 保存图像\n", "display_x.savefig(os.path.join(field_QC_fig_dir, \"field_fmap_x_slices.png\"), dpi=500)\n", "display_x.close()  # 保存后立即关闭\n", "print(\"已保存 fmap x_slices.png 并关闭图形。\")\n", "\n", "# 4. 绘制并保存 Y 轴方向的多个切片\n", "display_y = plot_anat(\n", "    plot_img,\n", "    display_mode='y',\n", "    cut_coords=y_slices,\n", "    title='Y Slices',\n", "    draw_cross=False,\n", "    annotate=True,\n", "    bg_img=None,\n", "    black_bg=False\n", ")\n", "display_y.savefig(os.path.join(field_QC_fig_dir, \"field_fmap_y_slices.png\"), dpi=500)\n", "display_y.close()  # 保存后立即关闭\n", "print(\"已保存 fmap y_slices.png 并关闭图形。\")\n", "\n", "\n", "# 5. 绘制并保存 Z 轴方向的多个切片\n", "display_z = plot_anat(\n", "    plot_img,\n", "    display_mode='z',\n", "    cut_coords=z_slices,\n", "    title='Z Slices',\n", "    draw_cross=False,\n", "    annotate=True,\n", "    bg_img=None,\n", "    black_bg=False\n", ")\n", "display_z.savefig(os.path.join(field_QC_fig_dir, \"field_fmap_z_slices.png\"), dpi=500)\n", "display_z.close()  # 保存后立即关闭\n", "print(\"已保存 fmap z_slices.png 并关闭图形。\")\n", "\n", "\n", "print(\"\\n所有图像已成功生成并保存！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Skull-strip and segment the anatomical T1 image\n", "\n", "This step provide gray matter, white matter, and csf maps."]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["## Skull-strip the T1 image\n", "cmd = f\"bet2 {anat_T1_dir} {anat_brain_dir}\"\n", "os.system(cmd)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/share/home/<USER>/data/our_nsd/bids/sub-04/ses-01/anat/anat_output/T1_brain.nii.gz\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "88b2bbc7a4d54928ab1e7a81fa958d3a", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=111, description='i', max=223), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6671dff5f33e4cadba1d6c69ce6eab98", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=134, description='j', max=269), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e674c1462c064012a163c0fa9fcbac67", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=159, description='k', max=319), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.show_slice_2(k)>"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the Nifti File\n", "print(anat_brain_dir)\n", "res_img = nib.load(anat_brain_dir)\n", "array = res_img.get_fdata()\n", "\n", "interact(show_slice_0, i=(0, array.shape[0]-1))\n", "interact(show_slice_1, j=(0, array.shape[1]-1))\n", "interact(show_slice_2, k=(0, array.shape[2]-1))"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["## Segment the T1 brain into gm, wm, and csf\n", "cmd = f\"fast -B -I 10 -l 10 {anat_brain_dir}\"\n", "os.system(cmd)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "11291cc51f644134967c3a8e4e0b712f", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=111, description='i', max=223), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "386b88b0d2c74ed7aa1d519cf4a0632a", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=134, description='j', max=269), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "32f56aad3800479b9b724681c0bdb982", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=159, description='k', max=319), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.show_slice_2(k)>"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the white-matter map Nifti File\n", "res_img = nib.load(anat_pve2_dir)\n", "array = res_img.get_fdata()\n", "\n", "interact(show_slice_0, i=(0, array.shape[0]-1))\n", "interact(show_slice_1, j=(0, array.shape[1]-1))\n", "interact(show_slice_2, k=(0, array.shape[2]-1))"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["## Create a binary white-matter map\n", "cmd = f\"fslmaths {anat_pve2_dir} -thr 0.5 -bin {anat_wmseg_dir}\"\n", "os.system(cmd)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "817949aef7f74c999e27bd7c87af8f09", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=111, description='i', max=223), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c64af2e7f52144daae45814bab26062b", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=134, description='j', max=269), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "84a4013404e741a2879e5a38dfac1df4", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=159, description='k', max=319), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.show_slice_2(k)>"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the Nifti File\n", "res_img = nib.load(anat_wmseg_dir)\n", "array = res_img.get_fdata()\n", "\n", "interact(show_slice_0, i=(0, array.shape[0]-1))\n", "interact(show_slice_1, j=(0, array.shape[1]-1))\n", "interact(show_slice_2, k=(0, array.shape[2]-1))"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["## Create a binary gray-matter map\n", "cmd = f\"fslmaths {anat_pve1_dir} -thr 0.4 -bin {anat_gm_mask_dir}\"\n", "os.system(cmd)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c7207aba07a14f69a23a3067ce68de31", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=111, description='i', max=223), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c16c150887134fe6a8a726a53a41c41d", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=134, description='j', max=269), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ffbf5f6d42404cd48382ba10fbd1a557", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=159, description='k', max=319), Output()), _dom_classes=('widget-interac…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<function __main__.show_slice_2(k)>"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the Nifti File\n", "res_img = nib.load(anat_gm_mask_dir)\n", "array = res_img.get_fdata()\n", "\n", "interact(show_slice_0, i=(0, array.shape[0]-1))\n", "interact(show_slice_1, j=(0, array.shape[1]-1))\n", "interact(show_slice_2, k=(0, array.shape[2]-1))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. recon"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. run preprocessing of func nii"]}], "metadata": {"kernelspec": {"display_name": "fmri", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}